# Web3Tools

Web3 开发工具库，集成区块链、社交媒体和通用工具等功能。

## 开发环境

### 依赖要求

- Node.js >= 14
- npm >= 6
- yarn >= 1.22.0

### 主要依赖

- ethers: ^6.13.4 (以太坊开发工具包)

### 开发依赖

- eslint 及相关插件
- prettier
- typescript 工具

### 安装步骤

git clone https://github.com/huhood/web3_tools/
cd web3-tools

### 安装依赖

yarn install

### 检查代码

yarn lint

### 自动修复格式

yarn lint:fix

## 使用说明

### 执行任务

1. 铭文和 NFT 铸造

```bash
node example/evm/task_tx.js 1-10
```

2. mint 铭文

```bash
# 铭文mint, 默认mint 1个
node example/evm/task_mint_insc.js 1-10

# 铭文mint, 指定mint 数量
node example/evm/task_mint_insc.js 1-10 10
```

3. mint NFT

```bash
# mint NFT, 默认mint 1个
node example/evm/task_mint_nft.js 1-10

# mint NFT, 指定mint 数量
node example/evm/task_mint_nft.js 1-10 10
```

4. 查询余额

```bash
# 查询所有链的余额
node example/evm/balance.js 1-10

# 查询eth主网余额
node example/evm/balance.js 1-10 eth

# 查询sepolia测试网余额
node example/evm/balance.js 1-10 sepolia

# 查询monad测试网余额
node example/evm/balance.js 1-10 monad

# 查询base测试网余额
node example/evm/balance.js 1-10 base

# 查询galxe主网 G 余额
node example/evm/balance.js 1-10 galxe


```
