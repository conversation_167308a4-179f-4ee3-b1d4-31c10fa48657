import xlsx from 'xlsx';
import fs from 'fs/promises';
import { existsSync } from 'fs';
import logger from './logger.js';


export function readExcelData(filePath, columnMapping, requiredFields = []) {
  try {
    if (!existsSync(filePath)) throw new Error(`文件不存在: ${filePath}`);
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    return xlsx.utils.sheet_to_json(sheet)
      .map(row => {
        const mappedData = {};
        Object.entries(columnMapping).forEach(([key, excelHeader]) => {
          mappedData[key] = row[excelHeader]?.trim?.() || row[excelHeader];
        });
        return mappedData;
      })
      .filter(item => requiredFields.every(field => item[field]));
  } catch (error) {
    logger.error(`读取Excel失败: ${error.message}`);
    throw error;
  }
}



/**
 * 写入Excel数据
 * @param {string} filePath - Excel文件路径
 * @param {Array} data - 要写入的数据
 * @param {Object} columnMapping - 列映射
 * @param {boolean} append - 是否追加数据，默认false（覆盖）
 */
export function writeExcelData(filePath, data, columnMapping, append = false) {
  let workbook;
  let existingData = [];

  // 检查文件是否存在
  const fileExists = existsSync(filePath);

  if (fileExists) {
      // 如果文件存在，读取工作簿
      workbook = xlsx.readFile(filePath);
      if (append) {
          // 如果需要追加数据，读取现有数据
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          existingData = xlsx.utils.sheet_to_json(firstSheet);
          // 合并现有数据和新数据
          data = [...existingData, ...data];
      }
  } else {
      // 如果文件不存在，创建新的工作簿
      workbook = xlsx.utils.book_new();
  }

  // 创建新的工作表数据
  const sheet = xlsx.utils.json_to_sheet(data, {
      header: Object.keys(columnMapping)
  });

  if (!fileExists) {
      // 只在文件不存在时添加新工作表
      xlsx.utils.book_append_sheet(workbook, sheet, 'Sheet1');
  } else {
      // 文件存在时直接更新第一个工作表
      workbook.Sheets[workbook.SheetNames[0]] = sheet;
  }

  // 写入文件
  xlsx.writeFile(workbook, filePath);
}


/**
 * 更新Excel文件中的数据
 * @param {string} filePath - Excel文件路径
 * @param {Object} query - 查询条件，如 {id: '001', name: '张三'}
 * @param {Object} updateFields - 要更新的字段，如 {age: 30, salary: 12000}
 * @returns {boolean} 更新是否成功
 */
export function updateExcelData(filePath, query, updateFields) {
  try {
    // 检查文件是否存在
    if (!existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    // 验证参数
    if (!query || typeof query !== 'object' || Object.keys(query).length === 0) {
      throw new Error('查询条件不能为空');
    }
    if (!updateFields || typeof updateFields !== 'object' || Object.keys(updateFields).length === 0) {
      throw new Error('更新字段不能为空');
    }

    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = xlsx.utils.sheet_to_json(sheet);

    let updateCount = 0;
    const updatedData = data.map(row => {
      const isMatch = Object.entries(query).every(([key, value]) =>
        row[key] === value
      );

      if (isMatch) {
        updateCount++;
        // 只更新指定的字段，保留其他字段不变
        const updatedRow = { ...row };
        Object.entries(updateFields).forEach(([key, value]) => {
          updatedRow[key] = value;
        });
        return updatedRow;
      }
      return row;
    });

    // 只有在实际有更新时才写入文件
    if (updateCount > 0) {
      const newSheet = xlsx.utils.json_to_sheet(updatedData);
      workbook.Sheets[workbook.SheetNames[0]] = newSheet;
      xlsx.writeFile(workbook, filePath);
      logger.info(`Excel文件更新成功: ${filePath}, 更新了 ${updateCount} 条记录`);
      return updateCount;
    } else {
      logger.info(`未找到匹配的记录: ${filePath}`);
      return 0;
    }
  } catch (error) {
    logger.error(`更新Excel数据失败: ${error.message}`);
    throw error;
  }
}


