import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import logger from './logger.js';

class SecureEncryption {
  constructor() {
    this.project_home = process.cwd();
    this.venvPath = path.join(this.project_home, '.venv'); // 虚拟环境路径
    this.pythonPath = path.resolve(this.project_home, 'base/py/secure_encryption.py');
  }

  getPythonPath() {
    const venvPath = process.platform === 'win32'
      ? path.join(this.project_home, '.venv', 'Scripts', 'python.exe')
      : path.join(this.project_home, '.venv', 'bin', 'python3');
    return venvPath;
  };

  activateVirtualEnv() {
    // 检查虚拟环境目录是否存在
    if (!fs.existsSync(this.venvPath)) {
      logger.warn('虚拟环境未安装。请确保已创建虚拟环境并安装所需的依赖项。');
      return false;
    }

    // 激活虚拟环境
    if (process.platform !== 'win32') {
      execSync('source .venv/bin/activate', {
        cwd: this.project_home,
        shell: '/bin/bash'
      });
    } else {
      execSync('.venv\\Scripts\\activate', {
        cwd: this.project_home,
        shell: 'cmd.exe'
      });
    }

    return true;
  }

  async callPython(action, data, password = '') {

    // 激活虚拟环境
    const result = this.activateVirtualEnv();
    if (!result) {
      return;
    }

    return new Promise((resolve, reject) => {
      const pythonProcess = spawn(this.getPythonPath(), [this.pythonPath, action, data, password]);

      let output = '';
      pythonProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        reject(`Python Error: ${data}`);
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          reject(`Python process exited with code ${code}`);
        }
      });
    });
  }

  async encrypt(data, password = '') {
    try {
      return await this.callPython('encrypt', data, password);
    } catch (error) {
      console.error('Error encrypting data:', error);
      return null;
    }
  }

  async decrypt(encryptedData, password = '') {
    try {
      return await this.callPython('decrypt', encryptedData, password);
    } catch (error) {
      console.error('Error decrypting data:', error);
      return null;
    }
  }

  async setPassword(password) {
    try {
      return await this.callPython('set_password', '', password);
    } catch (error) {
      console.error('Error setting password:', error);
      return null;
    }
  }

  async isEncrypted(data) {
    try {
      const result = await this.callPython('is_encrypted', data);
      return result === '1';
    } catch (error) {
      console.error('Error checking if data is encrypted:', error);
      return false;
    }
  }
}

export default SecureEncryption;
