import axios from 'axios';
import fs from 'fs';
import { HttpsProxyAgent } from 'https-proxy-agent';
import logger from './logger.js';
import { resolveFromModule } from './path.js';
import TOML from '@iarna/toml';

const configPath = resolveFromModule(import.meta.url, '../../config/config.toml');

logger.info(`configPath: ${configPath}`);
const config = TOML.parse(fs.readFileSync(configPath, 'utf-8'))

const PROXY_URL = config.PROXY_URL;
logger.info(`PROXY_URL: ${PROXY_URL}`);

/**
 * 代理助手类
 */
class ProxyHelper {
    constructor(proxyUrl) {
        this.proxyUrl = proxyUrl;
    }

    /**
     * 格式化代理地址
     * @param {string} proxyUrl - 原始代理地址
     * @returns {string} 格式化后的代理地址
     */
    _formatProxyUrl(proxyUrl) {
        try {
            if (!proxyUrl.startsWith('http://') &&
                !proxyUrl.startsWith('https://') &&
                !proxyUrl.startsWith('socks5://')) {
                return `http://${proxyUrl}`;
            }
            return proxyUrl;
        } catch (error) {
            logger.error(`代理地址格式化失败: ${error.message}`);
            return proxyUrl;
        }
    }

    /**
     * 获取代理配置
     * @returns {Object|null} 代理配置对象
     */
    getProxyAgent() {
        try {
            const formattedProxy = this._formatProxyUrl(this.proxyUrl);
            return new HttpsProxyAgent(formattedProxy);
        } catch (error) {
            logger.error(`代理设置失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 获取可用代理, 如果传入的代理不可用, 则返回默认代理
     * @returns {Object|null} 代理配置对象
     */
    async getAvailableProxy() {
        const isValid = await this.verify();
        if (isValid) {
            return this.getProxyAgent();
        }
        return new HttpsProxyAgent(PROXY_URL);
    }

    /**
     * 验证当前IP地址
     * @returns {Promise<boolean>} 验证是否成功
     */
    async verify() {
        try {
            const httpsAgent = await this.getProxyAgent();
            const response = await axios.get('https://myip.ipip.net', {
                httpsAgent,
                timeout: 10000,
                validateStatus: null
            });

            logger.info(`验证成功, IP信息: ${response.data}`);
            return true;
        } catch (error) {
            logger.error(`IP验证失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取真实IP
     * @returns {Promise<string>} 真实IP
     */
    async getRealIp() {
        try {
            const checkUrl = 'https://myip.ipip.net/json';
            const httpsAgent = this.getProxyAgent();

            const response = await axios.get(checkUrl, {
                httpsAgent,
                timeout: 10000,
                validateStatus: null
            });

            return response.data.data.ip;
        } catch (error) {
            logger.error(`获取真实IP失败: ${error.message}`);
            return '';
        }
    }
}

export default ProxyHelper;
