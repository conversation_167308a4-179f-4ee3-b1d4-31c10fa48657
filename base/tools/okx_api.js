import https from "https";
import crypto from "crypto";
import querystring from "querystring";

export default class OKXApi {
  constructor(api_key, secret_key, passphrase, project) {
    if (!api_key || !secret_key || !passphrase) {
      throw new Error("API 凭证不能为空");
    }
    // 定义 API 凭证和项目 ID
    this.api_config = {
      api_key: api_key,
      secret_key: secret_key,
      passphrase: passphrase,
      project: project,
    };
  }

  preHash(timestamp, method, request_path, params) {
    // 根据字符串和参数创建预签名
    let query_string = "";
    if (method === "GET" && params) {
      query_string = "?" + querystring.stringify(params);
    }
    if (method === "POST" && params) {
      query_string = JSON.stringify(params);
    }
    return timestamp + method + request_path + query_string;
  }

  sign(message, secret_key) {
    // 使用 HMAC-SHA256 对预签名字符串进行签名
    const hmac = crypto.createHmac("sha256", secret_key);
    hmac.update(message);
    return hmac.digest("base64");
  }

  createSignature(method, request_path, params) {
    // 获取 ISO 8601 格式时间戳
    const timestamp = new Date().toISOString().slice(0, -5) + "Z";
    // 生成签名
    const message = this.preHash(timestamp, method, request_path, params);
    const signature = this.sign(message, this.api_config["secret_key"]);
    return { signature, timestamp };
  }

  async sendGetRequest(request_path, params) {
    return new Promise((resolve, reject) => {
      // 生成签名
      const { signature, timestamp } = this.createSignature("GET", request_path, params);

      // 生成请求头
      const headers = {
        "OK-ACCESS-KEY": this.api_config["api_key"],
        "OK-ACCESS-SIGN": signature,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": this.api_config["passphrase"],
        "OK-ACCESS-PROJECT": this.api_config["project"],
      };

      const options = {
        hostname: "web3.okx.com",
        path: request_path + (params ? `?${querystring.stringify(params)}` : ""),
        method: "GET",
        headers: headers,
      };

      const req = https.request(options, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          try {
            const parsedData = JSON.parse(data);
            resolve(parsedData);
          } catch (err) {
            reject(new Error("解析响应数据失败: " + err.message));
          }
        });
      });

      req.on("error", (err) => {
        reject(new Error("请求失败: " + err.message));
      });

      req.end();
    });
  }

  async sendPostRequest(request_path, params) {
    return new Promise((resolve, reject) => {
      // 生成签名
      const { signature, timestamp } = createSignature("POST", request_path, params);

      // 生成请求头
      const headers = {
        "OK-ACCESS-KEY": this.api_config["api_key"],
        "OK-ACCESS-SIGN": signature,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": this.api_config["passphrase"],
        "OK-ACCESS-PROJECT": this.api_config["project"],
        "Content-Type": "application/json",
      };

      const options = {
        hostname: "www.okx.com",
        path: request_path,
        method: "POST",
        headers: headers,
      };

      const req = https.request(options, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          try {
            const parsedData = JSON.parse(data);
            resolve(parsedData);
          } catch (err) {
            reject(new Error("解析响应数据失败: " + err.message));
          }
        });
      });

      req.on("error", (err) => {
        reject(new Error("请求失败: " + err.message));
      });

      if (params) {
        req.write(JSON.stringify(params));
      }

      req.end();
    });
  }

  async getNFT(ownerAddress, chain = "eth") {
    return this.sendGetRequest("/api/v5/mktplace/nft/owner/asset-list", {
      chain,
      ownerAddress,
    });
  }

  async getNFTCount(ownerAddress, chain = "eth") {
    return this.getNFT(ownerAddress, chain).then((res) => {
      const data = res.data?.data;
      if (!data) {
        return 0;
      }
      let count = 0;
      data.forEach((item) => {
        count += parseInt(item.amount);
      });
      return count;
    });
  }

  async getTotalBalanceUSD(address, chains = "1") {
    try {
      // 调用 sendGetRequest 方法获取代币余额
      const response = await this.sendGetRequest("/api/v5/wallet/asset/all-token-balances-by-address", {
        'chains': chains,
        'address': address,
        "filter": 1
      });

      // 检查响应数据
      if (!response.data || !response.data[0] || !response.data[0].tokenAssets) {
        throw new Error("无法获取代币余额数据");
      }

      // 提取代币余额列表
      const tokenBalances = response.data[0].tokenAssets;

      // 计算总金额（以美元计价）
      let totalUSD = 0;
      tokenBalances.forEach((token) => {
        const balance = parseFloat(token.balance);
        const tokenPrice = parseFloat(token.tokenPrice);
        if (!isNaN(balance) && !isNaN(tokenPrice) && tokenPrice > 0) {
          totalUSD += balance * tokenPrice;
        }
      });

      // 返回四舍五入到两位小数的总金额
      return parseFloat(totalUSD.toFixed(2));
    } catch (error) {
      throw new Error(`获取总金额失败: ${error.message}`);
    }
  }
}
