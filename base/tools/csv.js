import fs from 'fs/promises';
import { createReadStream, createWriteStream } from 'fs';
import csv from 'csv-parser';
import { stringify } from 'csv-stringify/sync';
import path from 'path';

class CSV {
  /**
   * 读取 CSV 文件
   * @param {string} filePath - CSV 文件路径
   * @param {Object} options - 可选配置项
   * @returns {Promise<Array>} - 返回解析后的数据数组
   */
  static async read(filePath, options = {}) {
    const results = [];

    return new Promise((resolve, reject) => {
      createReadStream(filePath)
        .pipe(csv(options))
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  /**
   * 写入数据到 CSV 文件
   * @param {string} filePath - 目标文件路径
   * @param {Array} data - 要写入的数据数组
   * @param {Object} options - 可选配置项
   * @returns {Promise<void>}
   */
  static async write(filePath, data, options = {}) {
    const csvString = stringify(data, {
      header: true,
      ...options
    });

    await fs.writeFile(filePath, csvString);
  }

  /**
   * 追加数据到现有的 CSV 文件
   * @param {string} filePath - CSV 文件路径
   * @param {Array} data - 要追加的数据数组
   * @param {Object} options - 可选配置项
   * @returns {Promise<void>}
   */
  static async append(filePath, data, options = {}) {
    const csvString = stringify(data, {
      header: false,
      ...options
    });

    await fs.appendFile(filePath, csvString);
  }

  /**
   * 将数据转换为 CSV 字符串
   * @param {Array} data - 要转换的数据数组
   * @param {Object} options - 可选配置项
   * @returns {string} - CSV 格式的字符串
   */
  static toCSVString(data, options = {}) {
    return stringify(data, {
      header: true,
      ...options
    });
  }

  /**
   * 检查文件是否存在
   * @param {string} filePath - 文件路径
   * @returns {Promise<boolean>}
   */
  static async exists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 创建目录（如果不存在）
   * @param {string} filePath - 文件路径
   */
  static async ensureDir(filePath) {
    const dir = path.dirname(filePath);
    await fs.mkdir(dir, { recursive: true });
  }

  /**
   * 根据指定字段更新数据
   * @param {string} filePath - CSV 文件路径
   * @param {string} keyField - 用于匹配的字段名
   * @param {string} keyValue - 要匹配的字段值
   * @param {Object} newData - 要更新的新数据
   * @param {Object} options - 可选配置项
   * @returns {Promise<{success: boolean, message: string, updatedCount: number}>}
   */
  static async updateByField(filePath, keyField, keyValue, newData, options = {}) {
    try {
      // 检查文件是否存在
      if (!await this.exists(filePath)) {
        return {
          success: false,
          message: '文件不存在',
          updatedCount: 0
        };
      }

      // 读取所有数据
      const allData = await this.read(filePath, options);
      let updatedCount = 0;

      // 更新匹配的数据
      const updatedData = allData.map(row => {
        if (row[keyField] === keyValue) {
          updatedCount++;
          return { ...row, ...newData };
        }
        return row;
      });

      // 如果没有找到匹配的数据
      if (updatedCount === 0) {
        return {
          success: false,
          message: '未找到匹配的数据',
          updatedCount: 0
        };
      }

      // 写回文件
      await this.write(filePath, updatedData, options);

      return {
        success: true,
        message: `成功更新 ${updatedCount} 条数据`,
        updatedCount
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        updatedCount: 0
      };
    }
  }

  /**
   * 批量更新数据
   * @param {string} filePath - CSV 文件路径
   * @param {string} keyField - 用于匹配的字段名
   * @param {Array<{keyValue: any, newData: Object}>} updates - 批量更新数据数组
   * @param {Object} options - 可选配置项
   * @returns {Promise<{success: boolean, message: string, updatedCount: number}>}
   */
  static async batchUpdate(filePath, keyField, updates, options = {}) {
    try {
      if (!await this.exists(filePath)) {
        return {
          success: false,
          message: '文件不存在',
          updatedCount: 0
        };
      }

      const allData = await this.read(filePath, options);
      let updatedCount = 0;

      // 创建更新映射
      const updateMap = new Map(
        updates.map(update => [update.keyValue, update.newData])
      );

      // 更新匹配的数据
      const updatedData = allData.map(row => {
        const updateData = updateMap.get(row[keyField]);
        if (updateData) {
          updatedCount++;
          return { ...row, ...updateData };
        }
        return row;
      });

      if (updatedCount === 0) {
        return {
          success: false,
          message: '未找到匹配的数据',
          updatedCount: 0
        };
      }

      await this.write(filePath, updatedData, options);

      return {
        success: true,
        message: `成功更新 ${updatedCount} 条数据`,
        updatedCount
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        updatedCount: 0
      };
    }
  }
}

export default CSV;
