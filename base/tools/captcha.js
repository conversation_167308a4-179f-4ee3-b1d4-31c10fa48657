import axios from "axios";
import { resolveFromModule } from "./path.js";
import { parseTOMLConfig } from "./common.js";

const configPath = resolveFromModule(import.meta.url, "../../config/config.toml");
const config = parseTOMLConfig(configPath);

// 验证码服务配置
export const CAPTCHA_SERVICES = {
  YESCAPTCHA: {
    createTaskUrl: "https://api.yescaptcha.com/createTask",
    getResultUrl: "https://api.yescaptcha.com/getTaskResult",
    clientKey: config.YESCAPTCHA_KEY,
  },
  CAPSOLVER: {
    createTaskUrl: "https://api.capsolver.com/createTask",
    getResultUrl: "https://api.capsolver.com/getTaskResult",
    clientKey: config.CAPSOLVER_KEY,
  },
  FASTCAPTCHA: {
    createTaskUrl: "https://api.fastcaptcha.net/createTask",
    getResultUrl: "https://api.fastcaptcha.net/getTaskResult",
    clientKey: config.FASTCAPTCHA_KEY,
  },
};

// 创建验证码任务
export async function createTask(websiteUrl, websiteKey, taskType, pageAction, service) {
  if (!service) {
    throw new Error("请指定验证码平台");
  }

  const serviceConfig = CAPTCHA_SERVICES[service.toUpperCase()];
  if (!serviceConfig) {
    throw new Error(`不支持的验证码平台: ${service}`);
  }

  if (!serviceConfig.clientKey) {
    throw new Error(`验证码平台 ${service} 未配置密钥`);
  }

  const params = {
    task: {
      websiteURL: websiteUrl,
      websiteKey: websiteKey,
      type: taskType,
    },
  };

  // 根据不同服务处理参数
  switch (service.toUpperCase()) {
    case "CAPSOLVER":
      params.clientKey = serviceConfig.clientKey;
      params.appID = serviceConfig.clientKey;
      if (pageAction) {
        params.task.pageAction = pageAction;
      }
      break;
    case "FASTCAPTCHA":
      params.apitKey = serviceConfig.clientKey;
      params.developerKey = serviceConfig.clientKey;
      params.softID = serviceConfig.clientKey;
      if (pageAction) {
        params.task.pageAction = pageAction;
      }
      break;
    case "YESCAPTCHA":
      params.clientKey = serviceConfig.clientKey;
      params.softID = serviceConfig.clientKey;
      break;
    default:
      throw new Error(`未知的验证码平台: ${service}`);
  }

  const response = await axios.post(serviceConfig.createTaskUrl, params);
  return { ...response.data, service };
}

// 获取验证码结果
export async function getTaskResult(taskId, service) {
  if (!service) {
    throw new Error("请指定验证码平台");
  }

  const serviceConfig = CAPTCHA_SERVICES[service.toUpperCase()];
  if (!serviceConfig) {
    throw new Error(`不支持的验证码平台: ${service}`);
  }

  const params = {
    clientKey: serviceConfig.clientKey,
    taskId: taskId,
  };

  const response = await axios.post(serviceConfig.getResultUrl, params);
  const sleep = (minutes) => {
    const milliseconds = minutes * 60 * 1000;
    return new Promise((resolve) => setTimeout(resolve, milliseconds));
  };

  await sleep(0.2);
  if (response.data.status === "ready") {
    return { ...response.data, service };
  } else if (response.data.status === "processing") {
    return await getTaskResult(taskId, service);
  }
}
