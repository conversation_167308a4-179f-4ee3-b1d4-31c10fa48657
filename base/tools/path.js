import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';


/**
 * 从当前模块路径解析目标文件的绝对路径
 * @param {string} importMetaUrl - import.meta.url
 * @param {string} targetPath - 相对路径
 * @returns {string} 解析后的绝对路径
 */
export function resolveFromModule(importMetaUrl, targetPath) {
  const __filename = fileURLToPath(importMetaUrl);
  const __dirname = dirname(__filename);
  return resolve(__dirname, targetPath);
}
