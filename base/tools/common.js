import fs from "fs";
import TOML from "@iarna/toml";
import logger from "./logger.js";

/**
 * 生成指定范围内的随机值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机值
 */
export function getRandomValue(min, max) {
  if (min > max) {
    throw new Error("最小值不能大于最大值");
  }
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 延迟函数（以秒为单位）
 * @param {*} seconds 延迟时间（秒）
 * @returns
 */
export function sleep(seconds) {
  return new Promise((resolve) => setTimeout(resolve, seconds * 1000));
}

/**
 * 随机延迟函数（以秒为单位）
 * @param {number} minSeconds 最小延迟时间（秒）
 * @param {number} maxSeconds 最大延迟时间（秒）
 * @returns {Promise} 延迟结束后 resolve 的 Promise
 */
export function randomSleep(minSeconds, maxSeconds) {
  // 参数校验
  if (typeof minSeconds !== "number" || typeof maxSeconds !== "number") {
    throw new Error("参数必须是数字");
  }
  if (minSeconds < 0 || maxSeconds < 0) {
    throw new Error("延迟时间不能为负数");
  }
  if (minSeconds > maxSeconds) {
    throw new Error("最小延迟时间不能大于最大延迟时间");
  }

  // 转换为毫秒并生成随机延迟时间
  const minMs = minSeconds * 1000;
  const maxMs = maxSeconds * 1000;

  const ms = getRandomValue(minMs, maxMs);
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

/**
 * 解析钱包索引范围
 * @param {string} indexStr 钱包索引字符串
 * @returns {Object} 包含类型和索引信息的对象
 */
export function parseIndexRange(indexStr) {
  if (!indexStr) return null;

  // 确保输入是字符串
  if (typeof indexStr === "number") {
    indexStr = indexStr.toString(); // 将数字转换为字符串
  }

  // 处理逗号分隔的情况 "1,2,3,4"
  if (indexStr.includes(",")) {
    const idxs = indexStr.split(",").map(String);
    let indices = [];
    for (let idx of idxs) {
      if (idx.includes("-")) {
        const [start, end] = idx.split("-").map(Number);
        const length = Math.abs(end - start) + 1;
        const step = start <= end ? 1 : -1;
        const rng = Array.from({ length }, (_, i) => start + i * step);
        indices.push(...rng);
      } else {
        indices.push(parseInt(idx));
      }
    }
    return {
      type: "list",
      indices: indices,
    };
  }

  // 处理范围的情况 "1-10"
  if (indexStr.includes("-")) {
    const [start, end] = indexStr.split("-").map(Number);
    return {
      type: "range",
      start,
      end,
    };
  }

  // 处理单个数字的情况 "1"
  const singleIndex = Number(indexStr);
  return {
    type: "single",
    index: singleIndex,
  };
}

/**
 * 解析 TOML 配置文件
 * @param {string} filePath 配置文件路径
 * @returns {Object} 解析后的配置对象
 */
export function parseTOMLConfig(filePath) {
  const config = TOML.parse(fs.readFileSync(filePath, "utf-8"));
  return config;
}

/**
 * 根据index获取对应的钱包数据
 * @param {*} index index
 * @param {*} walletList 所有的钱包数据
 * @returns
 */
export function filterWalletsByIndex(index, walletList) {
  // 根据 index 范围过滤钱包列表
  let indexRange = parseIndexRange(index);
  const filteredWallets = indexRange
    ? walletList.filter((w) => {
      const idx = Number(w.index);
      if (indexRange.type === "range") {
        return idx >= indexRange.start && idx <= indexRange.end;
      } else if (indexRange.type === "list") {
        return indexRange.indices.includes(idx);
      } else if (indexRange.type === "single") {
        return idx === indexRange.index;
      }
      return true;
    })
    : walletList;

  return filteredWallets;
}

/**
 * 通用的重试函数
 * @param {*} asyncFunc 需要重试的异步函数
 * @param {*} options 配置选项
 * @returns
 */
export async function retry(asyncFunc, maxRetries = 3, delay = 1000) {
  let attempts = 0;

  while (attempts < maxRetries) {
    try {
      // 调用异步函数并返回结果
      return await asyncFunc();
    } catch (error) {
      attempts++;
      if (attempts >= maxRetries) {
        throw new Error(`最大重试次数 ${maxRetries} 次已达到，最后错误: ${error.message}`);
      }
      logger.warn(`尝试 ${attempts} 失败，等待 ${delay} 毫秒后重试...`);
      await new Promise((resolve) => setTimeout(resolve, delay)); // 等待指定的延迟
    }
  }
}

/**
 * 生成指定范围内的随机值，如最小值0.001，最大值0.05，
 * 返回的值可能为：0.001, 0.002, 0.003, 0.004, 0.005, 0.01, 0.02, 0.03, 0.04, 0.05
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机值
 */
export function getHumanRandomFloat(min, max) {
  // 如果最小值等于最大值，直接返回该值
  if (min === max) {
    return min;
  }

  // 如果最小值大于最大值，交换它们
  if (min > max) {
    [min, max] = [max, min];
  }

  const possibleValues = [];

  // 获取最小值和最大值的小数位数
  const getDecimalPlaces = (num) => {
    const str = num.toString();
    const decimalPart = str.split('.')[1];
    return decimalPart ? decimalPart.length : 0;
  };

  const minDecimals = getDecimalPlaces(min);
  const maxDecimals = getDecimalPlaces(max);

  // 如果最小值和最大值的小数位数相同，只需要生成一次
  if (minDecimals === maxDecimals) {
    const multiplier = Math.pow(10, minDecimals);
    const start = Math.ceil(min * multiplier);
    const end = Math.floor(max * multiplier);

    for (let i = start; i <= end; i++) {
      possibleValues.push(i / multiplier);
    }
  } else {
    // 添加最小精度的值 (比如三位小数 0.001-0.009)
    const multiplier1 = Math.pow(10, minDecimals);
    for (let i = 1; i <= 9; i++) {
      const value = i / multiplier1;
      if (value >= min && value <= max) {
        possibleValues.push(value);
      }
    }

    // 添加最大精度的值 (0.01-0.05)
    const multiplier2 = Math.pow(10, maxDecimals);
    for (let i = 1; i <= max * multiplier2; i++) {
      const value = i / multiplier2;
      if (value >= 0.01 && value <= max) {
        possibleValues.push(value);
      }
    }
  }

  // 随机选择一个值
  const randomIndex = Math.floor(Math.random() * possibleValues.length);
  return possibleValues[randomIndex];
}

/**
 * 生成指定范围内的人性化随机整数值，如最小值100，最大值2000，
 * 返回的值可能为：100, 200, 300, 500, 1000, 1500, 2000等较为圆润的数字
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机整数值
 */
export function getHumanRandomInt(min, max) {
  // 如果最小值等于最大值，直接返回该值
  if (min === max) {
    return min;
  }

  // 如果最小值大于最大值，交换它们
  if (min > max) {
    [min, max] = [max, min];
  }

  const possibleValues = new Set();

  // 获取数字的位数
  const getDigits = (num) => Math.floor(Math.log10(Math.abs(num))) + 1;
  const maxDigits = getDigits(max);

  // 添加原始的最小值和最大值
  possibleValues.add(min);
  possibleValues.add(max);

  // 生成不同位数的圆润数字
  for (let digits = 1; digits <= maxDigits; digits++) {
    const multiplier = Math.pow(10, digits - 1);
    // 生成以1-9开头，后面跟若干个0的数字
    for (let i = 1; i <= 9; i++) {
      const value = i * multiplier;
      if (value >= min && value <= max) {
        possibleValues.add(value);
      }
    }

    // 生成以1-9开头，后面跟5的数字（如150, 250, 1500等）
    if (digits > 1) {
      for (let i = 1; i <= 9; i++) {
        const value = i * multiplier + (multiplier / 2);
        if (value >= min && value <= max) {
          possibleValues.add(value);
        }
      }
    }
  }

  // 转换为数组并排序
  const sortedValues = Array.from(possibleValues).sort((a, b) => a - b);

  // 随机选择一个值
  const randomIndex = Math.floor(Math.random() * sortedValues.length);
  return sortedValues[randomIndex];
}

/**
 * 根据余额生成人性化的随机数
 * @param {number} balance 余额
 * @param {number} minThreshold 最小阈值，默认为3
 * @param {number} maxThreshold 最大阈值，默认为20
 * @returns {number} 随机值
 */
export function getHumanNumber(balance, minThreshold = 3, maxThreshold = 20) {
  // 处理浮点数部分
  if (balance <= minThreshold) {
    return getHumanRandomFloat(0.001, 1);
  }

  // 处理整数部分
  const a = getHumanRandomFloat(0.001, 1);
  const maxAmount = Math.max(Math.min(Math.floor(balance * 0.2), maxThreshold), 1);
  const b = getHumanRandomInt(1, maxAmount);
  // 从a和b中随机选择一个
  // return Math.random() < 0.5 ? a : b;
  return b;
}
