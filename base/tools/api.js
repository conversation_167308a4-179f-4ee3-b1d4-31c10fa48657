import axios from "axios";

class Api {
  // 静态属性存储 axios 实例
  static instance = axios.create({
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  /**
   * 静态 GET 方法
   * @param {string} url - 请求地址
   * @param {Object} params - 查询参数
   * @param {Object} headers - 自定义头部
   * @returns {Promise}
   */
  static async get(url, params = {}, headers = {}, proxy = null) {
    try {
      return await this.instance.get(url, {
        params,
        headers: { ...this.instance.defaults.headers, ...headers },
        proxy,
      });
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 静态 POST 方法
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} headers - 自定义头部
   * @returns {Promise}
   */
  static async post(url, data = {}, headers = {}, proxy = null) {
    try {
      return await this.instance.post(url, data, {
        headers: { ...this.instance.defaults.headers, ...headers },
        proxy,
      });
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 静态错误处理方法
   * @param {Error} error - 错误对象
   */
  static handleError(error) {
    if (error.response) {
      console.error("Response Error:", error.response.data);
      console.error("Status:", error.response.status);
    } else if (error.request) {
      console.error("Request Error:", error.request);
    } else {
      console.error("Error:", error.message);
    }
  }
}

export default Api;
