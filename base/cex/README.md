# 币安CEX提现工具

这个工具允许您从币安交易所通过指定网络批量提取代币到多个钱包。

## 文件结构

```
base/cex/
├── index.js                    # 多交易所提现工具（推荐）
├── config.toml                 # 配置文件
├── config.toml.example         # 配置文件模板
├── wallets.csv                 # 钱包地址文件
├── withdraw_records.csv        # 提现记录文件（自动生成）
├── view_records.js             # 提现记录查看工具
├── README.md                   # 使用说明
└── binance/                    # 币安相关模块
    ├── client.js               # 币安API客户端
    ├── withdraw.js             # 提现功能模块
    └── index.js                # 币安查询工具
```

## 🚀 两种使用方式

本工具提供两种不同的脚本：

1. **`index.js`** - 多交易所提现工具（推荐）
2. **`binance/index.js`** - 币安查询工具

## 功能特性

- 支持多种网络（BSC、ETH、TRC等）
- 支持多种代币（BNB、USDT、BTC、ETH等）
- 批量提现到多个钱包
- 灵活的钱包范围选择
- 可配置的金额范围和间隔时间
- 自动重试机制
- 详细的日志输出
- **提现结果记录**：自动记录所有提现操作到CSV文件

## 使用方法

### 基本语法

```bash
node base/cex/index.js <exchange> <network> <coin> <wallet_range>
```

### 参数说明

- `exchange`: 交易所代码（目前支持: `bn` 或 `binance`）
- `network`: 网络名称（如: `bsc`, `eth`, `trc`）
- `coin`: 代币名称（如: `bnb`, `usdt`, `btc`）
- `wallet_range`: 钱包范围（如: `1`, `1,3,5`, `1-5`）

### 使用示例

```bash
# 从币安通过BSC链提取BNB到钱包1-5
node base/cex/index.js bn bsc bnb 1-5

# 从币安通过BSC链提取USDT到钱包1-5
node base/cex/index.js bn bsc usdt 1-5

# 提取到指定钱包（1,3,5）
node base/cex/index.js bn bsc bnb 1,3,5

# 提取到单个钱包
node base/cex/index.js bn bsc usdt 1
```

### 币安查询工具

```bash
# 显示账户概览
node base/cex/binance/index.js balance

# 查询USDT支持的网络
node base/cex/binance/index.js networks USDT

# 查询BNB支持的网络
node base/cex/binance/index.js networks BNB

# 查询提现历史（最近10条）
node base/cex/binance/index.js history

# 查询USDT的提现历史（最近10条）
node base/cex/binance/index.js history USDT

# 查询USDT的提现历史（最近20条）
node base/cex/binance/index.js history USDT 20
```

## 配置文件

在使用之前，您需要在 `base/cex/config.toml` 文件中配置相应的参数：

### 1. 币安API配置

```toml
# 币安API配置
BINANCE_API_KEY = "your_api_key_here"
BINANCE_API_SECRET = "your_api_secret_here"
BINANCE_BASE_URL = "https://api.binance.com"
```

### 2. 网络代币配置

对于每个网络和代币组合，您需要添加相应的配置节：

```toml
# 币安BSC网络BNB配置
[BINANCE_BSC_BNB]
min_amount = 0.01          # 最小提现金额
max_amount = 0.5           # 最大提现金额
decimal_places = 4         # 小数位数
interval_seconds = [5, 10] # 提现间隔时间范围（秒），随机选择5-10秒
network = "BEP20"          # BSC网络

# 币安BSC网络USDT配置
[BINANCE_BSC_USDT]
min_amount = 5.0           # 最小提现金额
max_amount = 50.0          # 最大提现金额
decimal_places = 2         # 小数位数
interval_seconds = [8, 15] # 提现间隔时间范围（秒），随机选择8-15秒
network = "BEP20"          # BSC网络
```

### 3. 钱包地址配置

在 `base/cex/wallets.csv` 文件中配置钱包地址：

```csv
index,address
1,0xYourWalletAddress1
2,0xYourWalletAddress2
3,0xYourWalletAddress3
4,0xYourWalletAddress4
5,0xYourWalletAddress5
```

### 4. 币安IP白名单设置
```bash
curl --proxy http://127.0.0.1:7890 https://ifconfig.me
```

## 配置键名规则

配置键名遵循以下格式：`BINANCE_<NETWORK>_<COIN>`

例如：
- `BINANCE_BSC_BNB` - BSC网络的BNB配置
- `BINANCE_BSC_USDT` - BSC网络的USDT配置
- `BINANCE_ETH_ETH` - ETH网络的ETH配置
- `BINANCE_TRC_USDT` - TRC网络的USDT配置

## 间隔时间配置

`interval_seconds` 字段必须使用数组格式：

```toml
interval_seconds = [5, 10]  # 随机选择5-10秒之间的间隔时间
```

系统会在指定的最小值和最大值之间随机选择间隔时间，让提现行为更加自然，避免被检测为机器人行为。

## 提现记录功能

系统会自动记录所有提现操作（成功和失败）到 `base/cex/withdraw_records.csv` 文件中。

### 记录字段

| 字段 | 说明 | 示例 |
|------|------|------|
| 交易所 | 交易所名称 | Binance |
| 提现地址 | 目标钱包地址 | 0x1234...5678 |
| 提现网络 | 使用的网络 | BEP20, TRC20, ERC20 |
| 提现币种 | 提现的代币 | BNB, USDT, BTC |
| 提现数量 | 提现金额 | 0.1234, 25.50 |
| 提现时间 | 操作时间 | 2025/08/03 17:30:15 |
| 提现结果 | 成功/失败 | 成功, 失败 |
| 交易ID | 成功时的交易哈希 | 0x9876...4321 |
| 错误信息 | 失败时的错误详情 | Insufficient balance |

### 记录文件示例

```csv
交易所,提现地址,提现网络,提现币种,提现数量,提现时间,提现结果,交易ID,错误信息
Binance,******************************************,BEP20,BNB,0.1234,2025/08/03 17:30:15,成功,******************************************,
Binance,TYourWalletAddress1Here,TRC20,USDT,25.50,2025/08/03 17:31:20,成功,******************************************,
Binance,******************************************,BEP20,USDT,15.75,2025/08/03 17:32:45,失败,,余额不足
```

### 记录文件位置

- **记录文件**：`base/cex/withdraw_records.csv`
- **示例文件**：`base/cex/withdraw_records_example.csv`

记录文件会在首次提现时自动创建，每次提现操作都会追加一条记录。

### 查看提现记录

使用 `view_records.js` 工具查看和分析提现记录：

```bash
# 查看最近10条记录
node base/cex/view_records.js

# 查看最近20条记录
node base/cex/view_records.js --limit=20

# 只查看BNB的记录
node base/cex/view_records.js --coin=BNB

# 只查看失败的记录
node base/cex/view_records.js --failed

# 显示统计信息
node base/cex/view_records.js --stats
```

统计信息包括：
- 总记录数和成功率
- 各币种的提现次数和成功率
- 各网络的使用次数
- 成功提现的总金额

## 错误处理

工具包含完善的错误处理机制：

1. **参数验证**: 检查命令行参数的有效性
2. **配置验证**: 验证配置文件和配置节的存在
3. **API错误处理**: 处理币安API相关错误
4. **网络错误处理**: 处理网络连接问题
5. **重试机制**: 失败时自动重试（最多3次）

## 日志输出

工具提供详细的日志输出，包括：

- 配置信息显示
- 提现进度跟踪
- 成功/失败状态
- 错误详情
- 最终统计结果

## 注意事项

1. **API密钥安全**: 请妥善保管您的币安API密钥
2. **网络费用**: 提现会产生网络手续费
3. **余额检查**: 确保账户有足够的余额进行提现
4. **地址验证**: 确保钱包地址正确，避免资产损失
5. **限制遵守**: 遵守币安的提现限制和频率限制

## 故障排除

### 常见错误

1. **配置文件未找到**
   - 确保 `base/cex/config.toml` 文件存在
   - 检查文件路径是否正确

2. **配置节未找到**
   - 确保配置了相应的网络代币参数
   - 检查配置键名格式是否正确

3. **API错误**
   - 检查API密钥是否正确
   - 确认API权限包含提现功能
   - 检查网络连接

4. **余额不足**
   - 确保账户有足够的余额
   - 考虑网络手续费

### 获取帮助

运行以下命令查看使用说明：

```bash
node base/cex/index.js
```

或者查看现有的 `base/cex/binance/task.js` 文件了解更多高级用法。
