import BinanceWithdraw from "./binance/withdraw.js";
import Logger from "../tools/logger.js";
import { parseTOMLConfig, getRandomValue, filterWalletsByIndex } from "../tools/common.js";
import { resolveFromModule } from "../tools/path.js";
import CSV from "../tools/csv.js";
import { existsSync } from "fs";

/**
 * 币安CEX提现工具主入口
 * 支持通过指定网络提取代币到多个钱包
 * 
 * 使用方法：
 * node base/cex/index.js <exchange> <network> <coin> <wallet_range>
 * 
 * 示例：
 * node base/cex/index.js bn bsc bnb 1-5     # 从币安通过BSC链提取BNB到钱包1-5
 * node base/cex/index.js bn bsc usdt 1-5    # 从币安通过BSC链提取USDT到钱包1-5
 */

/**
 * 解析命令行参数
 * @returns {Object} 解析后的参数对象
 */
function parseArguments() {
  const args = process.argv.slice(2);
  
  if (args.length < 4) {
    showUsage();
    process.exit(1);
  }

  const [exchange, network, coin, walletRange] = args;
  
  // 验证交易所参数
  if (exchange.toLowerCase() !== 'bn' && exchange.toLowerCase() !== 'binance') {
    Logger.error("目前只支持币安交易所 (bn/binance)");
    process.exit(1);
  }

  return {
    exchange: 'binance',
    network: network.toUpperCase(),
    coin: coin.toUpperCase(),
    walletRange
  };
}

/**
 * 构建配置键名
 * @param {string} exchange - 交易所名称
 * @param {string} network - 网络名称
 * @param {string} coin - 代币名称
 * @returns {string} 配置键名
 */
function buildConfigKey(exchange, network, coin) {
  return `${exchange.toUpperCase()}_${network}_${coin}`;
}

/**
 * 解析间隔时间配置
 * @param {Array} intervalConfig - 间隔时间配置数组 [min, max]
 * @returns {Object} 包含最小值和最大值的对象
 */
function parseIntervalConfig(intervalConfig) {
  if (!Array.isArray(intervalConfig)) {
    throw new Error("interval_seconds 必须是包含两个数字的数组格式，如: [8, 15]");
  }

  if (intervalConfig.length !== 2) {
    throw new Error("interval_seconds 数组必须包含两个元素 [min, max]");
  }

  const [min, max] = intervalConfig;
  if (typeof min !== 'number' || typeof max !== 'number') {
    throw new Error("interval_seconds 数组元素必须是数字");
  }

  if (min > max) {
    throw new Error("interval_seconds 最小值不能大于最大值");
  }

  return { min, max };
}

/**
 * 读取钱包文件
 * @param {string} walletFilePath - 钱包文件路径
 * @returns {Promise<Array>} 钱包列表
 */
async function loadWallets(walletFilePath) {
  if (!existsSync(walletFilePath)) {
    throw new Error(`钱包文件不存在: ${walletFilePath}`);
  }

  try {
    const wallets = await CSV.read(walletFilePath);
    if (!wallets || wallets.length === 0) {
      throw new Error("钱包文件为空或格式错误");
    }

    // 验证钱包文件格式
    const requiredFields = ['index', 'address'];
    const firstWallet = wallets[0];
    for (const field of requiredFields) {
      if (!(field in firstWallet)) {
        throw new Error(`钱包文件缺少必需字段: ${field}`);
      }
    }

    Logger.info(`成功加载 ${wallets.length} 个钱包地址`);
    return wallets;
  } catch (error) {
    throw new Error(`读取钱包文件失败: ${error.message}`);
  }
}

/**
 * 准备提现参数
 * @param {Object} coinConfig - 币种配置
 * @param {Object} intervalConfig - 间隔时间配置
 * @param {Array} wallets - 钱包列表
 * @param {string} coin - 币种
 * @param {string} walletRange - 钱包范围
 * @returns {Object} 提现参数
 */
function prepareWithdrawParams(coinConfig, intervalConfig, wallets, coin, walletRange) {
  // 过滤钱包（注意参数顺序）
  const filteredWallets = filterWalletsByIndex(walletRange, wallets);
  if (filteredWallets.length === 0) {
    throw new Error(`没有找到匹配的钱包: ${walletRange}`);
  }
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  Logger.info(`选择了 ${shuffledWallets.length} 个钱包进行提现`);

  return {
    coin: coin.toUpperCase(),
    network: coinConfig.network,
    wallets: shuffledWallets,
    minAmount: coinConfig.min_amount,
    maxAmount: coinConfig.max_amount,
    decimalPlaces: coinConfig.decimal_places,
    intervalConfig: intervalConfig,
    maxRetries: 3,
    continueOnError: true
  };
}

/**
 * 获取网络特定的币种配置
 * @param {Object} config - 完整配置对象
 * @param {string} configKey - 配置键名
 * @returns {Object} 币种配置
 */
function getNetworkCoinConfig(config, configKey) {
  const coinConfig = config[configKey];

  if (!coinConfig) {
    throw new Error(`配置文件中未找到 [${configKey}] 配置节，请检查 config.toml 文件`);
  }

  // 验证必需的配置项
  const requiredFields = ['min_amount', 'max_amount', 'decimal_places', 'interval_seconds', 'network'];
  for (const field of requiredFields) {
    if (coinConfig[field] === undefined) {
      throw new Error(`配置 [${configKey}] 中缺少必需字段: ${field}`);
    }
  }

  return coinConfig;
}

/**
 * 显示使用说明
 */
function showUsage() {
  console.log(`
币安CEX提现工具

使用方法:
  node base/cex/index.js <exchange> <network> <coin> <wallet_range>

参数说明:
  exchange     - 交易所代码 (目前支持: bn/binance)
  network      - 网络名称 (如: bsc, eth, trc)
  coin         - 代币名称 (如: bnb, usdt, btc)
  wallet_range - 钱包范围 (如: 1, 1,3,5, 1-5)

示例:
  node base/cex/index.js bn bsc bnb 1-5      # 从币安通过BSC链提取BNB到钱包1-5
  node base/cex/index.js bn bsc usdt 1-5     # 从币安通过BSC链提取USDT到钱包1-5
  node base/cex/index.js bn bsc bnb 1,3,5    # 从币安通过BSC链提取BNB到钱包1,3,5

配置文件:
  请确保在 base/cex/config.toml 中配置了相应的网络代币参数，如:
  [BINANCE_BSC_BNB]  - BSC网络BNB配置
  [BINANCE_BSC_USDT] - BSC网络USDT配置
`);
}

/**
 * 主函数
 */


async function main() {
  try {
    
    // 解析命令行参数
    const { exchange, network, coin, walletRange } = parseArguments();

    // 构建配置键名
    const configKey = buildConfigKey(exchange, network, coin);

    // 加载配置文件
    const configPath = resolveFromModule(import.meta.url, "./config.toml");
    const config = parseTOMLConfig(configPath);
    
    // 获取网络特定的币种配置
    const coinConfig = getNetworkCoinConfig(config, configKey);
    if (!coinConfig) {
      throw new Error(`请先在config.toml中配置好${configKey}的配置`);
    }

    // 解析间隔时间配置
    const intervalConfig = parseIntervalConfig(coinConfig.interval_seconds);

    Logger.info(`准备提现:`);
    Logger.info(`交易所: ${exchange}`);
    Logger.info(`网络: ${network}`);
    Logger.info(`代币: ${coin}`);
    Logger.info(`钱包范围: ${walletRange}`);
    Logger.info(`金额范围: ${coinConfig.min_amount} - ${coinConfig.max_amount} ${coin}`);
    Logger.info(`小数位数: ${coinConfig.decimal_places}`);
    Logger.info(`间隔时间: ${intervalConfig.min}-${intervalConfig.max}秒（随机）`);

    // 读取钱包文件
    const walletFilePath = resolveFromModule(import.meta.url, "./wallets.csv");
    const wallets = await loadWallets(walletFilePath);

    // 准备提现参数
    const withdrawParams = prepareWithdrawParams(coinConfig, intervalConfig, wallets, coin, walletRange);

    // 创建币安提现工具实例（只传递API配置）
    const withdrawTool = new BinanceWithdraw(config.BINANCE_API_KEY, config.BINANCE_API_SECRET, config.BINANCE_BASE_URL);

    Logger.info("====== 开始批量提现 ======");

    // 执行批量提现
    const result = await withdrawTool.batchWithdraw(withdrawParams);

    Logger.info("====== 执行统计 ======");
    Logger.info(`总钱包数: ${result.data.total}`);
    Logger.info(`已处理: ${result.data.processed}`);
    Logger.info(`成功: ${result.data.successful}`);
    Logger.info(`失败: ${result.data.failed}`);

    if (result.data.failed) {
      const failedWallets = [];
      result.data.results.forEach(res => {
        if (!res.success) {
          Logger.error(`[钱包${res.index}] 失败 [${res.code}]: ${res.error}`);
          failedWallets.push(res.index);
        }
      });
      Logger.error(`失败的钱包index: ${failedWallets.join(',')}`);
    }
  } catch (error) {
    Logger.error("程序执行失败:", error.message);
    
    // 如果是配置相关错误，显示更详细的帮助信息
    if (error.message.includes('配置文件') || error.message.includes('配置节')) {
      Logger.info("\n请检查以下事项:");
      Logger.info("1. base/cex/config.toml 文件是否存在");
      Logger.info("2. 是否配置了相应的网络代币参数");
      Logger.info("3. 配置格式是否正确");
      Logger.info("\n示例配置:");
      Logger.info("[BINANCE_BSC_BNB]");
      Logger.info("min_amount = 0.01");
      Logger.info("max_amount = 0.5");
      Logger.info("decimal_places = 4");
      Logger.info("interval_seconds = [5, 10]  # 随机间隔5-10秒");
      Logger.info("network = \"BEP20\"");
      Logger.info("\n钱包文件格式 (base/cex/wallets.csv):");
      Logger.info("index,address");
      Logger.info("1,0xYourWalletAddress1");
      Logger.info("2,0xYourWalletAddress2");
    }
    
    process.exit(1);
  }
}

// 运行主函数
main();
