import { Spot } from "@binance/connector";
import logger from "../../tools/logger.js";

/**
 * 币安API客户端封装
 * 基于官方SDK @binance/connector
 */
class BinanceClient {
  constructor(apiKey, apiSecret, options = {}) {
    this.apiKey = apiKey;
    this.apiSecret = apiSecret;

    // 默认配置
    const defaultOptions = {
      baseURL: "https://api.binance.com",
      timeout: 30000,
      ...options
    };

    // 添加代理配置（如果需要）
    if (process.env.USE_PROXY === 'true' || options.useProxy) {
      const proxyHost = process.env.PROXY_HOST || options.proxyHost || '127.0.0.1';
      const proxyPort = process.env.PROXY_PORT || options.proxyPort || 7890;
      const proxyProtocol = process.env.PROXY_PROTOCOL || options.proxyProtocol || 'http';

      defaultOptions.proxy = {
        protocol: proxyProtocol,
        host: proxyHost,
        port: parseInt(proxyPort),
      };

      // 如果有代理认证
      if (process.env.PROXY_USERNAME || options.proxyUsername) {
        defaultOptions.proxy.auth = {
          username: process.env.PROXY_USERNAME || options.proxyUsername,
          password: process.env.PROXY_PASSWORD || options.proxyPassword || ''
        };
      }

      logger.debug(`使用代理: ${proxyProtocol}://${proxyHost}:${proxyPort}`);
    } else {
      // 默认启用代理（用于测试）
      defaultOptions.proxy = {
        protocol: 'http',
        host: '127.0.0.1',
        port: 7890,
      };
      logger.debug("使用默认代理: http://127.0.0.1:7890");
    }

    // 创建币安客户端实例
    this.client = new Spot(apiKey, apiSecret, defaultOptions);
  }

  /**
   * 获取账户信息
   * @returns {Promise} 账户信息
   */
  async getAccountInfo() {
    try {
      const response = await this.client.account();
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 获取账户余额
   * @param {string} coin - 币种（可选）
   * @returns {Promise} 余额信息
   */
  async getBalance(coin = null) {
    try {
      const accountInfo = await this.getAccountInfo();
      
      if (coin) {
        const balance = accountInfo.balances.find(b => b.asset === coin.toUpperCase());
        return balance || { asset: coin.toUpperCase(), free: "0", locked: "0" };
      }
      
      return accountInfo.balances.filter(b => parseFloat(b.free) > 0 || parseFloat(b.locked) > 0);
    } catch (error) {
      logger.error("Failed to get balance:", error.message);
      throw error;
    }
  }

  /**
   * 获取所有币种信息
   * @returns {Promise} 币种信息
   */
  async getAllCoinsInfo() {
    try {
      // 使用正确的API方法名
      const response = await this.client.coinInfo();
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 获取币种网络信息
   * @param {string} coin - 币种
   * @returns {Promise} 网络信息
   */
  async getCoinNetworks(coin) {
    try {
      const coinsInfo = await this.getAllCoinsInfo();
      const coinInfo = coinsInfo.find(c => c.coin === coin.toUpperCase());
      
      if (!coinInfo) {
        throw new Error(`Coin ${coin} not found`);
      }
      
      return coinInfo.networkList.filter(network => network.withdrawEnable);
    } catch (error) {
      logger.error(`Failed to get networks for ${coin}:`, error.message);
      throw error;
    }
  }

  /**
   * 提现
   * @param {Object} params - 提现参数
   * @returns {Promise<Object>} 统一格式的提现结果
   * @returns {boolean} success - 是否成功
   * @returns {string} code - 状态码
   * @returns {Object|string} data - 业务数据
   * @returns {string} message - 消息
   */
  async withdraw(params) {
    try {
      const requiredParams = ["coin", "address", "amount", "network"];

      // 验证必需参数
      for (const param of requiredParams) {
        if (!params[param]) {
          return {
            success: false,
            code: "INVALID_PARAMS",
            data: null,
            message: `Missing required parameter: ${param}`
          };
        }
      }

      const response = await this.client.withdraw(
        params.coin,
        params.address,
        params.amount,
        {
          network: params.network
        }
      );

      return {
        success: true,
        code: "SUCCESS",
        data: {
          id: response.data.id,
          coin: params.coin,
          address: params.address,
          amount: params.amount,
          network: params.network,
          timestamp: Date.now()
        },
        message: "Withdrawal completed successfully"
      };

    } catch (error) {
      // 记录错误但不抛出
      this.handleError(error);

      // 根据错误类型返回不同的状态码
      let code = "FAIL";
      let message = error.message || "Unknown error";

      if (error.response && error.response.data) {
        const apiError = error.response.data;

        // 根据币安API错误码映射状态码
        switch (apiError.code) {
          case -1013: // 余额不足
          case -4019: // 币种不支持提现
            code = "INSUFFICIENT";
            message = apiError.msg || "Insufficient balance or withdrawal not supported";
            break;
          case -1021: // 时间戳错误
            code = "TIMESTAMP_ERROR";
            message = "Request timestamp is outside of the recvWindow";
            break;
          case -1022: // 签名错误
          case -2014: // API密钥格式无效
            code = "AUTH_ERROR";
            message = "Invalid API key or signature";
            break;
          case -1003: // 请求频率超限
            code = "RATE_LIMIT";
            message = "Request rate limit exceeded";
            break;
          default:
            code = "API_ERROR";
            message = apiError.msg || message;
        }
      } else if (error.request) {
        code = "NETWORK_ERROR";
        message = "Network connection failed";
      }

      return {
        success: false,
        code: code,
        data: {
          coin: params.coin,
          address: params.address,
          amount: params.amount,
          network: params.network,
          error: error.message,
          timestamp: Date.now()
        },
        message: message
      };
    }
  }

  /**
   * 获取提现历史
   * @param {Object} params - 查询参数
   * @returns {Promise} 提现历史
   */
  async getWithdrawHistory(params = {}) {
    try {
      const response = await this.client.withdrawHistory(params);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 获取充值历史
   * @param {Object} params - 查询参数
   * @returns {Promise} 充值历史
   */
  async getDepositHistory(params = {}) {
    try {
      const response = await this.client.depositHistory(params);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 获取充值地址
   * @param {string} coin - 币种
   * @param {string} network - 网络
   * @returns {Promise} 充值地址信息
   */
  async getDepositAddress(coin, network = null) {
    try {
      const params = { coin };
      if (network) {
        params.network = network;
      }
      const response = await this.client.depositAddress(coin, params);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 获取系统状态
   * @returns {Promise} 系统状态
   */
  async getSystemStatus() {
    try {
      const response = await this.client.systemStatus();
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 错误处理
   * @param {Error} error - 错误对象
   */
  handleError(error) {
    if (error.response) {
      const { status, data } = error.response;
      logger.error(`币安API错误 [HTTP ${status}]:`, data.msg || data.message || "未知错误");

      // 特定错误码处理
      if (data.code) {
        switch (data.code) {
          case -1021:
            logger.error("时间戳错误: 请求时间戳超出了接收窗口范围");
            logger.error("解决方案: 检查系统时间是否准确，或调整时间同步");
            break;
          case -1022:
            logger.error("签名错误: API签名无效");
            logger.error("解决方案: 检查API密钥和密钥是否正确");
            break;
          case -2010:
            logger.error("订单被拒绝");
            break;
          case -2014:
            logger.error("API密钥格式无效");
            logger.error("解决方案: 检查API密钥格式是否正确");
            break;
          case -4019:
            logger.error("该币种当前不支持提现");
            break;
          case -1003:
            logger.error("请求频率超限");
            logger.error("解决方案: 降低请求频率，稍后重试");
            break;
          case -1000:
            logger.error("未知错误，请联系币安客服");
            break;
          default:
            logger.error(`错误代码: ${data.code}`);
            logger.error("详细信息:", JSON.stringify(data, null, 2));
        }
      }
    } else if (error.request) {
      logger.error("网络连接错误:", error.message || "无法连接到币安服务器");
      logger.error("解决方案:");
      logger.error("1. 检查网络连接是否正常");
      logger.error("2. 检查防火墙设置");
      logger.error("3. 尝试使用VPN或更换网络");
      logger.error("4. 确认币安API服务是否正常运行");
    } else {
      logger.error("请求配置错误:", error.message || "未知错误");
      logger.error("解决方案: 检查请求参数和配置是否正确");
    }

    // 添加通用调试信息
    if (error.config) {
      logger.error("请求URL:", error.config.url);
      logger.error("请求方法:", error.config.method?.toUpperCase());
    }
  }
}

export default BinanceClient;
