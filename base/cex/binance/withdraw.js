import BinanceClient from "./client.js";
import Logger from "../../tools/logger.js";
import { getRandomValue, sleep } from "../../tools/common.js";
import { appendFileSync, existsSync } from "fs";
import { resolveFromModule } from "../../tools/path.js";
import logger from "../../tools/logger.js";

/**
 * 币安提现工具类
 * 支持多钱包批量提现，可指定币种和网络
 */
class BinanceWithdraw {
  constructor(apiKey, apiSecret, baseURL = "https://api.binance.com") {
    // 验证必需的API配置
    if (!apiKey || !apiSecret) {
      throw new Error("Missing required Binance API credentials");
    }

    Logger.info("=== 币安CEX提现工具启动 ===");


    // 创建币安客户端
    this.client = new BinanceClient(
      apiKey,
      apiSecret,
      {
        baseURL: baseURL
      }
    );
  }


  /**
   * 生成随机提现金额
   * @param {number} minAmount - 最小金额
   * @param {number} maxAmount - 最大金额
   * @param {number} decimalPlaces - 小数位数
   * @returns {string} 随机金额
   */
  generateRandomAmount(minAmount, maxAmount, decimalPlaces) {
    // 生成随机金额
    const randomAmount = Math.random() * (maxAmount - minAmount) + minAmount;

    // 保留指定小数位数
    return parseFloat(randomAmount.toFixed(decimalPlaces)).toString();
  }


  /**
   * 单个提现
   * @param {Object} withdrawConfig - 提现配置
   * @returns {Promise<Object>} 统一格式的提现结果
   */
  async singleWithdraw(withdrawConfig) {
    const {
      coin,
      network,
      address,
      amount,
      index
    } = withdrawConfig;

    // 验证必需参数
    if (!coin || !address || !amount || !network) {
      const result = {
        success: false,
        code: "INVALID_PARAMS",
        data: null,
        message: `[${index}]提现失败，缺少必要参数（coin, network, address, amount 缺一不可）`
      };

      // 记录失败结果
      this.recordWithdrawResult({
        exchange: "Binance",
        address: address || "unknown",
        network: network || "unknown",
        coin: coin || "unknown",
        amount: amount || "0",
        timestamp: Date.now(),
        success: false,
        transactionId: "",
        error: result.message
      });

      return result;
    }

    try {
      // 检查余额
      const balance = await this.client.getBalance(coin);
      const availableBalance = parseFloat(balance.free);
      const withdrawAmount = parseFloat(amount);

      if (withdrawAmount > availableBalance) {
        const result = {
          success: false,
          code: "INSUFFICIENT",
          data: {
            availableBalance,
            requestedAmount: withdrawAmount,
            coin: coin.toUpperCase()
          },
          message: `[${index}]余额不足, 当前余额: ${availableBalance}${coin}, 提现金额: ${withdrawAmount}${coin}`
        };

        // 记录失败结果
        this.recordWithdrawResult({
          exchange: "Binance",
          address,
          network,
          coin: coin.toUpperCase(),
          amount: amount.toString(),
          timestamp: Date.now(),
          success: false,
          transactionId: "",
          error: result.message
        });
        logger.error(`[${index}]提现失败,余额不足; 当前余额: ${availableBalance}${coin}, 提现金额: ${withdrawAmount}${coin}`)
        return result;
      }

      // 构建提现参数
      const withdrawParams = {
        coin: coin.toUpperCase(),
        address,
        amount: amount.toString(),
        network
      };

      // 执行提现
      const result = await this.client.withdraw(withdrawParams);
      if (result.success) {
        logger.success(`[${index}]提现成功, 交易所: Binance, 网络: ${network}, 币种: ${coin}, 金额: ${amount}, 地址: ${address}`)
      }else{
        logger.error(`[${index}]提现失败, ${result.message}`)
      }
      // 记录提现结果
      this.recordWithdrawResult({
        exchange: "Binance",
        address,
        network,
        coin: coin.toUpperCase(),
        amount: amount.toString(),
        timestamp: Date.now(),
        success: result.success,
        transactionId: result.success ? (result.data?.id || "") : "",
        error: result.success ? "" : result.message
      });

      return result;

    } catch (error) {
      // 处理意外错误
      const result = {
        success: false,
        code: "UNEXPECTED_ERROR",
        data: null,
        message: `[${index}]提现过程中发生意外错误: ${error.message}`
      };

      // 记录失败结果
      this.recordWithdrawResult({
        exchange: "Binance",
        address,
        network,
        coin: coin.toUpperCase(),
        amount: amount.toString(),
        timestamp: Date.now(),
        success: false,
        transactionId: "",
        error: result.message
      });

      logger.error(`[${index}]提现失败, ${result.message}`)
      return result;
    }
  }

  /**
   * 批量提现
   * @param {Object} params - 提现参数
   * @param {string} params.coin - 币种
   * @param {string} params.network - 网络
   * @param {Array} params.wallets - 钱包列表
   * @param {number} params.minAmount - 最小金额
   * @param {number} params.maxAmount - 最大金额
   * @param {number} params.decimalPlaces - 小数位数
   * @param {Object} params.intervalConfig - 间隔时间配置 {min, max}
   * @returns {Promise<Object>} 统一格式的批量提现结果
   */
  async batchWithdraw(params) {
    // 验证参数
    const requiredFields = ['coin', 'network', 'wallets', 'minAmount', 'maxAmount', 'intervalConfig', 'decimalPlaces'];
    for (const field of requiredFields) {
      if (params[field] === undefined) {
        return {
          success: false,
          code: "INVALID_PARAMS",
          data: null,
          message: `Missing required parameter: ${field}`
        };
      }
    }

    const {
      coin,
      network,
      wallets,
      minAmount,
      maxAmount,
      decimalPlaces,
      intervalConfig
    } = params;

    if (wallets.length === 0) {
      return {
        success: false,
        code: "INVALID_PARAMS",
        data: null,
        message: "No wallets provided for withdrawal"
      };
    }


    try {

      const results = [];
      let processedCount = 0;
      let successCount = 0;
      let failedCount = 0;

      for (let i = 0; i < wallets.length; i++) {
        const wallet = wallets[i];

        Logger.info(`正在处理第 ${i + 1}/${wallets.length} 个钱包`);

        // 生成随机金额
        const amount = this.generateRandomAmount(minAmount, maxAmount, decimalPlaces);

        const withdrawConfig = {
          coin,
          network,
          address: wallet.address,
          amount,
          index: wallet.index,
          name: wallet.name || `Wallet ${wallet.index}`
        };

        const result = await this.singleWithdraw(withdrawConfig);
        processedCount++;

        // 处理结果
        if (result.success) {
          results.push({
            index: wallet.index,
            success: true,
            result,
            config: withdrawConfig
          });

          successCount++;
        } else {
          results.push({
            index: wallet.index,
            success: false,
            error: result.message,
            code: result.code,
            config: { coin, address: wallet.address, amount }
          });

          failedCount++;

          // 如果是余额不足，立即停止所有后续提现
          if (result.code === "INSUFFICIENT") {
            Logger.error("余额不足，停止所有后续提现操作");

            // 将剩余未处理的钱包标记为跳过
            for (let j = i + 1; j < wallets.length; j++) {
              results.push({
                index: wallets[j].index,
                success: false,
                error: "由于余额不足，跳过提现",
                code: "SKIPPED_DUE_TO_INSUFFICIENT",
                config: { coin, address: wallets[j].address, amount: "N/A" }
              });
              failedCount++;
            }

            return {
              success: false,
              code: "INSUFFICIENT",
              data: {
                processed: processedCount,
                successful: successCount,
                failed: failedCount,
                total: wallets.length,
                results: results,
                stoppedAt: i + 1,
                reason: "余额不足，提前终止批量提现"
              },
              message: `批量提现因余额不足而终止。已处理 ${processedCount}/${wallets.length} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个`
            };
          }
        }

        // 延迟下一次提现（如果不是最后一个且当前成功）
        if (i < wallets.length - 1 && result.success) {
          const delay = getRandomValue(intervalConfig.min, intervalConfig.max);
          Logger.info(`等待 ${delay}s 后继续下一个钱包`);
          await sleep(delay);
        }
      }

      // 所有钱包处理完成
      const allSuccess = failedCount === 0;
      return {
        success: allSuccess,
        code: allSuccess ? "SUCCESS" : "PARTIAL_SUCCESS",
        data: {
          processed: processedCount,
          successful: successCount,
          failed: failedCount,
          total: wallets.length,
          results: results
        },
        message: allSuccess
          ? `批量提现全部成功。处理了 ${processedCount} 个钱包，全部成功`
          : `批量提现部分成功。处理了 ${processedCount} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个`
      };

    } catch (error) {
      Logger.error("批量提现发生异常:", error.message);
      return {
        success: false,
        code: "UNEXPECTED_ERROR",
        data: null,
        message: `批量提现过程中发生意外错误: ${error.message}`
      };
    }
  }

  /**
   * 显示账户概览
   * @returns {Promise}
   */
  async showAccountOverview() {
    try {
      Logger.info("Fetching account overview...");
      
      const balances = await this.client.getBalance();
      const nonZeroBalances = balances.filter(b => parseFloat(b.free) > 0);
      
      Logger.info(`Account has ${nonZeroBalances.length} coins with balance:`);
      
      nonZeroBalances.forEach(balance => {
        Logger.info(`${balance.asset}: ${balance.free} (Locked: ${balance.locked})`);
      });
      
    } catch (error) {
      Logger.error("显示账户概览失败:");
      Logger.error("错误详情:", error.message || "未知错误");

      // 如果是API相关错误，提供更多上下文
      if (error.response) {
        Logger.error("这可能是由于以下原因造成的:");
        Logger.error("1. API密钥无效或已过期");
        Logger.error("2. API密钥权限不足（需要读取权限）");
        Logger.error("3. 网络连接问题");
        Logger.error("4. 币安服务器暂时不可用");
      }

      throw error; // 重新抛出错误，让上层处理
    }
  }

  /**
   * 获取提现历史
   * @param {Object} params - 查询参数
   * @returns {Promise} 提现历史
   */
  async getWithdrawHistory(params = {}) {
    try {
      return await this.client.getWithdrawHistory(params);
    } catch (error) {
      Logger.error("Failed to get withdraw history:", error.message);
      throw error;
    }
  }

  /**
   * 获取币种网络信息
   * @param {string} coin - 币种
   * @returns {Promise} 网络信息
   */
  async getCoinNetworks(coin) {
    try {
      return await this.client.getCoinNetworks(coin);
    } catch (error) {
      Logger.error(`Failed to get networks for ${coin}:`, error.message);
      throw error;
    }
  }

  /**
   * 记录提现结果到文件
   * @param {Object} record - 提现记录
   */
  recordWithdrawResult(record) {
    try {
      const {
        exchange = "Binance",
        address,
        network,
        coin,
        amount,
        timestamp,
        success,
        transactionId = "",
        error = ""
      } = record;

      // 格式化时间
      const formattedTime = new Date(timestamp).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      // 构建记录行
      const result = success ? "成功" : "失败";
      const recordLine = `${exchange},${address},${network},${coin},${amount},${formattedTime},${result},${transactionId},${error}\n`;

      // 获取记录文件路径
      const recordFilePath = resolveFromModule(import.meta.url, "../withdraw_records.csv");

      // 检查文件是否存在，如果不存在则创建并写入表头
      if (!existsSync(recordFilePath)) {
        const header = "交易所,提现地址,提现网络,提现币种,提现数量,提现时间,提现结果,交易ID,错误信息\n";
        appendFileSync(recordFilePath, header, 'utf8');
        Logger.info(`Created withdraw records file: ${recordFilePath}`);
      }

      // 追加记录
      appendFileSync(recordFilePath, recordLine, 'utf8');

    } catch (error) {
      Logger.error("Failed to record withdraw result:", error.message);
    }
  }
}

export default BinanceWithdraw;
