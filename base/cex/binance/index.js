import BinanceWithdraw from "./withdraw.js";
import Logger from "../../tools/logger.js";
import { resolveFromModule } from "../../tools/path.js";
import { parseTOMLConfig } from "../../tools/common.js";

/**
 * 币安查询工具
 * 使用方法：node base/cex/binance/index.js <command>
 *
 * 支持的命令：
 * balance     - 显示账户概览
 * networks    - 查询币种网络信息
 * history     - 查询提现历史
 */

async function main() {
  try {
    const args = process.argv.slice(2);

    if (args.length < 1) {
      showUsage();
      return;
    }

    const command = args[0].toLowerCase();

    // 配置文件路径
    const configPath = resolveFromModule(import.meta.url, "../config.toml");
    const config = parseTOMLConfig(configPath);
    // 初始化提现工具
    const withdrawTool = new BinanceWithdraw(config.BINANCE_API_KEY, config.BINANCE_API_SECRET, config.BINANCE_BASE_URL);

    // 验证配置
    // if (!withdrawTool.validateConfig()) {
    //   Logger.error("配置验证失败");
    //   Logger.error("请检查以下配置项:");
    //   Logger.error("1. 确认 base/cex/config.toml 文件存在");
    //   Logger.error("2. 检查以下配置是否正确设置:");
    //   Logger.error("   - BINANCE_API_KEY = \"your_api_key_here\"");
    //   Logger.error("   - BINANCE_API_SECRET = \"your_api_secret_here\"");
    //   Logger.error("   - BINANCE_BASE_URL = \"https://api.binance.com\"");
    //   Logger.error("3. 确认API密钥格式正确（无多余空格或换行）");
    //   Logger.error("4. 确认API密钥有足够的权限（至少需要读取权限）");
    //   Logger.info("\n获取API密钥的步骤:");
    //   Logger.info("1. 登录币安账户");
    //   Logger.info("2. 进入 API 管理页面");
    //   Logger.info("3. 创建新的API密钥");
    //   Logger.info("4. 启用必要的权限（读取、提现等）");
    //   return;
    // }

    Logger.info(`Binance Query Tool initialized successfully`);

    switch (command) {
      case 'balance':
        await showAccountOverview(withdrawTool);
        break;

      case 'networks':
        // 先检查参数，避免不必要的API调用
        if (!args[1]) {
          Logger.error("请指定币种，例如: node base/cex/binance/index.js networks USDT");
          return;
        }
        await showNetworks(withdrawTool, args[1]);
        break;

      case 'history':
        await showHistory(withdrawTool, args[1], args[2]);
        break;

      default:
        Logger.error(`Unknown command: ${command}`);
        showUsage();
        process.exit(1);
    }

  } catch (error) {
    Logger.error("Query execution failed:", error.message);
    process.exit(1);
  }
}

/**
 * 显示账户概览
 * @param {BinanceWithdraw} withdrawTool - 提现工具实例
 */
async function showAccountOverview(withdrawTool) {
  try {
    Logger.info("=== 币安账户概览 ===");
    await withdrawTool.showAccountOverview();
  } catch (error) {
    Logger.error("获取账户概览失败:");
    Logger.error(`错误信息: ${error.message || '未知错误'}`);

    if (error.code) {
      Logger.error(`错误代码: ${error.code}`);
    }

    if (error.response) {
      Logger.error(`HTTP状态码: ${error.response.status}`);
      if (error.response.data) {
        Logger.error(`API响应: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }

    // 提供解决建议
    Logger.info("\n可能的解决方案:");
    Logger.info("1. 检查网络连接是否正常");
    Logger.info("2. 确认API密钥和密钥是否正确配置");
    Logger.info("3. 检查API密钥是否有足够的权限");
    Logger.info("4. 确认币安API服务是否正常");
  }
}

/**
 * 查询币种网络信息
 * @param {BinanceWithdraw} withdrawTool - 提现工具实例
 * @param {string} coin - 币种
 */
async function showNetworks(withdrawTool, coin) {
  let coinUpper = "未知币种"; // 默认值，避免未定义错误

  try {
    if (!coin) {
      Logger.error("请指定币种，例如: node base/cex/binance/index.js networks USDT");
      return;
    }

    coinUpper = coin.toUpperCase();
    Logger.info(`=== ${coinUpper} 支持的网络 ===`);

    const networks = await withdrawTool.getCoinNetworks(coinUpper);
    if (networks.length === 0) {
      Logger.warn(`未找到 ${coinUpper} 支持的网络`);
      return;
    }

    networks.forEach((network, index) => {
      Logger.info(`${index + 1}. 网络: ${network.network}`);
      Logger.info(`   最小提现: ${network.withdrawMin} ${coinUpper}`);
      Logger.info(`   手续费: ${network.withdrawFee} ${coinUpper}`);
      Logger.info(`   状态: ${network.withdrawEnable ? '可用' : '不可用'}`);
      Logger.info('');
    });
  } catch (error) {
    Logger.error(`获取 ${coinUpper} 网络信息失败:`);
    Logger.error(`错误类型: ${error.name || 'Unknown'}`);
    Logger.error(`错误信息: ${error.message || '未知错误'}`);

    if (error.code) {
      Logger.error(`错误代码: ${error.code}`);
    }

    if (error.response) {
      Logger.error(`HTTP状态码: ${error.response.status}`);
      if (error.response.data) {
        Logger.error(`API响应: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }

    // 提供解决建议
    Logger.info("\n可能的解决方案:");
    Logger.info("1. 检查币种名称是否正确（如：USDT, BNB, BTC）");
    Logger.info("2. 确认API密钥是否有查询权限");
    Logger.info("3. 检查网络连接是否正常");
    Logger.info("4. 如果是SDK版本问题，请尝试更新 @binance/connector");
  }
}

/**
 * 查询提现历史
 * @param {BinanceWithdraw} withdrawTool - 提现工具实例
 * @param {string} coin - 币种（可选）
 * @param {string} limit - 限制数量（可选）
 */
async function showHistory(withdrawTool, coin, limit) {
  try {
    const queryParams = {};

    if (coin) {
      queryParams.coin = coin.toUpperCase();
    }

    const limitNum = limit ? parseInt(limit) : 10;
    if (limitNum > 0) {
      queryParams.limit = limitNum;
    }

    Logger.info("=== 提现历史记录 ===");
    if (coin) {
      Logger.info(`币种: ${coin.toUpperCase()}`);
    }
    Logger.info(`显示最近 ${limitNum} 条记录`);
    Logger.info('');

    const history = await withdrawTool.getWithdrawHistory(queryParams);

    if (history.length === 0) {
      Logger.warn("未找到提现记录");
      return;
    }

    history.forEach((record, index) => {
      const status = record.status === 6 ? '✅ 成功' :
                    record.status === 4 ? '❌ 失败' :
                    record.status === 2 ? '⏳ 处理中' :
                    `🔄 状态${record.status}`;

      Logger.info(`${index + 1}. ${status}`);
      Logger.info(`   币种: ${record.coin}`);
      Logger.info(`   数量: ${record.amount}`);
      Logger.info(`   地址: ${record.address}`);
      Logger.info(`   网络: ${record.network || '默认'}`);
      Logger.info(`   时间: ${new Date(record.applyTime).toLocaleString('zh-CN')}`);
      if (record.txId) {
        Logger.info(`   交易ID: ${record.txId}`);
      }
      Logger.info('');
    });
  } catch (error) {
    Logger.error("获取提现历史失败:");
    Logger.error(`错误类型: ${error.name || 'Unknown'}`);
    Logger.error(`错误信息: ${error.message || '未知错误'}`);

    if (error.code) {
      Logger.error(`错误代码: ${error.code}`);
    }

    if (error.response) {
      Logger.error(`HTTP状态码: ${error.response.status}`);
      if (error.response.data) {
        Logger.error(`API响应: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }

    // 提供解决建议
    Logger.info("\n可能的解决方案:");
    Logger.info("1. 确认API密钥是否有查询提现历史的权限");
    Logger.info("2. 检查币种名称是否正确（如果指定了币种）");
    Logger.info("3. 检查网络连接是否正常");
  }
}
/**
 * 显示使用说明
 */
function showUsage() {
  console.log(`
币安查询工具

使用方法:
  node base/cex/binance/index.js <command> [parameters]

支持的命令:
  balance                    显示账户概览
  networks <coin>            查询币种网络信息
  history [coin] [limit]     查询提现历史

参数说明:
  coin     - 币种 (如: USDT, BTC, ETH, BNB)
  limit    - 历史记录数量限制 (默认: 10)

示例:
  node base/cex/binance/index.js balance
  node base/cex/binance/index.js networks USDT
  node base/cex/binance/index.js networks BNB
  node base/cex/binance/index.js history
  node base/cex/binance/index.js history USDT
  node base/cex/binance/index.js history USDT 20
  `);
}

// 运行主函数
main();
