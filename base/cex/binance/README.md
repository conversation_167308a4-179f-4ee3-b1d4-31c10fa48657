# 币安查询工具

这是一个专门用于查询币安交易所账户信息的工具，提供三个核心功能：

## 🚀 功能特性

1. **账户概览** - 显示账户余额和基本信息
2. **网络查询** - 查询指定币种支持的网络信息
3. **提现历史** - 查询提现历史记录

## 📋 使用方法

### 基本语法

```bash
node base/cex/binance/index.js <command> [parameters]
```

### 支持的命令

#### 1. balance - 显示账户概览

```bash
node base/cex/binance/index.js balance
```

显示内容：
- 账户总资产
- 各币种余额
- 可用余额和冻结余额

#### 2. networks - 查询币种网络信息

```bash
node base/cex/binance/index.js networks <coin>
```

参数：
- `coin` - 币种名称（如：USDT, BNB, BTC, ETH）

示例：
```bash
# 查询USDT支持的网络
node base/cex/binance/index.js networks USDT

# 查询BNB支持的网络
node base/cex/binance/index.js networks BNB
```

显示内容：
- 支持的网络列表
- 每个网络的最小提现金额
- 每个网络的手续费
- 网络可用状态

#### 3. history - 查询提现历史

```bash
node base/cex/binance/index.js history [coin] [limit]
```

参数：
- `coin` - 币种名称（可选，不指定则查询所有币种）
- `limit` - 记录数量限制（可选，默认10条）

示例：
```bash
# 查询所有币种的提现历史（最近10条）
node base/cex/binance/index.js history

# 查询USDT的提现历史（最近10条）
node base/cex/binance/index.js history USDT

# 查询USDT的提现历史（最近20条）
node base/cex/binance/index.js history USDT 20

# 查询BTC的提现历史（最近5条）
node base/cex/binance/index.js history BTC 5
```

显示内容：
- 提现状态（成功/失败/处理中）
- 币种和数量
- 目标地址
- 使用的网络
- 提现时间
- 交易ID（如果有）

## ⚙️ 配置要求

使用前需要在 `base/cex/config.toml` 文件中配置币安API密钥：

```toml
# 币安API配置
BINANCE_API_KEY = "your_api_key_here"
BINANCE_API_SECRET = "your_api_secret_here"
BINANCE_BASE_URL = "https://api.binance.com"
```

### 获取API密钥

1. 登录币安账户
2. 进入 API 管理页面
3. 创建新的API密钥
4. 确保API密钥有以下权限：
   - 读取权限（查看账户信息）
   - 提现权限（查看提现历史）

## 🔒 安全说明

- API密钥仅用于查询，不执行任何提现操作
- 建议使用只读权限的API密钥
- 请妥善保管您的API密钥，不要泄露给他人

## 📊 输出示例

### 账户概览示例
```
=== 币安账户概览 ===
总资产: $1,234.56
可用余额:
- USDT: 500.00
- BNB: 2.5
- BTC: 0.01
```

### 网络查询示例
```
=== USDT 支持的网络 ===
1. 网络: TRC20
   最小提现: 1.0 USDT
   手续费: 1.0 USDT
   状态: 可用

2. 网络: ERC20
   最小提现: 10.0 USDT
   手续费: 5.0 USDT
   状态: 可用

3. 网络: BEP20
   最小提现: 1.0 USDT
   手续费: 0.8 USDT
   状态: 可用
```

### 提现历史示例
```
=== 提现历史记录 ===
币种: USDT
显示最近 10 条记录

1. ✅ 成功
   币种: USDT
   数量: 100.0
   地址: TYourWalletAddress1Here
   网络: TRC20
   时间: 2025/08/03 17:30:15
   交易ID: 0x1234567890abcdef

2. ❌ 失败
   币种: USDT
   数量: 50.0
   地址: 0xYourWalletAddress2Here
   网络: ERC20
   时间: 2025/08/03 16:45:30
```

## 🆘 故障排除

### 常见错误

1. **Configuration validation failed**
   - 检查 `base/cex/config.toml` 文件是否存在
   - 确认API密钥和密钥是否正确配置

2. **API权限错误**
   - 确认API密钥有足够的权限
   - 检查API密钥是否已启用

3. **网络连接错误**
   - 检查网络连接
   - 确认币安API服务是否正常

### 获取帮助

运行以下命令查看使用说明：

```bash
node base/cex/binance/index.js
```

## 🔗 相关工具

- **提现工具**：使用 `../index.js` 进行批量提现操作
- **记录查看**：使用 `../view_records.js` 查看提现记录
