import { readFileSync, existsSync } from "fs";
import { resolveFromModule } from "../tools/path.js";
import Logger from "../tools/logger.js";

/**
 * 提现记录查看工具
 * 使用方法：node base/cex/view_records.js [options]
 * 
 * 选项：
 * --limit=<number>    显示最近N条记录（默认：10）
 * --coin=<coin>       只显示指定币种的记录
 * --success           只显示成功的记录
 * --failed            只显示失败的记录
 * --stats             显示统计信息
 */

function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    limit: 10,
    coin: null,
    success: null,
    stats: false
  };

  for (const arg of args) {
    if (arg.startsWith('--limit=')) {
      options.limit = parseInt(arg.split('=')[1]) || 10;
    } else if (arg.startsWith('--coin=')) {
      options.coin = arg.split('=')[1].toUpperCase();
    } else if (arg === '--success') {
      options.success = true;
    } else if (arg === '--failed') {
      options.success = false;
    } else if (arg === '--stats') {
      options.stats = true;
    } else if (arg === '--help') {
      showUsage();
      process.exit(0);
    }
  }

  return options;
}

function showUsage() {
  console.log(`
提现记录查看工具

使用方法:
  node base/cex/view_records.js [options]

选项:
  --limit=<number>    显示最近N条记录（默认：10）
  --coin=<coin>       只显示指定币种的记录（如：BNB, USDT）
  --success           只显示成功的记录
  --failed            只显示失败的记录
  --stats             显示统计信息
  --help              显示此帮助信息

示例:
  node base/cex/view_records.js                    # 显示最近10条记录
  node base/cex/view_records.js --limit=20         # 显示最近20条记录
  node base/cex/view_records.js --coin=BNB         # 只显示BNB记录
  node base/cex/view_records.js --failed           # 只显示失败记录
  node base/cex/view_records.js --stats            # 显示统计信息
  `);
}

function parseCSV(content) {
  const lines = content.trim().split('\n');
  if (lines.length <= 1) return [];

  const headers = lines[0].split(',');
  const records = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',');
    if (values.length === headers.length) {
      const record = {};
      headers.forEach((header, index) => {
        record[header] = values[index];
      });
      records.push(record);
    }
  }

  return records;
}

function filterRecords(records, options) {
  let filtered = records;

  // 按币种过滤
  if (options.coin) {
    filtered = filtered.filter(record => record['提现币种'] === options.coin);
  }

  // 按成功/失败过滤
  if (options.success !== null) {
    const targetResult = options.success ? '成功' : '失败';
    filtered = filtered.filter(record => record['提现结果'] === targetResult);
  }

  // 限制数量（显示最新的记录）
  if (options.limit > 0) {
    filtered = filtered.slice(-options.limit);
  }

  return filtered;
}

function displayRecords(records) {
  if (records.length === 0) {
    Logger.info("没有找到匹配的记录");
    return;
  }

  Logger.info(`=== 提现记录 (共 ${records.length} 条) ===`);
  
  records.forEach((record, index) => {
    const status = record['提现结果'] === '成功' ? '✅' : '❌';
    const txId = record['交易ID'] ? ` (${record['交易ID'].substring(0, 10)}...)` : '';
    const error = record['错误信息'] ? ` - ${record['错误信息']}` : '';
    
    console.log(`${index + 1}. ${status} ${record['提现时间']} | ${record['提现币种']} ${record['提现数量']} → ${record['提现地址'].substring(0, 10)}... (${record['提现网络']})${txId}${error}`);
  });
}

function displayStats(records) {
  if (records.length === 0) {
    Logger.info("没有记录可统计");
    return;
  }

  const stats = {
    total: records.length,
    success: 0,
    failed: 0,
    coins: {},
    networks: {},
    totalAmount: {}
  };

  records.forEach(record => {
    // 成功/失败统计
    if (record['提现结果'] === '成功') {
      stats.success++;
    } else {
      stats.failed++;
    }

    // 币种统计
    const coin = record['提现币种'];
    if (!stats.coins[coin]) {
      stats.coins[coin] = { count: 0, success: 0, failed: 0 };
    }
    stats.coins[coin].count++;
    if (record['提现结果'] === '成功') {
      stats.coins[coin].success++;
    } else {
      stats.coins[coin].failed++;
    }

    // 网络统计
    const network = record['提现网络'];
    if (!stats.networks[network]) {
      stats.networks[network] = 0;
    }
    stats.networks[network]++;

    // 金额统计
    const amount = parseFloat(record['提现数量']);
    if (!isNaN(amount)) {
      if (!stats.totalAmount[coin]) {
        stats.totalAmount[coin] = 0;
      }
      if (record['提现结果'] === '成功') {
        stats.totalAmount[coin] += amount;
      }
    }
  });

  Logger.info("=== 提现统计信息 ===");
  Logger.info(`总记录数: ${stats.total}`);
  Logger.info(`成功: ${stats.success} (${(stats.success / stats.total * 100).toFixed(1)}%)`);
  Logger.info(`失败: ${stats.failed} (${(stats.failed / stats.total * 100).toFixed(1)}%)`);

  Logger.info("\n=== 币种统计 ===");
  Object.entries(stats.coins).forEach(([coin, data]) => {
    Logger.info(`${coin}: ${data.count}次 (成功: ${data.success}, 失败: ${data.failed})`);
  });

  Logger.info("\n=== 网络统计 ===");
  Object.entries(stats.networks).forEach(([network, count]) => {
    Logger.info(`${network}: ${count}次`);
  });

  Logger.info("\n=== 成功提现总额 ===");
  Object.entries(stats.totalAmount).forEach(([coin, amount]) => {
    Logger.info(`${coin}: ${amount.toFixed(6)}`);
  });
}

async function main() {
  try {
    const options = parseArguments();
    const recordFilePath = resolveFromModule(import.meta.url, "./withdraw_records.csv");

    if (!existsSync(recordFilePath)) {
      Logger.error("提现记录文件不存在:", recordFilePath);
      Logger.info("请先执行提现操作以生成记录文件");
      process.exit(1);
    }

    const content = readFileSync(recordFilePath, 'utf8');
    const allRecords = parseCSV(content);

    if (options.stats) {
      displayStats(allRecords);
    } else {
      const filteredRecords = filterRecords(allRecords, options);
      displayRecords(filteredRecords);
    }

  } catch (error) {
    Logger.error("查看记录失败:", error.message);
    process.exit(1);
  }
}

main();
