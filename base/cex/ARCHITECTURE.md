# 重构后的架构设计

经过重构，代码架构更加清晰，职责分离更加明确。

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   index.js      │    │  withdraw.js    │    │   client.js     │
│  (控制层)        │───▶│  (业务层)        │───▶│  (API层)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ config.toml     │    │ 提现记录         │    │ 币安API         │
│ wallets.csv     │    │ 余额检查         │    │ 网络请求         │
│ 参数验证         │    │ 金额生成         │    │ 错误处理         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 职责分离

### `index.js` - 控制层
**职责：**
- ✅ 解析命令行参数
- ✅ 读取和验证配置文件
- ✅ 读取和验证钱包文件
- ✅ 参数准备和验证
- ✅ 调用业务层执行操作
- ✅ 结果展示和统计

**不再负责：**
- ❌ 具体的提现逻辑
- ❌ API调用
- ❌ 余额检查

### `withdraw.js` - 业务层
**职责：**
- ✅ 接收准备好的参数
- ✅ 执行提现业务逻辑
- ✅ 余额检查和验证
- ✅ 随机金额生成
- ✅ 提现记录保存
- ✅ 重试机制
- ✅ 间隔时间控制

**不再负责：**
- ❌ 读取配置文件
- ❌ 读取钱包文件
- ❌ 参数解析
- ❌ 文件路径处理

### `client.js` - API层
**职责：**
- ✅ 币安API调用
- ✅ 网络请求处理
- ✅ 错误处理和重试
- ✅ 代理配置
- ✅ 响应数据处理

## 🔄 数据流

### 1. 参数准备阶段
```
命令行参数 → 参数解析 → 配置读取 → 钱包读取 → 参数验证 → 参数对象
```

### 2. 提现执行阶段
```
参数对象 → 余额检查 → 批量提现 → API调用 → 结果记录 → 统计报告
```

## 📊 重构前后对比

| 方面 | 重构前 | 重构后 |
|------|--------|--------|
| **配置读取** | withdraw.js 中读取 | index.js 中读取 |
| **钱包读取** | withdraw.js 中读取 | index.js 中读取 |
| **参数传递** | 文件路径 + 选项对象 | 准备好的参数对象 |
| **职责分离** | 混合职责 | 清晰分离 |
| **测试性** | 难以测试 | 易于测试 |
| **可维护性** | 耦合度高 | 松耦合 |

## 🎯 重构优势

### 1. 职责清晰
- **index.js**: 专注于参数处理和流程控制
- **withdraw.js**: 专注于提现业务逻辑
- **client.js**: 专注于API通信

### 2. 易于测试
```javascript
// 现在可以轻松进行单元测试
const withdrawParams = {
  coin: 'USDT',
  network: 'BSC',
  wallets: [{ index: 1, address: '0x...' }],
  minAmount: 10,
  maxAmount: 50,
  decimalPlaces: 2,
  intervalConfig: { min: 5, max: 10 }
};

const result = await withdrawTool.batchWithdraw(withdrawParams);
```

### 3. 配置灵活
```javascript
// API配置独立传递
const apiConfig = {
  apiKey: 'your_key',
  apiSecret: 'your_secret',
  baseURL: 'https://api.binance.com'
};

const withdrawTool = new BinanceWithdraw(apiConfig);
```

### 4. 参数验证集中
```javascript
// 所有参数验证在 index.js 中完成
const withdrawParams = prepareWithdrawParams(
  coinConfig, 
  intervalConfig, 
  wallets, 
  coin, 
  walletRange
);
```

## 🔧 新的API接口

### BinanceWithdraw 构造函数
```javascript
// 旧接口
new BinanceWithdraw(configPath)

// 新接口
new BinanceWithdraw(apiConfig)
```

### batchWithdraw 方法
```javascript
// 旧接口
batchWithdraw(coin, walletFilePath, indexArg, options)

// 新接口
batchWithdraw(params)
```

### 参数对象结构
```javascript
const params = {
  coin: 'USDT',                    // 币种
  network: 'BSC',                  // 网络
  wallets: [...],                  // 钱包列表
  minAmount: 10,                   // 最小金额
  maxAmount: 50,                   // 最大金额
  decimalPlaces: 2,                // 小数位数
  intervalConfig: { min: 5, max: 10 }, // 间隔配置
  maxRetries: 3,                   // 最大重试次数
  continueOnError: true            // 出错时是否继续
};
```

## 🚀 使用示例

### 完整流程
```javascript
// 1. 读取配置
const config = parseTOMLConfig(configPath);

// 2. 读取钱包
const wallets = await loadWallets(walletFilePath);

// 3. 准备参数
const withdrawParams = prepareWithdrawParams(
  coinConfig, intervalConfig, wallets, coin, walletRange
);

// 4. 创建工具实例
const apiConfig = {
  apiKey: config.BINANCE_API_KEY,
  apiSecret: config.BINANCE_API_SECRET,
  baseURL: config.BINANCE_BASE_URL
};
const withdrawTool = new BinanceWithdraw(apiConfig);

// 5. 执行提现
const result = await withdrawTool.batchWithdraw(withdrawParams);
```

## 📈 性能优化

1. **配置缓存**: 配置只读取一次
2. **钱包预加载**: 钱包列表预先过滤
3. **参数验证前置**: 避免运行时错误
4. **API连接复用**: 同一个客户端实例处理所有请求

## 🔮 扩展性

重构后的架构更容易扩展：

1. **支持新交易所**: 只需实现新的 withdraw 类
2. **支持新网络**: 只需添加配置和参数
3. **支持新功能**: 在相应层次添加功能
4. **支持新格式**: 修改参数准备逻辑

这种架构设计遵循了单一职责原则，使代码更加模块化、可测试和可维护。
