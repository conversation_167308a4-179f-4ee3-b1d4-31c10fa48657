import fs from "fs/promises";
import bs58 from 'bs58';
import * as bip39 from '@scure/bip39';
import { wordlist } from '@scure/bip39/wordlists/english';
import { Keypair } from '@solana/web3.js';
import { derivePath } from 'ed25519-hd-key';
import logger from "../tools/logger.js";


class SolanaWallet {
  constructor() {}

  /**
   * 根据助记词获取私钥跟钱包地址
   * @param {*} mnemonic
   * @returns
   */
  async getPrivateKeyAndAddress(mnemonic) {
    if (!bip39.validateMnemonic(mnemonic, wordlist)) {
        throw new Error('无效的助记词');
    }

    const seed = bip39.mnemonicToSeedSync(mnemonic, '');

    // 根据backpack的助记词生成路径
    const path = `m/44'/501'/0'/0'`;
    const derivedSeed = derivePath(path, seed).key;
    const keypair = Keypair.fromSeed(derivedSeed);

    return {
        address: keypair.publicKey.toString(),
        privateKey: bs58.encode(keypair.secretKey),
    };

  }

  /**
   * 生成指定数量的钱包
   * @param {number} count - 要生成的钱包数量
   * @param {string} outputFile - 保存钱包信息的文件路径
   * @returns {Promise<Array<Object>>} 返回生成的钱包列表
   */
  async createWallets(count, outputFile) {
    const wallets = [];

    for (let i = 0; i < count; i++) {
      const mnemonic = bip39.generateMnemonic(wordlist, 256);
      const walletInfo = await this.getPrivateKeyAndAddress(mnemonic);

      wallets.push({
        index: i + 1,
        mnemonic,
        address: walletInfo.address,
        privateKey: walletInfo.privateKey,
      });

      logger.info(`已生成第 ${i + 1} 个钱包`);
    }

    await this._saveToFile(wallets, outputFile);
    logger.info(`\n✅ 成功生成 ${count} 个钱包`);
    return wallets;
  }

  /**
   * 查询多个地址的余额
   * @param {Array<string>} addresses - 要查询的地址列表
   * @returns {Promise<Array<Object>>} 返回查询结果列表
   */
  async checkBalances(addresses) {
    logger.info(`开始查询 ${addresses.length} 个地址的余额`);

    const results = [];

    for (const address of addresses) {
      const result = await this.checkBalance(address);
      results.push(result);
    }

    return results;
  }

  // 私有方法，用于保存钱包信息到文件
  async _saveToFile(wallets, outputFile) {
    const header = "index,mnemonic,private_key,address\n";

    let content = header;
    wallets.forEach((wallet) => {
      content += `${wallet.index},"${wallet.mnemonic}","${wallet.privateKey}","${wallet.address}"\n`;
    });

    await fs.writeFile(outputFile, content, "utf8");
  }

  // 更新查询单个地址余额的私有方法
  async checkBalance(address) {
    // TODO: 实现查询余额
    return {
      address,
      balance: "0",
      balanceInSui: "0",
      success: false,
      error: "Not implemented",
      timestamp: new Date().toISOString(),
    };
  }
}

export default SolanaWallet;




