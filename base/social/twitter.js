import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";
import { QueryIds } from "./constants.js";

/**
 * Twitter API 客户端类
 */
class Twitter {
  /**
   * 创建 Twitter 客户端实例
   * @param {Object} accountInfo - 账号信息
   * @param {string} accountInfo.twitter_auth_token - Twitter认证token
   * @param {string} [accountInfo.proxy] - 代理服务器地址
   */
  constructor(accountInfo) {
    console.log("初始化Twitter客户端...");

    // 基础配置
    this.baseUrl = "https://x.com";
    this.auth_token = accountInfo.twitter_auth_token;

    // 设置请求头
    this.headers = {
      authority: "x.com",
      accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "accept-language": "en-US,en;q=0.9",
      "cache-control": "max-age=0",
      "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      "sec-fetch-dest": "document",
      "sec-fetch-mode": "navigate",
      "sec-fetch-site": "none",
      "sec-fetch-user": "?1",
      "upgrade-insecure-requests": "1",
      "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      Cookie: `auth_token=${accountInfo.twitter_auth_token}`,
    };

    // 创建axios实例
    const axiosConfig = {
      headers: this.headers,
      timeout: 10000, // 修改为10秒
      withCredentials: true,
      maxRedirects: 5,
    };

    // 配置代理
    if (accountInfo.proxy) {
      console.log("配置代理:", accountInfo.proxy);
      const proxyAgent = new HttpsProxyAgent(accountInfo.proxy);
      axiosConfig.httpsAgent = proxyAgent;
      axiosConfig.proxy = false;
    }

    this.client = axios.create(axiosConfig);
    this.myUserId = null;
    this.myUsername = null;
    this.isLoggedIn = false; // 添加登录状态标记
  }

  /**
   * 确保用户已登录
   * @private
   */
  async ensureLoggedIn() {
    if (!this.isLoggedIn) {
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error("自动登录失败");
      }
      this.isLoggedIn = true;
    }
  }

  /**
   * 执行需要登录的操作
   * @private
   * @param {Function} operation - 要执行的操作函数
   */
  async executeWithLogin(operation) {
    await this.ensureLoggedIn();
    return operation();
  }

  /**
   * 登录Twitter并获取ct0 token
   * @returns {Promise<boolean>} 登录是否成功
   */
  async login() {
    try {
      if (!this.headers["x-csrf-token"]) {
        // console.log('尝试登录获取ct0...');

        const response = await this.client.get(`${this.baseUrl}/home`);

        if (response.headers["set-cookie"]) {
          const cookies = response.headers["set-cookie"];
          let ct0 = cookies.find((cookie) => cookie.includes("ct0="));
          if (ct0) {
            ct0 = ct0.split("ct0=")[1].split(";")[0];
            this.headers["x-csrf-token"] = ct0;

            // 更新所有cookie，但保留原始的auth_token
            const newCookies = cookies
              .map((cookie) => cookie.split(";")[0])
              .filter((cookie) => !cookie.startsWith("auth_token="));

            const originalAuthToken = this.auth_token;
            const cookieStr = [...newCookies, `auth_token=${originalAuthToken}`].join("; ");

            this.headers["Cookie"] = cookieStr;
            this.client.defaults.headers = this.headers;
            return true;
          }
        }
        console.log("未找到ct0");
        return false;
      }
      return true;
    } catch (error) {
      console.error("登录失败:", error.message);
      if (error.response) {
        console.error("错误状态:", error.response.status);
      }
      return false;
    }
  }

  /**
   * 发送推文
   * @param {string} text - 推文内容
   * @returns {Promise<string>} 返回推文URL
   * @throws {Error} 发送失败时抛出异常
   */
  async createTweet(text) {
    if (!this.headers["x-csrf-token"]) {
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error("登录失败，无法获取ct0");
      }
    }

    try {
      // 验证必要的token
      if (!this.headers["Cookie"] || !this.headers["Cookie"].includes("auth_token=")) {
        throw new Error("缺少auth_token");
      }
      if (!this.headers["x-csrf-token"]) {
        throw new Error("缺少ct0 token");
      }

      // 构造请求数据
      const json_data = {
        variables: {
          tweet_text: text,
          dark_request: false,
          media: {
            media_entities: [],
            possibly_sensitive: false,
          },
          semantic_annotation_ids: [],
        },
        features: {
          premium_content_api_read_enabled: false,
          communities_web_enable_tweet_community_results_fetch: true,
          c9s_tweet_anatomy_moderator_badge_enabled: true,
          responsive_web_grok_analyze_button_fetch_trends_enabled: false,
          responsive_web_grok_analyze_post_followups_enabled: true,
          responsive_web_jetfuel_frame: false,
          responsive_web_grok_share_attachment_enabled: true,
          responsive_web_edit_tweet_api_enabled: true,
          graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
          view_counts_everywhere_api_enabled: true,
          longform_notetweets_consumption_enabled: true,
          responsive_web_twitter_article_tweet_consumption_enabled: true,
          tweet_awards_web_tipping_enabled: false,
          creator_subscriptions_quote_tweet_preview_enabled: false,
          longform_notetweets_rich_text_read_enabled: true,
          longform_notetweets_inline_media_enabled: true,
          profile_label_improvements_pcf_label_in_post_enabled: true,
          rweb_tipjar_consumption_enabled: true,
          responsive_web_graphql_exclude_directive_enabled: true,
          verified_phone_label_enabled: false,
          articles_preview_enabled: true,
          rweb_video_timestamps_enabled: true,
          responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
          freedom_of_speech_not_reach_fetch_enabled: true,
          standardized_nudges_misinfo: true,
          tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
          responsive_web_grok_image_annotation_enabled: false,
          responsive_web_graphql_timeline_navigation_enabled: true,
          responsive_web_enhance_cards_enabled: false,
        },
        queryId: QueryIds.CREATE_TWEET,
      };

      // 创建新的axios实例用于发推
      const tweetClient = axios.create({
        baseURL: "https://x.com",
        headers: {
          authority: "x.com",
          accept: "*/*",
          "accept-encoding": "gzip, deflate, br, zstd",
          "accept-language": "zh-CN,zh;q=0.9",
          authorization:
            "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
          "content-type": "application/json",
          cookie: this.headers["Cookie"],
          priority: "u=1, i",
          referer: "https://x.com/compose/tweet",
          "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          "x-csrf-token": this.headers["x-csrf-token"],
          "x-twitter-active-user": "yes",
          "x-twitter-auth-type": "OAuth2Session",
          "x-twitter-client-language": "en",
        },
        withCredentials: true,
        timeout: 10000,
      });

      if (this.client.defaults.httpsAgent) {
        tweetClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
        tweetClient.defaults.proxy = false;
      }

      // 添加请求拦截器进行重试
      tweetClient.interceptors.response.use(null, async (error) => {
        const config = error.config;

        // 如果没有设置重试配置，就返回错误
        if (!config || !config.retry) return Promise.reject(error);

        // 设置重试次数计数器
        config.__retryCount = config.__retryCount || 0;

        // 如果重试次数超过设置的重试次数，就返回错误
        if (config.__retryCount >= config.retry) {
          return Promise.reject(error);
        }

        // 增加重试计数
        config.__retryCount += 1;
        console.log(`重试第 ${config.__retryCount} 次...`);

        // 延迟后重试
        const backoff = new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, config.retryDelay || 1000);
        });

        await backoff;
        return tweetClient(config);
      });

      const url = `/i/api/graphql/${QueryIds.CREATE_TWEET}/CreateTweet`;
      console.log("发送请求...");

      const response = await tweetClient.post(url, json_data);

      if (response.data.errors) {
        // 检查是否是重复推文错误
        const error = response.data.errors[0];
        throw new Error(error.message || "Unknown error");
      }

      if (response.data.data?.create_tweet?.tweet_results?.result) {
        const result = response.data.data.create_tweet.tweet_results.result;
        const restId = result.rest_id;
        const screenName = result.core.user_results.result.legacy.screen_name;
        const tweetUrl = `https://twitter.com/${screenName}/status/${restId}`;
        console.log("发推成功:", tweetUrl);
        return tweetUrl;
      }

      throw new Error("发推失败：未获取到推文URL");
    } catch (error) {
      if (error.code === "ECONNABORTED") {
        console.error("请求超时，正在重试...");
        // 可以在这里添加重试逻辑
      }
      console.error("发推失败:", error.message);
      if (error.response?.data?.errors) {
        // 如果是API返回的错误，直接抛出
        throw new Error(error.response.data.errors[0]?.message || "Unknown API error");
      }
      // 其他错误继续抛出
      throw error;
    }
  }

  /**
   * 获取用户基本信息
   * @returns {Promise<string>} 返回用户名
   * @throws {Error} 获取失败时抛出异常
   */
  async getMyProfileInfo() {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 验证必要的token
        if (!this.headers["Cookie"] || !this.headers["Cookie"].includes("auth_token=")) {
          throw new Error("缺少auth_token");
        }
        if (!this.headers["x-csrf-token"]) {
          throw new Error("缺少ct0 token");
        }

        // console.log('请求前验证:');
        // console.log('auth_token存在:', this.headers['Cookie'].includes('auth_token='));
        // console.log('ct0存在:', !!this.headers['x-csrf-token']);
        // console.log('完整cookie:', this.headers['Cookie']);

        // 构造URL参数
        const variables = JSON.stringify({
          withCommunitiesMemberships: true,
        });

        const features = JSON.stringify({
          rweb_tipjar_consumption_enabled: true,
          responsive_web_graphql_exclude_directive_enabled: true,
          verified_phone_label_enabled: false,
          creator_subscriptions_tweet_preview_api_enabled: true,
          responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
          responsive_web_graphql_timeline_navigation_enabled: true,
        });

        const fieldToggles = JSON.stringify({
          withAuxiliaryUserLabels: false,
        });

        const url = `/i/api/graphql/${QueryIds.VIEWER_INFO}/Viewer?variables=${encodeURIComponent(
          variables,
        )}&features=${encodeURIComponent(features)}&fieldToggles=${encodeURIComponent(
          fieldToggles,
        )}`;

        // 创建新的axios实例
        const profileClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            priority: "u=1, i",
            referer: "https://x.com/home",
            "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          profileClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          profileClient.defaults.proxy = false;
        }

        console.log("发送请求...");
        // console.log('完整URL:', url);
        // console.log('请求头:', JSON.stringify(profileClient.defaults.headers, null, 2));

        const response = await profileClient.get(url);

        console.log("响应状态:", response.status);
        // console.log('响应数据:', JSON.stringify(response.data, null, 2));

        if (response.data.errors) {
          throw new Error(response.data.errors[0]?.message || "Unknown error");
        }

        const userData = response.data.data?.viewer?.user_results?.result;
        if (!userData) {
          throw new Error("Failed to get user data from response");
        }

        // 保存用户信息
        this.myUsername = userData.legacy.screen_name;
        this.myUserId = userData.rest_id;

        return this.myUsername;
      } catch (error) {
        console.error("获取个人信息失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
        }
        throw new Error(`Get my profile info failed: ${error.message}`);
      }
    });
  }

  async getUserProfile(screenName) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 验证必要的token
        if (!this.headers["Cookie"] || !this.headers["Cookie"].includes("auth_token=")) {
          throw new Error("缺少auth_token");
        }
        if (!this.headers["x-csrf-token"]) {
          throw new Error("缺少ct0 token");
        }

        // console.log('请求前验证:');
        // console.log('auth_token存在:', this.headers['Cookie'].includes('auth_token='));
        // console.log('ct0存在:', !!this.headers['x-csrf-token']);
        // console.log('完整cookie:', this.headers['Cookie']);

        // 构造URL参数
        const variables = JSON.stringify({
          screen_name: screenName,
        });

        const features = JSON.stringify({
          hidden_profile_subscriptions_enabled: true,
          profile_label_improvements_pcf_label_in_post_enabled: true,
          rweb_tipjar_consumption_enabled: true,
          responsive_web_graphql_exclude_directive_enabled: true,
          verified_phone_label_enabled: false,
          subscriptions_verification_info_is_identity_verified_enabled: true,
          subscriptions_verification_info_verified_since_enabled: true,
          highlights_tweets_tab_ui_enabled: true,
          responsive_web_twitter_article_notes_tab_enabled: true,
          subscriptions_feature_can_gift_premium: true,
          creator_subscriptions_tweet_preview_api_enabled: true,
          responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
          responsive_web_graphql_timeline_navigation_enabled: true,
        });

        const fieldToggles = JSON.stringify({
          withAuxiliaryUserLabels: false,
        });

        const url = `/i/api/graphql/${
          QueryIds.USER_BY_SCREEN_NAME
        }/UserByScreenName?variables=${encodeURIComponent(variables)}&features=${encodeURIComponent(
          features,
        )}&fieldToggles=${encodeURIComponent(fieldToggles)}`;

        // 创建新的axios实例
        const profileClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            priority: "u=1, i",
            referer: `https://x.com/${screenName}`,
            "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          profileClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          profileClient.defaults.proxy = false;
        }

        console.log("发送请求...");
        // console.log('完整URL:', url);
        // console.log('请求头:', JSON.stringify(profileClient.defaults.headers, null, 2));

        const response = await profileClient.get(url);

        console.log("响应状态:", response.status);
        // console.log('响应数据:', JSON.stringify(response.data, null, 2));

        return response.data;
      } catch (error) {
        console.error("获取用户信息失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
        }
        throw new Error(`Get user profile failed: ${error.message}`);
      }
    });
  }

  /**
   * 获取指定用户的ID
   * @param {string} username - 用户名
   * @returns {Promise<number>} 返回用户ID
   * @throws {Error} 获取失败时抛出异常
   */
  async getUserId(username) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 验证必要的token
        if (!this.headers["Cookie"] || !this.headers["Cookie"].includes("auth_token=")) {
          throw new Error("缺少auth_token");
        }
        if (!this.headers["x-csrf-token"]) {
          throw new Error("缺少ct0 token");
        }

        // 处理用户名
        if (username.startsWith("@")) {
          username = username.substring(1);
        }
        username = username.toLowerCase();

        // 构造URL参数
        const variables = JSON.stringify({
          screen_name: username,
          withSafetyModeUserFields: true,
        });

        const features = JSON.stringify({
          hidden_profile_subscriptions_enabled: true,
          profile_label_improvements_pcf_label_in_post_enabled: true,
          rweb_tipjar_consumption_enabled: true,
          responsive_web_graphql_exclude_directive_enabled: true,
          verified_phone_label_enabled: false,
          subscriptions_verification_info_is_identity_verified_enabled: true,
          subscriptions_verification_info_verified_since_enabled: true,
          highlights_tweets_tab_ui_enabled: true,
          responsive_web_twitter_article_notes_tab_enabled: true,
          subscriptions_feature_can_gift_premium: true,
          creator_subscriptions_tweet_preview_api_enabled: true,
          responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
          responsive_web_graphql_timeline_navigation_enabled: true,
        });

        const fieldToggles = JSON.stringify({
          withAuxiliaryUserLabels: false,
        });

        const url = `/i/api/graphql/${
          QueryIds.USER_BY_SCREEN_NAME
        }/UserByScreenName?variables=${encodeURIComponent(variables)}&features=${encodeURIComponent(
          features,
        )}&fieldToggles=${encodeURIComponent(fieldToggles)}`;

        // 创建新的axios实例
        const client = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            priority: "u=1, i",
            referer: `https://x.com/${username}`,
            "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          client.defaults.httpsAgent = this.client.defaults.httpsAgent;
          client.defaults.proxy = false;
        }

        console.log("获取用户ID...");
        const response = await client.get(url);

        if (!response.data?.data || Object.keys(response.data.data).length === 0) {
          throw new Error("User not found");
        }

        const userId = response.data.data.user.result.rest_id;
        return parseInt(userId);
      } catch (error) {
        console.error("获取用户ID失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
        }
        throw new Error(`Get user id error: ${error.message}`);
      }
    });
  }

  /**
   * 查找已发送的推文
   * @param {Function} textConditionFunc - 文本匹配条件函数
   * @param {number} [count=20] - 获取的推文数量
   * @returns {Promise<string|null>} 返回匹配的推文URL或null
   * @throws {Error} 查找失败时抛出异常
   */
  async findPostedTweet(textConditionFunc, count = 20) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 验证必要的token
        if (!this.headers["Cookie"] || !this.headers["Cookie"].includes("auth_token=")) {
          throw new Error("缺少auth_token");
        }
        if (!this.headers["x-csrf-token"]) {
          throw new Error("缺少ct0 token");
        }

        // 确保已获取用户信息
        if (!this.myUserId || !this.myUsername) {
          await this.getMyProfileInfo();
        }

        // 构造URL参数
        const variables = JSON.stringify({
          userId: this.myUserId,
          count: count,
          includePromotedContent: false,
          withQuickPromoteEligibilityTweetFields: false,
          withVoice: false,
          withV2Timeline: true,
          withBirdwatchNotes: false,
          withDownvotePerspective: false,
          withReactionsMetadata: false,
          withReactionsPerspective: false,
          withSuperFollowsTweetFields: false,
          withSuperFollowsUserFields: false,
        });

        const features = JSON.stringify({
          profile_label_improvements_pcf_label_in_post_enabled: false,
          rweb_tipjar_consumption_enabled: true,
          responsive_web_graphql_exclude_directive_enabled: true,
          verified_phone_label_enabled: false,
          creator_subscriptions_tweet_preview_api_enabled: true,
          responsive_web_graphql_timeline_navigation_enabled: true,
          responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
          premium_content_api_read_enabled: false,
          communities_web_enable_tweet_community_results_fetch: true,
          c9s_tweet_anatomy_moderator_badge_enabled: true,
          responsive_web_grok_analyze_button_fetch_trends_enabled: true,
          responsive_web_grok_analyze_post_followups_enabled: false,
          responsive_web_grok_share_attachment_enabled: false,
          articles_preview_enabled: true,
          responsive_web_edit_tweet_api_enabled: true,
          graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
          view_counts_everywhere_api_enabled: true,
          longform_notetweets_consumption_enabled: true,
          responsive_web_twitter_article_tweet_consumption_enabled: true,
          tweet_awards_web_tipping_enabled: false,
          creator_subscriptions_quote_tweet_preview_enabled: false,
          freedom_of_speech_not_reach_fetch_enabled: true,
          standardized_nudges_misinfo: true,
          tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
          rweb_video_timestamps_enabled: true,
          longform_notetweets_rich_text_read_enabled: true,
          longform_notetweets_inline_media_enabled: true,
          responsive_web_enhance_cards_enabled: false,
        });

        const url = `/i/api/graphql/${
          QueryIds.USER_TWEETS
        }/UserTweets?variables=${encodeURIComponent(variables)}&features=${encodeURIComponent(
          features,
        )}`;

        // 创建新的axios实例
        const tweetClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-language": "zh-CN,zh;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            priority: "u=1, i",
            referer: `https://x.com/${this.myUsername}`,
            "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
          decompress: true,
        });

        if (this.client.defaults.httpsAgent) {
          tweetClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          tweetClient.defaults.proxy = false;
        }

        console.log("获取用户推文...");
        // console.log('请求URL:', url);
        console.log("用户ID:", this.myUserId);
        console.log("用户名:", this.myUsername);

        const response = await tweetClient.get(url);

        // 检查响应头
        // console.log('响应头:', response.headers);
        // console.log('响应类型:', typeof response.data);
        // console.log('响应数据:', JSON.stringify(response.data, null, 2));

        if (typeof response.data === "string") {
          console.error("响应数据是字符串，可能是压缩问题");
          return null;
        }

        if (response.data.errors) {
          throw new Error(response.data.errors[0]?.message || "Unknown error");
        }

        // 修改数据解析逻辑
        const timeline = response.data.data?.user?.result?.timeline_v2?.timeline;
        if (!timeline) {
          console.log("未找到推文数据");
          return null;
        }

        const entries = timeline.instructions.find(
          (instruction) => instruction.type === "TimelineAddEntries",
        )?.entries;

        if (!entries) {
          console.log("未找到推文条目");
          return null;
        }

        for (const entry of entries) {
          // 跳过非推文条目
          if (!entry.content?.itemContent?.tweet_results?.result) {
            continue;
          }

          const tweetResult = entry.content.itemContent.tweet_results.result;
          const tweetText = tweetResult.legacy?.full_text;

          console.log("检查推文:", tweetText);

          if (!tweetText || !textConditionFunc(tweetText)) continue;

          let tweetId = entry.entryId;
          if (tweetId.startsWith("tweet-")) {
            tweetId = tweetId.substring(6);
          }

          const tweetUrl = `https://x.com/${this.myUsername}/status/${tweetId}`;
          console.log("找到匹配的推文:", tweetUrl);
          return tweetUrl;
        }

        console.log("未找到匹配的推文");
        return null;
      } catch (error) {
        console.error("获取用户推文失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
          console.error("错误数据:", error.response.data);
        }
        throw new Error(`Find posted tweet error: ${error.message}`);
      }
    });
  }

  /**
   * 关注指定用户
   * @param {string} username - 要关注的用户名
   * @returns {Promise<boolean>} 关注是否成功
   * @throws {Error} 关注失败时抛出异常
   * @example
   * const twitter = new Twitter({ ... });
   * await twitter.follow('elonmusk');
   */
  async follow(username) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 获取用户ID
        const userId = await this.getUserId(username);
        if (!userId) {
          throw new Error(`未找到用户 ${username} 的ID`);
        }

        // 构造请求参数
        const params = new URLSearchParams({
          include_profile_interstitial_type: "1",
          include_blocking: "1",
          include_blocked_by: "1",
          include_followed_by: "1",
          include_want_retweets: "1",
          include_mute_edge: "1",
          include_can_dm: "1",
          include_can_media_tag: "1",
          include_ext_has_nft_avatar: "1",
          include_ext_is_blue_verified: "1",
          include_ext_verified_type: "1",
          include_ext_profile_image_shape: "1",
          skip_status: "1",
          user_id: userId.toString(),
        });

        // 创建新的axios实例
        const followClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-language": "zh-CN,zh;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/x-www-form-urlencoded",
            cookie: this.headers["Cookie"],
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          followClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          followClient.defaults.proxy = false;
        }

        const response = await followClient.post(
          "/i/api/1.1/friendships/create.json",
          params.toString(),
        );

        if (response.data && response.data.id) {
          console.log(`成功关注用户 ${username}`);
          return true;
        }

        throw new Error("关注请求失败");
      } catch (error) {
        console.error("关注用户失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
          console.error("错误数据:", error.response.data);
        }
        throw new Error(`Follow error: ${error.message}`);
      }
    });
  }

  /**
   * 转发推文
   * @param {string} tweetId - 要转发的推文ID
   * @returns {Promise<object>} 转发结果
   * @throws {Error} 转发失败时抛出异常
   * @example
   * const twitter = new Twitter({ ... });
   * await twitter.retweet('1234567890');
   */
  async retweet(tweetId) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 构造请求数据
        const json_data = {
          variables: {
            tweet_id: tweetId,
            dark_request: false,
            withDownvotePerspective: false,
            withReactionsMetadata: false,
            withReactionsPerspective: false,
          },
          features: {
            responsive_web_graphql_exclude_directive_enabled: true,
            verified_phone_label_enabled: false,
            creator_subscriptions_tweet_preview_api_enabled: true,
            responsive_web_graphql_timeline_navigation_enabled: true,
            responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
            c9s_tweet_anatomy_moderator_badge_enabled: true,
            tweetypie_unmention_optimization_enabled: true,
            responsive_web_edit_tweet_api_enabled: true,
            graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
            view_counts_everywhere_api_enabled: true,
            longform_notetweets_consumption_enabled: true,
            responsive_web_twitter_article_tweet_consumption_enabled: true,
            tweet_awards_web_tipping_enabled: false,
            freedom_of_speech_not_reach_fetch_enabled: true,
            standardized_nudges_misinfo: true,
            tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
            rweb_video_timestamps_enabled: true,
            longform_notetweets_rich_text_read_enabled: true,
            longform_notetweets_inline_media_enabled: true,
            responsive_web_enhance_cards_enabled: false,
          },
          queryId: QueryIds.CREATE_RETWEET,
        };

        // 创建新的axios实例
        const retweetClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "en-US,en;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            origin: "https://x.com",
            referer: `https://x.com/i/status/${tweetId}`,
            "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          retweetClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          retweetClient.defaults.proxy = false;
        }

        // 添加随机延迟
        const delay = Math.floor(Math.random() * (3000 - 1000)) + 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));

        const response = await retweetClient.post(
          `/i/api/graphql/${QueryIds.CREATE_RETWEET}/CreateRetweet`,
          json_data,
        );

        if (response.data.errors) {
          throw new Error(response.data.errors[0]?.message || "Unknown error");
        }

        console.log(`成功转发推文 ${tweetId}`);
        return response.data;
      } catch (error) {
        console.error("转发推文失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
          console.error("错误数据:", error.response.data);
        }
        throw new Error(`Retweet error: ${error.message}`);
      }
    });
  }

  /**
   * 点赞推文
   * @param {string} tweetId - 要点赞的推文ID
   * @returns {Promise<boolean>} 点赞是否成功
   * @throws {Error} 点赞失败时抛出异常
   */
  async like(tweetId) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 构造请求数据
        const json_data = {
          variables: {
            tweet_id: tweetId,
            dark_request: false,
          },
          queryId: QueryIds.FAVORITE_TWEET,
        };

        // 创建新的axios实例
        const likeClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "en-US,en;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            origin: "https://x.com",
            referer: `https://x.com/i/status/${tweetId}`,
            "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          likeClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          likeClient.defaults.proxy = false;
        }

        // 添加随机延迟
        const delay = Math.floor(Math.random() * (3000 - 1000)) + 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));

        const response = await likeClient.post(
          `/i/api/graphql/${QueryIds.FAVORITE_TWEET}/FavoriteTweet`,
          json_data,
        );

        if (response.data.errors) {
          throw new Error(response.data.errors[0]?.message || "Unknown error");
        }

        const success = response.data.data?.favorite_tweet === "Done";
        if (success) {
          console.log(`成功点赞推文 ${tweetId}`);
        }
        return success;
      } catch (error) {
        console.error("点赞推文失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
          console.error("错误数据:", error.response.data);
        }
        throw new Error(`Like error: ${error.message}`);
      }
    });
  }

  /**
   * 评论推文
   * @param {string} tweetId - 要评论的推文ID
   * @param {string} comment - 评论内容
   * @returns {Promise<object>} 评论结果
   * @throws {Error} 评论失败时抛出异常
   */
  async comment(tweetId, comment) {
    return this.executeWithLogin(async () => {
      if (!this.headers["x-csrf-token"]) {
        const loginSuccess = await this.login();
        if (!loginSuccess) {
          throw new Error("登录失败，无法获取ct0");
        }
      }

      try {
        // 构造请求数据
        const json_data = {
          variables: {
            tweet_text: comment,
            reply: {
              in_reply_to_tweet_id: tweetId,
              exclude_reply_user_ids: [],
            },
            dark_request: false,
            media: {
              media_entities: [],
              possibly_sensitive: false,
            },
            semantic_annotation_ids: [],
            disallowed_reply_options: null,
          },
          features: {
            premium_content_api_read_enabled: false,
            communities_web_enable_tweet_community_results_fetch: true,
            c9s_tweet_anatomy_moderator_badge_enabled: true,
            responsive_web_grok_analyze_button_fetch_trends_enabled: false,
            responsive_web_grok_analyze_post_followups_enabled: true,
            responsive_web_jetfuel_frame: false,
            responsive_web_grok_share_attachment_enabled: true,
            responsive_web_edit_tweet_api_enabled: true,
            graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
            view_counts_everywhere_api_enabled: true,
            longform_notetweets_consumption_enabled: true,
            responsive_web_twitter_article_tweet_consumption_enabled: true,
            tweet_awards_web_tipping_enabled: false,
            responsive_web_grok_analysis_button_from_backend: true,
            creator_subscriptions_quote_tweet_preview_enabled: false,
            longform_notetweets_rich_text_read_enabled: true,
            longform_notetweets_inline_media_enabled: true,
            profile_label_improvements_pcf_label_in_post_enabled: true,
            rweb_tipjar_consumption_enabled: true,
            responsive_web_graphql_exclude_directive_enabled: true,
            verified_phone_label_enabled: false,
            articles_preview_enabled: true,
            rweb_video_timestamps_enabled: true,
            responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
            freedom_of_speech_not_reach_fetch_enabled: true,
            standardized_nudges_misinfo: true,
            tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
            responsive_web_grok_image_annotation_enabled: false,
            responsive_web_graphql_timeline_navigation_enabled: true,
            responsive_web_enhance_cards_enabled: false,
          },
          queryId: QueryIds.CREATE_COMMENT,
        };

        // 创建新的axios实例
        const commentClient = axios.create({
          baseURL: "https://x.com",
          headers: {
            authority: "x.com",
            accept: "*/*",
            "accept-language": "en-US,en;q=0.9",
            authorization:
              "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            cookie: this.headers["Cookie"],
            origin: "https://x.com",
            referer: `https://x.com/i/status/${tweetId}`,
            "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": this.headers["x-csrf-token"],
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
          },
          withCredentials: true,
          timeout: 10000,
        });

        if (this.client.defaults.httpsAgent) {
          commentClient.defaults.httpsAgent = this.client.defaults.httpsAgent;
          commentClient.defaults.proxy = false;
        }

        // 添加随机延迟
        const delay = Math.floor(Math.random() * (3000 - 1000)) + 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));

        const response = await commentClient.post(
          `/i/api/graphql/${QueryIds.CREATE_COMMENT}/CreateTweet`,
          json_data,
        );

        if (response.data.errors) {
          throw new Error(response.data.errors[0]?.message || "Unknown error");
        }

        console.log(`成功评论推文 ${tweetId}`);
        return response.data;
      } catch (error) {
        console.error("评论推文失败:", error.message);
        if (error.response) {
          console.error("错误状态:", error.response.status);
          console.error("错误数据:", error.response.data);
        }
        throw new Error(`Comment error: ${error.message}`);
      }
    });
  }

  /**
   * 批量操作方法
   * @param {string} tweetId - 推文ID
   * @param {Object} options - 操作选项
   * @param {boolean} [options.like=false] - 是否点赞
   * @param {boolean} [options.retweet=false] - 是否转发
   * @param {string} [options.comment] - 评论内容
   */
  async interact(tweetId, options = {}) {
    return this.executeWithLogin(async () => {
      const results = {
        success: true,
        operations: {},
      };

      try {
        if (options.like) {
          results.operations.like = await this.like(tweetId);
        }

        if (options.retweet) {
          results.operations.retweet = await this.retweet(tweetId);
        }

        if (options.comment) {
          results.operations.comment = await this.comment(tweetId, options.comment);
        }
      } catch (error) {
        results.success = false;
        results.error = error.message;
      }

      return results;
    });
  }
}

export default Twitter;
