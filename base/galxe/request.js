import axios from "axios";
import { v4 as uuidv4 } from 'uuid';
import { SuiWallet } from "@okxweb3/coin-sui";
import { ethers } from "ethers";
import crypto from 'crypto';

class GalxeRequester {

    constructor() {
        this.apiUrl = "https://graphigo.prd.galaxy.eco/query";
        this.deviceId = `ga-user-${Math.floor(Math.random() * 9999999999)}.${Math.floor(Date.now() / 1000)}`;
        this.authToken = null; // 保存认证token
    }

    /**
     * 检查钱包地址是否已注册 Galxe ID
     *
     * @param {string} walletAddress - 要检查的钱包地址
     * @param {object} httpsAgent - 代理配置
     * @returns {Promise<object|null>} 返回检查结果或null
     *
     * @example
     * // 返回数据格式
     * {
     *     "galxeIdExist": true
     * }
     */
    async checkGalxeId(walletAddress, httpsAgent = null) {
        // GraphQL 查询
        const query = `
            query GalxeIDExist($schema: String!) {
                galxeIdExist(schema: $schema)
            }
        `;

        // 准备请求数据
        const payload = {
            operationName: "GalxeIDExist",
            variables: {
                schema: `EVM:${walletAddress}`
            },
            query: query
        };

        try {
            const config = this.getRequestConfig(httpsAgent);
            const response = await axios.post(this.apiUrl, payload, config);

            // 检查响应
            if (response.data.errors) {
                console.error("GraphQL 错误:", response.data.errors);
                return null;
            }

            return response.data.data;

        } catch (error) {
            console.error("请求错误:", error.message);
            return null;
        }
    }

    /**
     * 检查用户名是否已被注册
     * @param {string} username - 要检查的用户名
     * @param {object} httpsAgent - 代理配置
     * @returns {Promise<boolean|null>} 返回是否已注册，发生错误时返回null
     */
    async checkUsernameExists(username, httpsAgent = null) {
        // 确保有认证token
        if (!this.authToken) {
            console.error("未找到认证token，请先登录");
            return null;
        }

        const query = `
                query UserNameExists($username: String!) {
                    userNameExists(username: $username) {
                        exists
                        errorMessage
                        __typename
                    }
                }
            `;

        const payload = {
            operationName: "UserNameExists",
            variables: {
                username: username
            },
            query: query
        };

        try {
            const config = this.getRequestConfig(httpsAgent);
            // 添加认证token
            config.headers['authorization'] = this.authToken;

            const response = await axios.post(this.apiUrl, payload, config);

            if (response.data.errors) {
                console.error("GraphQL 错误:", response.data.errors);
                return null;
            }

            const result = response.data.data?.userNameExists;
            if (result?.errorMessage) {
                console.error("检查用户名错误:", result.errorMessage);
                return null;
            }

            return result?.exists ?? null;

        } catch (error) {
            console.error("检查用户名请求错误:", error.message);
            return null;
        }
    }


    /**
     * 创建新的 Galxe 账号
     * @param {object} params - 注册参数
     * @param {string} params.address - 钱包地址
     * @param {string} params.username - 用户名
     * @param {object} httpsAgent - 代理配置
     * @returns {Promise<string|null>} 返回账号ID或null
     */
    async createNewAccount(params, httpsAgent = null) {
        // 确保有认证token
        if (!this.authToken) {
            console.error("未找到认证token，请先登录");
            return null;
        }

        const query = `
            mutation CreateNewAccount($input: CreateNewAccount!) {
                createNewAccount(input: $input)
            }
        `;

        const payload = {
            operationName: "CreateNewAccount",
            variables: {
                input: {
                    schema: `EVM:${params.address}`,
                    username: params.username,
                    socialUsername: params.username
                }
            },
            query: query
        };

        try {
            const config = this.getRequestConfig(httpsAgent);
            // 添加认证token
            config.headers['authorization'] = this.authToken;

            const response = await axios.post(this.apiUrl, payload, config);

            if (response.data.errors) {
                console.error("GraphQL 错误:", response.data.errors);
                return null;
            }

            // 保存账号ID
            const accountId = response.data.data?.createNewAccount;
            if (accountId) {
                this.accountId = accountId;
            }

            return accountId;

        } catch (error) {
            console.error("创建账号请求错误:", error.message);
            return null;
        }
    }
    /**
      * 获取登录签名信息并保存token
      * @param {object} signData - 签名相关数据
      * @param {object} httpsAgent - 代理配置
      * @returns {Promise<object|null>} 返回登录token或null
      */
    async getSignInfo(signData, httpsAgent = null) {
        console.log('开始获取登录签名信息，输入数据:', signData);

        const query = `
        mutation SignIn($input: Auth) {
            signin(input: $input)
        }
    `;
        console.log('GraphQL 查询:', query);

        const payload = {
            operationName: "SignIn",
            variables: {
                input: {
                    address: signData.address,
                    signature: signData.signature,
                    message: signData.message,
                    addressType: "EVM",
                    publicKey: signData.publicKey
                }
            },
            query: query
        };
        console.log('请求负载:', payload);

        try {
            const config = this.getRequestConfig(httpsAgent);
            console.log('请求配置:', config);

            console.log('发送请求到:', this.apiUrl);
            const response = await axios.post(this.apiUrl, payload, config);
            console.log('收到响应:', response.data);

            if (response.data.errors) {
                console.error("GraphQL 错误:", response.data.errors);
                return null;
            }

            // 保存token
            if (response.data.data?.signin) {
                this.authToken = response.data.data.signin;
                console.log('成功保存认证token:', this.authToken);
            }

            console.log('返回数据:', response.data.data);
            return response.data.data;

        } catch (error) {
            console.error("登录请求错误:", error.message);
            console.error("错误详情:", error);
            return null;
        }
    }

    /**
     * 获取当前保存的认证token
     * @returns {string|null} 返回认证token或null
     */
    getAuthToken() {
        return this.authToken;
    }

    /**
     * 手动设置认证token
     * @param {string} token - 认证token
     */
    setAuthToken(token) {
        this.authToken = token;
    }

    /**
     * 获取当前保存的账号ID
     * @returns {string|null} 返回账号ID或null
     */
    getAccountId() {
        return this.accountId;
    }

    /**
     * 手动设置账号ID
     * @param {string} id - 账号ID
     */
    setAccountId(id) {
        this.accountId = id;
    }


    async suiSign(message, sui_pk) {
        // 签名消息
        let param = {
           privateKey: sui_pk,
           data: Buffer.from(message)
       }
       let wallet = new SuiWallet();
       let sigMsg = await wallet.signMessage(param);
       console.log('生成的签名:', sigMsg);
     }

    /**
    * 生成签名数据
    * @param {object} account - 账户信息
    * @param {string} account.address - 钱包地址
    * @param {string} account.privateKey - 私钥
    * @returns {Promise<object>} 返回签名数据
    */
    async generateSignData(account) {
        try {
            console.log('开始生成签名数据，账户地址:', account.address);

            // 创建钱包实例
            const wallet = new ethers.Wallet(account.privateKey);
            console.log('已创建钱包实例');

            // 生成随机 nonce
            const nonce = this.generateNonce(16);
            console.log('生成的随机 nonce:', nonce);

            // 获取当前时间和过期时间
            const issuedAt = new Date().toISOString();
            const expirationTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
            console.log('签名时间:', issuedAt);
            console.log('过期时间:', expirationTime);

            // 构建消息
            const message = `app.galxe.com wants you to sign in with your Ethereum account:\n${account.address}\n\nSign in with Ethereum to the app.\n\nURI: https://app.galxe.com\nVersion: 1\nChain ID: 80084\nNonce: ${nonce}\nIssued At: ${issuedAt}\nExpiration Time: ${expirationTime}`;
            console.log('待签名消息:\n', message);

            // 签名消息
            const signature = await wallet.signMessage(message);
            console.log('生成的签名:', signature);

            const signData = {
                address: account.address,
                signature: signature,
                message: message,
                publicKey: "1"  // 链 ID
            };
            console.log('最终生成的签名数据:', signData);

            return signData;
        } catch (error) {
            console.error("生成签名数据失败:", error);
            throw error;
        }
    }


    /**
     * 获取请求配置
     * @private
     * @param {object} httpsAgent - 代理配置
     * @returns {object} 请求配置对象
     */
    getRequestConfig(httpsAgent) {
        // 生成唯一的 link-id
        const linkId = crypto
            .createHash('sha256')
            .update(Date.now().toString())
            .digest('hex');

        const config = {
            headers: {
                'authority': 'graphigo.prd.galaxy.eco',
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/json',
                'device-id': this.deviceId,
                'origin': 'https://app.galxe.com',
                'priority': 'u=1, i',
                'request-id': uuidv4(),
                'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'cross-site',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-unique-client-id': this.deviceId,
                'x-unique-link-id': linkId
            },
            timeout: 30000
        };

        if (httpsAgent) {
            config.httpsAgent = httpsAgent;
            config.proxy = false;
        }

        return config;
    }

    /**
     * 生成随机 nonce
     * @private
     * @param {number} length - nonce 长度
     * @returns {string} 随机 nonce
     */
    generateNonce(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**

     * 生成指定范围内的随机整数

     * @param {number} min - 最小值

     * @param {number} max - 最大值

     * @returns {number} 随机整数

     */

    getRandomInt(min, max) {

        min = Math.ceil(min);

        max = Math.floor(max);

        return Math.floor(Math.random() * (max - min + 1)) + min;

    }

    /**
     * 绑定 Sui 钱包到 Galxe 账户
     * @param {object} account - 账户信息
     * @param {string} account.address - EVM 钱包地址
     * @param {string} account.sui_addr - Sui 钱包地址
     * @param {string} account.sui_pk - Sui 私钥
     * @param {object} httpsAgent - 代理配置
     * @returns {Promise<object|null>} 返回绑定结果或null
     */
    async bindSuiWallet(account, httpsAgent = null) {
        try {
            console.log('开始绑定 SUI 钱包，账户信息:', {
                evm_address: account.address,
                sui_address: account.sui_addr,
                // 出于安全考虑，不打印私钥
                has_sui_pk: !!account.sui_pk
            });

            if (!account.sui_addr) {
                throw new Error('SUI 地址不能为空');
            }
            if (!account.sui_pk) {
                throw new Error('SUI 私钥不能为空');
            }

            let wallet = new SuiWallet();
            console.log('已创建 SUI 钱包实例');

            // 生成随机 nonce
            const nonce = this.getRandomInt(********, ********).toString();
            console.log('生成的随机 nonce:', nonce);

            // 构建消息
            const issuedAt = new Date().toISOString();
            const expirationTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
            console.log('签名时间:', issuedAt);
            console.log('过期时间:', expirationTime);

            const message = `You are updating your Galxe profile with sui address ${account.sui_addr}. Please sign the transaction to confirm.\nnonce: ${nonce}\nIssued At: ${issuedAt}\nExpiration Time: ${expirationTime}`;
            console.log('待签名消息:\n', message);


            // 签名消息
            let param = {
                privateKey: account.sui_pk,
                data: Buffer.from(message)
            }
            console.log('准备签名参数（不含私钥）:', {
                data: param.data
            });

            let sigMsg = await wallet.signMessage(param);
            console.log('生成的签名:', sigMsg);

            // 构建请求数据
            const payload = {
                operationName: "UpdateUserAddress",
                variables: {
                    input: {
                        address: account.address?.toLowerCase(),
                        addressType: "EVM",
                        updateAddress: account.sui_addr,
                        updateAddressType: "SUI",
                        sig: sigMsg,
                        sigNonce: nonce,
                        addressPublicKey: "",
                        message: message
                    }
                },
                query: `
                    mutation UpdateUserAddress($input: UpdateUserAddressInput!) {
                        updateUserAddress(input: $input) {
                            code
                            message
                            __typename
                        }
                    }
                `
            };
            console.log('GraphQL 请求负载:', payload);

            const config = this.getRequestConfig(httpsAgent);
            config.headers['authorization'] = this.authToken;
            console.log('请求配置:', config);

            console.log('发送请求到:', this.apiUrl);
            const response = await axios.post(this.apiUrl, payload, config);
            console.log('收到响应:', response.data);

            // 处理 GraphQL 错误
            if (response.data.errors) {
                const error = response.data.errors[0];
                console.error('GraphQL 错误:', error);

                // 处理特定错误类型
                if (error.extensions?.reason === 'sui_address_already_bound') {
                    return {
                        success: false,
                        message: 'SUI 地址已被绑定到其他账号，请先解绑',
                        error: error
                    };
                }

                // 处理其他错误
                return {
                    success: false,
                    message: error.message || '绑定失败',
                    error: error
                };
            }

            // 检查成功响应
            if (response.data.data?.updateUserAddress === null) {
                console.log('SUI 钱包绑定成功');
                return {
                    success: true,
                    message: 'SUI 钱包绑定成功'
                };
            }

            console.log('收到未知响应格式:', response.data);
            return {
                success: false,
                message: '未知响应格式',
                response: response.data
            };
        } catch (error) {
            console.error("绑定 SUI 钱包请求错误:", error);
            return {
                success: false,
                message: error.message || '请求发生错误',
                error: error
            };
        }
    }
}

export default GalxeRequester;
