import {
  Contract,
  formatEther,
  isHexString,
  JsonRpc<PERSON><PERSON><PERSON>,
  Wallet as EthersWallet,
} from "ethers";
import { sleep } from "../tools/common.js";

// ERC20 代币标准接口 ABI
const ERC20_ABI = [
  // 查询余额
  {
    constant: true,
    inputs: [{ name: "_owner", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "balance", type: "uint256" }],
    type: "function",
  },
  // 查询代币精度
  {
    constant: true,
    inputs: [],
    name: "decimals",
    outputs: [{ name: "", type: "uint8" }],
    type: "function",
  },
  // 查询代币符号
  {
    constant: true,
    inputs: [],
    name: "symbol",
    outputs: [{ name: "", type: "string" }],
    type: "function",
  },
];

export class WalletError extends Error {
  constructor(message, code) {
    super(message);
    this.name = "WalletError";
    this.code = code;
  }
}

export class Wallet {
  constructor(privateKey = null, rpcUrl) {
    try {
      this._validateRpcUrl(rpcUrl);
      this.rpcUrl = rpcUrl;
      this.provider = null; // Will be initialized lazily

      if (privateKey) {
        this._validatePrivateKey(privateKey);
        this.wallet = new EthersWallet(privateKey);
      }
    } catch (error) {
      throw new WalletError(`Failed to initialize wallet: ${error.message}`, "INIT_ERROR");
    }
  }

  // 私有方法：验证私钥
  _validatePrivateKey(privateKey) {
    if (!privateKey) {
      throw new WalletError("Private key is required", "INVALID_PRIVATE_KEY");
    }
    if (!isHexString(privateKey) || privateKey.length !== 66) {
      // 0x + 64 chars
      throw new WalletError("Invalid private key format", "INVALID_PRIVATE_KEY");
    }
  }

  // 私有方法：验证 RPC URL
  _validateRpcUrl(rpcUrl) {
    if (!rpcUrl) {
      throw new WalletError("RPC URL is required", "INVALID_RPC_URL");
    }
    try {
      new URL(rpcUrl);
    } catch {
      throw new WalletError("Invalid RPC URL format", "INVALID_RPC_URL");
    }
  }

  // 私有方法：创建带重试机制的 Provider
  async _createProviderWithRetry(maxRetries = 5, baseDelay = 1000) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const provider = new JsonRpcProvider(this.rpcUrl);

        // 测试网络连接以确保 provider 可用
        await provider.getNetwork();

        return provider;
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries) {
          break;
        }

        // 指数退避延迟
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.warn(`JsonRpcProvider initialization failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`);
        await sleep(delay / 1000); // sleep expects seconds
      }
    }

    throw new WalletError(
      `Failed to initialize JsonRpcProvider after ${maxRetries} attempts: ${lastError.message}`,
      "PROVIDER_INIT_ERROR"
    );
  }

  // 获取或创建 Provider
  async _getProvider() {
    if (!this.provider) {
      this.provider = await this._createProviderWithRetry();
      // 如果有钱包实例，自动连接
      if (this.wallet) {
        this.wallet = this.wallet.connect(this.provider);
      }
    }
    return this.provider;
  }

  // 私有方法：验证合约地址
  _validateContractAddress(contractAddress) {
    if (!contractAddress) {
      throw new WalletError("Contract address is required", "INVALID_CONTRACT_ADDRESS");
    }
    if (!isHexString(contractAddress) || contractAddress.length !== 42) {
      throw new WalletError("Invalid contract address format", "INVALID_CONTRACT_ADDRESS");
    }
  }

  // 获取钱包地址
  getAddress() {
    try {
      return this.wallet.address;
    } catch (error) {
      throw new WalletError(`Failed to get address: ${error.message}`, "ADDRESS_ERROR");
    }
  }

  // 获取私钥
  getPrivateKey() {
    try {
      return this.wallet.privateKey;
    } catch (error) {
      throw new WalletError(`Failed to get private key: ${error.message}`, "PRIVATE_KEY_ERROR");
    }
  }

  // 签名消息
  async signMessage(message) {
    try {
      if (!message) {
        throw new WalletError("Message is required", "INVALID_MESSAGE");
      }
      return await this.wallet.signMessage(message);
    } catch (error) {
      throw new WalletError(`Failed to sign message: ${error.message}`, "SIGN_MESSAGE_ERROR");
    }
  }

  // 签名交易
  async signTransaction(transaction) {
    try {
      if (!transaction || typeof transaction !== "object") {
        throw new WalletError("Invalid transaction object", "INVALID_TRANSACTION");
      }
      return await this.wallet.signTransaction(transaction);
    } catch (error) {
      throw new WalletError(
        `Failed to sign transaction: ${error.message}`,
        "SIGN_TRANSACTION_ERROR",
      );
    }
  }

  async sendTransaction(transaction) {
    try {
      if (!transaction || typeof transaction !== "object") {
        throw new WalletError("Invalid transaction object", "INVALID_TRANSACTION");
      }
      return await this.wallet.sendTransaction(transaction);
    } catch (error) {
      throw new WalletError(`Failed to send transaction: ${error.message}`, "SEND_TRANSACTION_ERROR");
    }
  }

  // 连接到提供者
  async connect() {
    try {
      if (!this.provider) {
        // 如果 provider 还没有初始化，使用带重试机制的创建方法
        this.provider = await this._createProviderWithRetry();
      }
      this.wallet = this.wallet.connect(this.provider);
      return this;
    } catch (error) {
      throw new WalletError(`Failed to connect wallet: ${error.message}`, "CONNECT_ERROR");
    }
  }

  // 查询余额
  async getBalance(address) {
    if (!address) {
      address = this.wallet.address;
    }
    try {
      const provider = await this._getProvider();
      return await provider.getBalance(address);
    } catch (error) {
      throw new WalletError(`Failed to get balance: ${error.message}`, "BALANCE_ERROR");
    }
  }

  async getFormattedBalance(address) {
    if (!address) {
      address = this.wallet.address;
    }
    const balance = await this.getBalance(address);
    return Number(formatEther(balance)).toFixed(4);
  }

  // 检查连接状态
  async isConnected() {
    try {
      const provider = await this._getProvider();
      await provider.getNetwork();
      return true;
    } catch {
      return false;
    }
  }

  // 查询nonce
  async getNonce(address) {
    if (!address) {
      address = this.wallet.address;
    }
    const provider = await this._getProvider();
    return await provider.getTransactionCount(address);
  }

  /**
   * 查询 ERC20 代币余额
   * @param {string} tokenAddress - 代币合约地址
   * @param {string} [address] - 要查询的地址，默认为钱包地址
   * @returns {Promise<bigint>} - 代币余额（原始值，未格式化）
   */
  async getTokenBalance(tokenAddress, address) {
    try {
      this._validateContractAddress(tokenAddress);

      if (!address) {
        address = this.wallet.address;
      }

      const provider = await this._getProvider();
      const tokenContract = new Contract(tokenAddress, ERC20_ABI, provider);

      return await tokenContract.balanceOf(address);
    } catch (error) {
      throw new WalletError(`Failed to get token balance: ${error.message}`, "TOKEN_BALANCE_ERROR");
    }
  }
}
