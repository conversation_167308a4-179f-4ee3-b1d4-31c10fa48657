import { ethers } from "ethers";
import logger from "../tools/logger.js";

export async function getRecentTxCount(address, rpc) {
  try {
    const provider = new ethers.JsonRpcProvider(rpc);
    const currentBlock = await provider.getBlockNumber();
    const oneDayBlocks = Math.floor((24 * 60 * 60) / 12);
    const yesterdayBlock = currentBlock - oneDayBlocks;

    const currentNonce = await provider.getTransactionCount(address, "latest");
    const yesterdayNonce = await provider.getTransactionCount(address, yesterdayBlock);

    const txCount = currentNonce - yesterdayNonce;
    return Math.max(0, txCount);
  } catch (err) {
    logger.error(`获取钱包 ${address} 当天交易数量失败: ${err.message}`);
    throw err;
  }
}

/**
 * 构建并发送以太坊交易的通用方法
 * @param {string} rpc - RPC节点地址
 * @param {string} privateKey - 钱包私钥
 * @param {string} contractAddress - 合约地址
 * @param {string} inputData - 交易输入数据
 * @param {string|number} value - 交易金额(单位: wei)
 * @returns {Promise<string>} 交易哈希
 */
export async function buildAndSendTransaction(rpc, privateKey, contractAddress, inputData, value = '0') {
  try {
      const provider = new ethers.JsonRpcProvider(rpc);
      const wallet = new ethers.Wallet(privateKey, provider);
      const fromAddress = wallet.address;

      // 并行获取所需数据
      const [
          feeData,
          transactionNonce,
          network
      ] = await Promise.all([
          provider.getFeeData(),
          provider.getTransactionCount(fromAddress, "pending"),
          provider.getNetwork()
      ]);

      // 根据网络是否支持EIP-1559设置fee参数
      let fee = feeData.maxFeePerGas ? {
          maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
          maxFeePerGas: feeData.maxFeePerGas,
          type: 2  // EIP-1559交易
      } : {
          gasPrice: feeData.gasPrice,
          type: 0  // 传统交易
      };

      // 估算gasLimit
      const gasLimit = await provider.estimateGas({
          from: fromAddress,
          to: contractAddress,
          value: ethers.parseUnits(value.toString(), 'wei'),
          data: inputData,
          nonce: transactionNonce,
      });

      // 构建交易对象
      const transaction = {
          from: fromAddress,
          to: contractAddress,
          value: ethers.parseUnits(value.toString(), 'wei'),
          nonce: transactionNonce,
          chainId: network.chainId,
          data: inputData,
          gasLimit: gasLimit * 12n / 10n, // gasLimit增加20%作为安全边际
          ...fee  // 展开fee对象，包含type和对应的gas价格设置
      };

      const tx = await wallet.sendTransaction(transaction);

      return tx;
  } catch (error) {
      throw new Error(`交易发送失败: ${error.message}`);
  }
}
