import { ethers } from 'ethers';
import logger from '../tools/logger.js';
import SecureEncryption from '../tools/secure-encryption.js';
import { Wallet } from './wallet.js';

class Transaction {
    constructor(provider, privateKey, currency) {
        this.provider = provider;
        this.privateKey = privateKey;
        this.currency = currency;
    }

    async getBalance() {
        const wallet = new ethers.Wallet(this.privateKey, this.provider);
        const balance = await this.provider.getBalance(wallet.address);
        const formattedBalance = ethers.formatEther(balance);
        return formattedBalance;
    }

    /**
     * 获取当前网络Gas价格信息
     * @param {ethers.Provider} provider
     * @returns {Promise<{baseFee: string, maxFeePerGas: string, maxPriorityFeePerGas: string}>}
     */
    async getGasPrice(multiplier = 1.0) {
        try {
            // 获取最新区块
            const block = await this.provider.getBlock('latest');
            // 获取基础费用
            const baseFee = block.baseFeePerGas;

            // 小费
            const maxPriorityFeePerGas = ethers.parseUnits('0.1', 'gwei');

            // 建议的 maxFeePerGas （基础费用 * multiplier + 小费）
            const multiplierBigInt = BigInt(Math.floor(multiplier * 1000)) / 1000n;
            const maxFeePerGas = (baseFee * multiplierBigInt) + maxPriorityFeePerGas;

            return {
                baseFee: ethers.formatUnits(baseFee, 'gwei'),
                maxPriorityFeePerGas: ethers.formatUnits(maxPriorityFeePerGas, 'gwei'),
                maxFeePerGas: ethers.formatUnits(maxFeePerGas, 'gwei')
            };
        } catch (error) {
            console.error('获取Gas价格失败:', error.message);
            throw error;
        }
    };


    async transferFunds(toAddress, amount) {
        const wallet = new ethers.Wallet(this.privateKey, this.provider);

        try {
            // 1. 获取当前余额
            const balance = await this.provider.getBalance(wallet.address);
            const formattedBalance = ethers.formatEther(balance);
            console.log(`钱包地址: ${wallet.address}`);
            console.log(`余额: ${formattedBalance} ${this.currency}`);

            // 2. 估算 gas 费用
            const tx = {
                to: toAddress,
                value: ethers.parseEther(amount.toString())
            };
            const gasLimit = await this.provider.estimateGas(tx);
            const gasPrice = await this.getGasPrice();
            const gasCost = BigInt(gasLimit) * ethers.parseUnits(gasPrice.maxFeePerGas, 'gwei');

            // 3. 检查总费用
            const totalNeeded = ethers.parseEther(amount.toString()) + gasCost;

            if (balance < totalNeeded) {
                logger.error(`余额不足：
                    当前余额: ${ethers.formatEther(balance)} ${this.currency}
                    需要金额: ${ethers.formatEther(totalNeeded)} ${this.currency}
                    (转账: ${amount} + Gas: ${ethers.formatEther(gasCost)})`);
                return false;
            }

            // 4. 发送交易
            tx.gasLimit = gasLimit;
            tx.maxFeePerGas = ethers.parseUnits(gasPrice.maxFeePerGas, 'gwei');
            tx.maxPriorityFeePerGas = ethers.parseUnits(gasPrice.maxPriorityFeePerGas, 'gwei');

            const transaction = await wallet.sendTransaction(tx);
            const receipt = await transaction.wait();

            logger.success(`转账成功：${amount} ${this.currency} 从 ${wallet.address} 到 ${toAddress}`);
            return receipt.status === 1;

        } catch (error) {
            console.error('获取余额失败:', error.message);
            return false;
        }
    }

}

export class BaseTransfer {
    constructor(config) {
        this._validateConfig(config);

        this.RPC_URL = config.RPC_URL;
        this.GAS_LIMIT = 21000;
        this.currency = config.currency;

        try {
            this.provider = new ethers.JsonRpcProvider(this.RPC_URL);
        } catch (error) {
            throw new Error(`无效的 RPC URL: ${error.message}`);
        }
    }

    _validateConfig(config) {
        const requiredFields = [
            'RPC_URL',
            'currency',
        ];

        // 检查必需字段
        for (const field of requiredFields) {
            if (!config[field]) {
                throw new Error(`缺少必需的配置项: ${field}`);
            }
        }

        // // 验证数值类型和范围
        // if (typeof config.MIN_BALANCE !== 'number' || config.MIN_BALANCE <= 0) {
        //     throw new Error('MIN_BALANCE 必须是大于0的数字');
        // }
        // if (typeof config.KEEP_BALANCE_MIN !== 'number' || config.KEEP_BALANCE_MIN <= 0) {
        //     throw new Error('KEEP_BALANCE_MIN 必须是大于0的数字');
        // }
        // if (typeof config.KEEP_BALANCE_MAX !== 'number' || config.KEEP_BALANCE_MAX <= 0) {
        //     throw new Error('KEEP_BALANCE_MAX 必须是大于0的数字');
        // }
        // if (config.KEEP_BALANCE_MIN >= config.KEEP_BALANCE_MAX) {
        //     throw new Error('KEEP_BALANCE_MIN 必须小于 KEEP_BALANCE_MAX');
        // }
        // if (config.KEEP_BALANCE_MIN > config.MIN_BALANCE) {
        //     throw new Error('KEEP_BALANCE_MIN 必须小于或等于 MIN_BALANCE');
        // }
    }


    validateTransactionData(data) {
        return data.to_address && data.private_key && data.index &&
            ethers.isAddress(data.to_address) &&
            typeof data.private_key === 'string' && data.private_key.length === 64;
    }

    /**
     * 计算转账金额
     * @param {string} pk - 私钥
     * @param {number} index - 索引
     * @param {number} gasBuffer - 气体缓冲区, 默认1.0
     * @returns {Promise<number>} 转账金额
     */
    async _calculateTransferAmount(pk, index, gasBuffer = 1.0) {
        const wallet = new Wallet(pk, this.RPC_URL);
        const balance = await wallet.getBalance() || 0;
        const formattedAmount = Number(ethers.formatEther(balance)).toFixed(8);

        logger.info(`[${index}] ${wallet.wallet.address} 余额: ${formattedAmount} ${this.currency}`);

        if (balance <= 0) {
            throw new Error(`[${index}] ${wallet.wallet.address} 余额不足`);
        }

        const gasPrice = await this.getGasPrice();
        const maxFeePerGas = ethers.parseUnits(gasPrice.maxFeePerGas, 'gwei');
        const estimatedGasFee = maxFeePerGas * BigInt(this.GAS_LIMIT);
        const withBuffer = (estimatedGasFee * BigInt(gasBuffer * 100)) / BigInt(100);
        logger.info(`[${index}] ${wallet.wallet.address} 需要支付的 gas: ${ethers.formatEther(withBuffer)} ${this.currency}`);
        if (formattedAmount <= withBuffer) {
            console.log(`[${index}] ${wallet.wallet.address} 余额不足支付 gas，需要 ${ethers.formatEther(withBuffer)} ${this.currency}`);
            return false;
        }

        const formattedBuffer = Number(ethers.formatEther(withBuffer));
        // 确保数字计算的精确性，保留 8 位小数
        const transferAmount = Number((formattedAmount - formattedBuffer).toFixed(8));
        console.log(`[${index}] ${wallet.wallet.address} 转移金额(保留gas): ${transferAmount} ${this.currency}`);

        if (transferAmount <= 0) {
            console.log(`[${index}] ${wallet.wallet.address} 可转金额太小，不足以支付 gas`);
            return false;
        }

        return transferAmount;
        // }

        // if (formattedAmount < this.MIN_BALANCE) {
        //     console.log(`[${index}] ${wallet.address} Balance: ${amount} ${this.currency}, 余额不足`);
        //     return false;
        // }

        // if (formattedAmount <= this.KEEP_BALANCE_MAX) {
        //     console.log(`[${index}] ${wallet.address} Balance: ${formattedAmount} ${this.currency}, 余额太小，跳过`);
        //     return false;
        // }

        // const keepBalance = Number((this.KEEP_BALANCE_MIN +
        //     Math.random() * (this.KEEP_BALANCE_MAX - this.KEEP_BALANCE_MIN)).toFixed(8));
        // console.log(`[${index}] ${wallet.address} Keep Balance: ${keepBalance} ${this.currency}`);

        // const transferAmount = Number((formattedAmount - keepBalance).toFixed(8));
        // return transferAmount;

    }

    async _transferFunds(address, pk, index, amount = 0, gasBuffer = 1.0) {
        try {
            if (!ethers.isAddress(address)) {
                throw new Error(`无效的地址格式: ${address}`);
            }

            // 计算转账金额
            let transferAmount = 0
            if (amount > 0) {
                transferAmount = amount;
            } else {
                transferAmount = await this._calculateTransferAmount(pk, index, gasBuffer);
            }

            if (transferAmount <= 0) {
                console.log(`[${index}] ${address} 转账金额为负或零，跳过`);
                return false;
            }

            console.log(`[${index}] ${address} 开始转账, 转账金额: ${transferAmount} ${this.currency}`);
            const transaction = new Transaction(this.provider, pk, this.currency);
            const result = await transaction.transferFunds(address, transferAmount);

            await new Promise(resolve => setTimeout(resolve, 1000));

            if (result) {
                logger.success(`[${index}] ${address} 转账成功`);
                const waitTime = 10000 + Math.random() * 20000;
                await new Promise(resolve => setTimeout(resolve, waitTime));
                return true;
            } else {
                logger.error(`[${index}] ${address} 转账失败`);
                return false;
            }

        } catch (error) {
            logger.error(`[${index}] ${address} 转账失败:`, error.message);
            return false;
        }
    }



    /**
     * 获取当前网络Gas价格信息
     * @param {ethers.Provider} provider
     * @returns {Promise<{baseFee: string, maxFeePerGas: string, maxPriorityFeePerGas: string}>}
     */
    async getGasPrice(multiplier = 1.0) {
        try {
            // 获取最新区块
            const block = await this.provider.getBlock('latest');
            // 获取基础费用
            const baseFee = block.baseFeePerGas;

            // 小费
            const maxPriorityFeePerGas = ethers.parseUnits('0.1', 'gwei');

            // 建议的 maxFeePerGas （基础费用 * multiplier + 小费）
            const multiplierBigInt = BigInt(Math.floor(multiplier * 1000)) / 1000n;
            const maxFeePerGas = (baseFee * multiplierBigInt) + maxPriorityFeePerGas;

            return {
                baseFee: ethers.formatUnits(baseFee, 'gwei'),
                maxPriorityFeePerGas: ethers.formatUnits(maxPriorityFeePerGas, 'gwei'),
                maxFeePerGas: ethers.formatUnits(maxFeePerGas, 'gwei')
            };
        } catch (error) {
            console.error('获取Gas价格失败:', error.message);
            throw error;
        }
    };


    async processTransactions(transactions, amount, gasBuffer = 1.0) {
        try {
            console.log('开始处理转账...');
            console.log(`共读取到 ${transactions.length} 条转账记录`);

            let successCount = 0;
            let failCount = 0;

            const secureEncryption = new SecureEncryption();

            for (const txData of transactions) {
                const { to_address, index } = txData;
                let private_key = txData.private_key;
                const isEncrypted = await secureEncryption.isEncrypted(private_key);
                if (isEncrypted) {
                    private_key = await secureEncryption.decrypt(private_key);
                }

                if (!to_address || !private_key) {
                    logger.error(`[${index}] 地址或私钥为空，跳过`);
                    failCount++;
                    continue;
                }

                const success = await this._transferFunds(to_address, private_key, index, amount, gasBuffer);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            console.log('\n转账统计:');
            console.log(`总计: ${transactions.length}`);
            console.log(`成功: ${successCount}`);
            console.log(`失败: ${failCount}`);

        } catch (error) {
            console.error('程序执行错误:', error.message);
            throw error; // 向上传递错误
        }
    }


    async processTransaction(walletData, amount, gasBuffer = 1.0) {
        try {
            const { to_address, index } = walletData;

            let private_key = walletData.private_key;
            const secureEncryption = new SecureEncryption();
            const isEncrypted = await secureEncryption.isEncrypted(private_key);
            if (isEncrypted) {
                private_key = await secureEncryption.decrypt(private_key);
            }
            if (!to_address || !private_key) {
                logger.error(`[${index}] 地址或私钥为空，跳过`);
                return false;
            }
            const success = await this._transferFunds(to_address, private_key, index, amount, gasBuffer);
            if (success) {
                return true;
            } else {
                return false;
            }
        } catch (error) {
            console.error('程序执行错误:', error.message);
            throw error; // 向上传递错误
        }
    }
}






