import { ethers } from 'ethers';
import logger from '../tools/logger.js';


/**
 * 铭文类
 */
export default class Inscription {
  /**
   *
   * @param {*} private_key 私钥
   * @param {*} rpc rpc
   * @param {*} maxFee 最大gas费
   */
  constructor(private_key, rpc, maxFee) {
    if (!private_key || !rpc) {
      throw new Error('缺少必要参数');
    }

    this.maxFee = maxFee;

    // 初始化 provider 和 wallet
    this.provider = new ethers.JsonRpcProvider(rpc);
    this.wallet = new ethers.Wallet(private_key, this.provider);
    this.address = this.wallet.address;
  }


  /**
   * 检查 gas 费用
   * @returns {Promise<object>} gas 费用数据
   */
  async _checkGasFee() {
    while (true) {
      const feeData = await this.provider.getFeeData();

      const maxFee = parseFloat(ethers.formatUnits(feeData.maxFeePerGas || feeData.gasPrice, "gwei"));
      if (maxFee > this.maxFee) {
        logger.info(`当前 gas ${maxFee} gwei 高于限制 ${this.maxFee} gwei`);
        await new Promise(resolve => setTimeout(resolve, 10000));
      } else {
        return feeData;
      }
    }
  }

  /**
   * 执行 mint 操作
   * @returns {Promise<object>} 交易结果
   */
  async mint(p, tick, id, amt) {
    try {
      await this._checkGasFee();
      let data = { "p": p, "op": "mint", "tick": tick, "id": id.toString(), "amt": amt }
      let _tx = {
        to: this.address,
        value: 0,
        data: '0x' + Buffer.from('data:,' + JSON.stringify(data), 'utf-8').toString('hex')
      }

      const tx = await this.wallet.sendTransaction(_tx)
      try {
        const receipt = await tx.wait()
        return receipt;
      } catch (error) {
        logger.error(`Mint 失败: ${error.message}`);
        if (error.data) logger.error("错误数据:", error.data);
        if (error.transaction) logger.error("相关交易:", error.transaction);
        return null;
      }

    } catch (err) {
      logger.error(`Mint 失败: ${err.message}`);
      if (err.data) logger.error("错误数据:", err.data);
      if (err.transaction) logger.error("相关交易:", err.transaction);
      throw err;
    }

  }

}
