import { ethers } from 'ethers';
import logger from '../tools/logger.js';


/**
 * NFT 类
 */
export default class NFT {
    /**
     *
     * @param {*} private_key 私钥
     * @param {*} contract_address 合约地址
     * @param {*} rpc rpc
     * @param {*} abi 合约abi
     * @param {*} maxFee 最大gas费
     * @param {*} amount mint数量
     * @param {*} tokenId ERC1155的tokenId
     */
    constructor(private_key, contract_address, rpc, abi, maxFee, amount = 1, tokenId = 1) {
        if (!private_key || !contract_address || !rpc || !abi) {
            throw new Error('缺少必要参数');
        }

        this.amount = amount;
        this.maxFee = maxFee;
        this.tokenId = tokenId;

        // 初始化 provider 和 wallet
        this.provider = new ethers.JsonRpcProvider(rpc);
        this.wallet = new ethers.Wallet(private_key, this.provider);
        this.contract = new ethers.Contract(contract_address, abi, this.wallet);
        this.address = this.wallet.address;
    }

    /**
     * 检测合约是否为 ERC1155
     * @returns {Promise<boolean>}
     */
    async isERC1155() {
        try {
            // 检查是否支持 ERC1155 的 interface ID
            const supportsERC1155 = await this.contract.supportsInterface('0xd9b67a26');
            return supportsERC1155;
        } catch (err) {
            return false;
        }
    }

    /**
     * 检测合约是否为 ERC721
     * @returns {Promise<boolean>}
     */
    async isERC721() {
        try {
            // 检查是否支持 ERC721 的 interface ID
            const supportsERC721 = await this.contract.supportsInterface('0x80ac58cd');
            return supportsERC721;
        } catch (err) {
            return false;
        }
    }

    /**
     * 检查 gas 费用
     * @returns {Promise<object>} gas 费用数据
     */
    async _checkGasFee() {
        while (true) {
            const feeData = await this.provider.getFeeData();
            // logger.info("当前网络 gas 费用:", {
            //     gasPrice: ethers.formatUnits(feeData.gasPrice, "gwei"),
            //     maxFeePerGas: feeData.maxFeePerGas ? ethers.formatUnits(feeData.maxFeePerGas, "gwei") : null,
            //     maxPriorityFeePerGas: feeData.maxPriorityFeePerGas ? ethers.formatUnits(feeData.maxPriorityFeePerGas, "gwei") : null
            // });

            const maxFee = parseFloat(ethers.formatUnits(feeData.maxFeePerGas || feeData.gasPrice, "gwei"));
            if (maxFee > this.maxFee) {
                logger.info(`当前 gas ${maxFee} gwei 高于限制 ${this.maxFee} gwei`);
                await new Promise(resolve => setTimeout(resolve, 10000));
            } else {
                return feeData;
            }
        }
    }

    /**
     * 执行 mint 操作
     * @returns {Promise<object>} 交易结果
     */
    async mint() {
        try {
            logger.info(`开始 mint NFT, 地址: ${this.wallet.address}`);

            // 检查 gas 费用
            const feeData = await this._checkGasFee();

            // 获取当前余额
            const balance = await this.provider.getBalance(this.wallet.address);

            // 动态获取 maxPriorityFeePerGas，降低一点
            const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas
                ? (feeData.maxPriorityFeePerGas * 8n / 10n)  // 降低到80%
                : ethers.parseUnits('0.5', "gwei"); // 降低默认值

            let fee = feeData.maxFeePerGas ? {
                maxPriorityFeePerGas,
                maxFeePerGas: feeData.maxFeePerGas * 8n / 10n, // 降低到80%
                type: 2
            } : {
                gasPrice: feeData.gasPrice * 8n / 10n, // 降低到80%
                type: 0
            };

            // 先估算 gas 限制
            try {
                const gasEstimate = await this.contract.mint.estimateGas(this.amount, fee);
                fee.gasLimit = gasEstimate * 11n / 10n;  // 增加 10% buffer

                logger.info(`预估 gas limit: ${gasEstimate}, 实际使用: ${fee.gasLimit}`);
            } catch (error) {
                logger.warn(`预估 gas 失败: ${error.message}, 使用固定 gas limit`);
                fee.gasLimit = 100000n;  // 使用更小的固定值
            }

            // 计算并显示预估费用
            const maxFeeGwei = fee.maxFeePerGas
                ? ethers.formatUnits(fee.maxFeePerGas, 'gwei')
                : ethers.formatUnits(fee.gasPrice, 'gwei');
            const estimatedCost = ethers.formatEther(fee.gasLimit * (fee.maxFeePerGas || fee.gasPrice));
            logger.info(`Gas Price: ${maxFeeGwei} gwei, 预估最大费用: ${estimatedCost} ETH`);

            // 计算总费用
            const totalCost = fee.maxFeePerGas
                ? (fee.maxFeePerGas * fee.gasLimit)
                : (fee.gasPrice * fee.gasLimit);

            // 检查余额是否足够
            if (balance < totalCost) {
                throw new Error(`余额不足，当前余额: ${ethers.formatEther(balance)} ETH, 需要: ${ethers.formatEther(totalCost)} ETH`);
            }

            logger.info(`预估 gas: ${fee.gasLimit}, 最大费用: ${ethers.formatEther(totalCost)} ETH`);

            // 发送交易
            const tx = await this.contract.mint(this.amount, fee);

            try {
                const receipt = await tx.wait();
                logger.success(`Mint 成功, 交易哈希: ${receipt.hash}`);
                return receipt;
            } catch (error) {
                logger.error(`Mint 失败: ${error.message}`);
                if (error.data) logger.error("错误数据:", error.data);
                if (error.transaction) logger.error("相关交易:", error.transaction);
                return null;
            }

        } catch (err) {
            logger.error(`Mint 失败: ${err.message}`);
            if (err.data) logger.error("错误数据:", err.data);
            if (err.transaction) logger.error("相关交易:", err.transaction);
            throw err;
        }
    }

    /**
       * 查询 NFT 余额，自动识别 ERC721 或 ERC1155
       * @returns {Promise<bigint>} NFT 余额
       */
    async getBalance() {
        try {
            let balance;
            const isERC1155 = await this.isERC1155();
            const isERC721 = await this.isERC721();

            if (isERC1155) {
                // ERC1155 余额查询
                balance = await this.contract.balanceOf(this.address, this.tokenId);
                logger.info(`[ERC1155] 地址 ${this.address} 持有 TokenID ${this.tokenId} 数量: ${balance.toString()}`);
            } else if (isERC721) {
                // ERC721 余额查询
                balance = await this.contract.balanceOf(this.address);
                logger.info(`[ERC721] 地址 ${this.address} 持有数量: ${balance.toString()}`);
            } else {
                throw new Error('不支持的 NFT 标准');
            }

            return balance;
        } catch (err) {
            logger.error(`查询余额失败: ${err.message}`);
            throw err;
        }
    }

    /**
       * 批量查询 ERC1155 NFT 余额
       * @param {number[]} tokenIds NFT的ID数组
       * @returns {Promise<bigint[]>} NFT 余额数组
       */
    async getBalanceBatch(tokenIds) {
        try {
            const isERC1155 = await this.isERC1155();
            if (!isERC1155) {
                throw new Error('该合约不支持 ERC1155 批量查询');
            }

            const accounts = Array(tokenIds.length).fill(this.address);
            const balances = await this.contract.balanceOfBatch(accounts, tokenIds);

            tokenIds.forEach((id, index) => {
                logger.info(`地址 ${this.address} 持有 TokenID ${id} 数量: ${balances[index].toString()}`);
            });

            return balances;
        } catch (err) {
            logger.error(`批量查询余额失败: ${err.message}`);
            throw err;
        }
    }
}
