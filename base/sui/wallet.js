import fs from "fs/promises";
import { Ed25519Keypair } from "@mysten/sui.js/keypairs/ed25519";
import { mnemonicToSeed, generateMnemonic } from "@scure/bip39";
import { wordlist } from "@scure/bip39/wordlists/english";
import axios from "axios";
import logger from "../tools/logger.js";


class SuiWallet {
  constructor() {}

  /**
   * 根据助记词获取私钥跟钱包地址
   * @param {*} mnemonic
   * @returns
   */
  async getPrivateKeyAndAddress(mnemonic) {
    const keypair = Ed25519Keypair.deriveKeypair(mnemonic);
    const privateKey = keypair.getSecretKey();
    const address = keypair.getPublicKey().toSuiAddress();
    return { privateKey, address };
  }

  /**
   * 生成指定数量的钱包
   * @param {number} count - 要生成的钱包数量
   * @param {string} outputFile - 保存钱包信息的文件路径
   * @returns {Promise<Array<Object>>} 返回生成的钱包列表
   */
  async createWallets(count, outputFile) {
    const wallets = []; // 清空现有钱包列表

    for (let i = 0; i < count; i++) {
      const mnemonic = generateMnemonic(wordlist, 256);
      const seed = await mnemonicToSeed(mnemonic);

      const keypair = Ed25519Keypair.fromSecretKey(seed.slice(0, 32));
      const privateKey = keypair.getSecretKey();
      const address = keypair.getPublicKey().toSuiAddress();

      wallets.push({
        index: i + 1,
        mnemonic,
        privateKey,
        address,
      });

      console.log(`已生成第 ${i + 1} 个钱包`);
    }

    await this._saveToFile(wallets, outputFile);
    console.log(`\n✅ 成功生成 ${count} 个钱包`);
    return wallets;
  }

  async checkBalances(addresses) {
    logger.info(`开始查询 ${addresses.length} 个地址的余额`);

    const results = [];
    const zeroBalanceAddresses = [];

    for (const address of addresses) {
      const result = await this.checkBalance(address);
      if (result.success) {
        logger.success(`地址: ${address} | 余额: ${result.balanceInSui} SUI`);
        if (result.balance === "0") {
          zeroBalanceAddresses.push(address);
        }
      } else {
        logger.error(`地址: ${address} | 查询失败: ${result.error}`);
      }
      results.push(result);

      await new Promise((resolve) => setTimeout(resolve, 200));
    }

    // 保存余额为0的地址
    if (zeroBalanceAddresses.length > 0) {
      await fs.writeFile("zero_balance_addresses.txt", zeroBalanceAddresses.join("\n"));
      logger.info(
        `已将 ${zeroBalanceAddresses.length} 个余额为0的地址保存到 zero_balance_addresses.txt`,
      );
    }

    // 保存完整查询结果
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    await fs.writeFile(`balance_check_${timestamp}.json`, JSON.stringify(results, null, 2));

    // 统计信息
    const totalBalance = results.reduce((sum, r) => sum + Number(r.balance), 0);
    const totalBalanceInSui = totalBalance / 1000000000;
    const successCount = results.filter((r) => r.success).length;
    const zeroBalanceCount = zeroBalanceAddresses.length;
    const nonZeroBalanceCount = results.filter((r) => r.balance !== "0").length;

    logger.info("\n=== 统计信息 ===");
    logger.info(`总地址数: ${addresses.length}`);
    logger.info(`成功查询: ${successCount}`);
    logger.info(`余额为0: ${zeroBalanceCount}`);
    logger.info(`余额非0: ${nonZeroBalanceCount}`);
    logger.info(`总余额: ${totalBalanceInSui.toFixed(4)} SUI`);
    logger.info(`平均余额: ${(totalBalanceInSui / successCount).toFixed(4)} SUI`);
  }

  // 私有方法，用于保存钱包信息到文件
  async _saveToFile(wallets, outputFile) {
    const header = "index,mnemonic,private_key,address\n";

    let content = header;
    wallets.forEach((wallet) => {
      content += `${wallet.index},"${wallet.mnemonic}","${wallet.privateKey}","${wallet.address}"\n`;
    });

    await fs.writeFile(outputFile, content, "utf8");
  }

  // 更新查询单个地址余额的私有方法
  async checkBalance(address) {
    try {
      const { data } = await axios.post("https://rpc-testnet.suiscan.xyz/", {
        jsonrpc: "2.0",
        id: 1,
        method: "suix_getBalance",
        params: [address, "0x2::sui::SUI"],
      });

      const balance = data.result?.totalBalance || "0";
      const balanceInSui = Number(balance) / 1000000000;

      return {
        address,
        balance,
        balanceInSui: balanceInSui.toFixed(4),
        success: true,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        address,
        balance: "0",
        balanceInSui: "0",
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

export default SuiWallet;




