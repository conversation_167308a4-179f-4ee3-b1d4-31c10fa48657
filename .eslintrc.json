{"root": true, "env": {"node": true, "browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "plugins": ["prettier"], "rules": {"no-console": "off", "no-debugger": "off", "prettier/prettier": "error", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-var": "error", "prefer-const": "error", "no-constant-condition": ["error", {"loops": false}]}}