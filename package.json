{"name": "web3-tools", "version": "1.0.0", "type": "module", "dependencies": {"@binance/connector": "^3.6.1", "@faker-js/faker": "^9.5.0", "@iarna/toml": "^2.2.5", "@mysten/sui.js": "^0.54.1", "@okxweb3/coin-sui": "^1.1.0", "@scure/bip39": "^1.5.1", "@solana/web3.js": "^1.98.0", "@uniswap/sdk-core": "^7.6.0", "@uniswap/v2-sdk": "^4.14.0", "axios": "^1.7.9", "bs58": "^6.0.0", "chalk": "^5.3.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "ed25519-hd-key": "^1.3.0", "ethers": "^6.13.4", "fake-useragent": "^1.0.1", "got": "^14.4.5", "https-proxy-agent": "^7.0.6", "moment": "^2.30.1", "node-fetch": "^3.3.2", "save": "^2.9.0", "undici": "^7.2.0", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "scripts": {"lint": "eslint . --ext .js,.jsx", "lint:fix": "eslint . --ext .js,.jsx --fix", "format": "prettier --write \"**/*.{js,jsx,json,md}\"", "check-format": "prettier --check \"**/*.{js,jsx,json,md}\""}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "prettier": "^2.0.0"}}