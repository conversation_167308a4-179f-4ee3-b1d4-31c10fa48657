import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './example/somnia', // 指定测试目录
  testMatch: ['demo.js'], // 匹配 demo.js 文件
  fullyParallel: true,
  reporter: 'html', // 生成 HTML 报告
  use: {
    // baseURL: 'https://your-web3-website.com', // 可选：设置基础 URL
    headless: false, // 显示浏览器，便于调试
    // viewport: { width: 1280, height: 720 },
    actionTimeout: 0,
    ignoreHTTPSErrors: true,
  },
  projects: [
    {
      name: 'chromium',
      use: { browserName: 'chromium' },
    },
  ],
});