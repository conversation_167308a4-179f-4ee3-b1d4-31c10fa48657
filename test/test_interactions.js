import Twitter from "../base/social/twitter.js";

async function testInteractions() {
  try {
    console.log("=== 开始测试Twitter交互功能 ===\n");

    const twitter = new Twitter({
      twitter_auth_token: "推特token",
      proxy: "http://127.0.0.1:10809",
    });

    // 1. 测试登录
    console.log("1. 测试登录...");
    const loginResult = await twitter.login();
    if (!loginResult) {
      console.log("❌ 登录失败");
      return;
    }
    console.log("✅ 登录成功");

    // 添加随机延迟，模拟人工操作
    const initialDelay = Math.floor(Math.random() * (5000 - 2000)) + 2000;
    console.log(`等待 ${initialDelay / 1000} 秒...`);
    await new Promise((resolve) => setTimeout(resolve, initialDelay));

    // 2. 获取个人信息
    console.log("\n2. 获取个人信息...");
    const myScreenName = await twitter.getMyProfileInfo();
    console.log("✅ 获取成功，用户名:", myScreenName);

    // 再次添加随机延迟
    const preWriteDelay = Math.floor(Math.random() * (4000 - 1000)) + 1000;
    console.log(`模拟输入中，等待 ${preWriteDelay / 1000} 秒...`);
    await new Promise((resolve) => setTimeout(resolve, preWriteDelay));

    // 3. 发送测试推文
    const randomEmojis = ["😊", "👋", "🌟", "💫", "✨"];
    const randomEmoji = randomEmojis[Math.floor(Math.random() * randomEmojis.length)];
    const tweetText = `Hello Twitter! Just testing something ${randomEmoji} #Testing ${Date.now()
      .toString()
      .slice(-4)}`;

    console.log("\n3. 尝试发送推文...");
    console.log("推文内容:", tweetText);

    // 模拟输入完成后的延迟
    const postWriteDelay = Math.floor(Math.random() * (3000 - 1000)) + 1000;
    console.log(`准备发送，等待 ${postWriteDelay / 1000} 秒...`);
    await new Promise((resolve) => setTimeout(resolve, postWriteDelay));

    let tweetUrl;
    try {
      tweetUrl = await twitter.createTweet(tweetText);
      console.log("✅ 推文发送成功");
      console.log("推文链接:", tweetUrl);

      // 等待一段时间再进行互动
      const interactionDelay = Math.floor(Math.random() * (4000 - 2000)) + 2000;
      console.log(`等待 ${interactionDelay / 1000} 秒后进行互动...`);
      await new Promise((resolve) => setTimeout(resolve, interactionDelay));

      // 4. 测试批量互动
      if (tweetUrl) {
        const tweetId = tweetUrl.split("/").pop();
        console.log("\n4. 测试批量互动...");
        const randomComments = [
          "This is interesting! 👍",
          "Great point! 💡",
          "Thanks for sharing! 🙌",
          "Awesome! 🌟",
          "Love this! ❤️",
        ];

        const interactionResult = await twitter.interact(tweetId, {
          like: true,
          retweet: Math.random() > 0.5,
          comment: randomComments[Math.floor(Math.random() * randomComments.length)],
        });

        console.log("✅ 批量互动结果:", JSON.stringify(interactionResult, null, 2));
      }

      // 等待一段时间再验证
      const verifyDelay = Math.floor(Math.random() * (4000 - 2000)) + 2000;
      console.log(`等待 ${verifyDelay / 1000} 秒后验证...`);
      await new Promise((resolve) => setTimeout(resolve, verifyDelay));

      // 5. 验证推文
      console.log("\n5. 验证推文...");
      const foundTweet = await twitter.findPostedTweet((text) => text.includes(tweetText));

      if (foundTweet) {
        console.log("✅ 成功找到发送的推文");
        console.log("验证链接:", foundTweet);
      } else {
        console.log("❌ 未找到发送的推文");
      }
    } catch (error) {
      console.error("❌ 操作失败:", error.message);
      if (error.message.includes("duplicate")) {
        console.log("提示: 推文内容重复");
      }
      throw error;
    }
  } catch (error) {
    console.error("\n❌ 测试过程出错:", error.message);
    if (error.response) {
      console.error("错误状态:", error.response.status);
      console.error("错误数据:", error.response.data);
    }
    if (error.code === "ECONNABORTED") {
      console.error("连接超时，请检查网络状态和代理设置");
    }
  }
}

// 运行测试
testInteractions().catch(console.error);
