# 最大并发数
MAX_CONCURRENT = 1

# 重试延迟时间（毫秒）
RETRY_DELAY = 3000

# 随机延迟范围（毫秒）
RANDOM_DELAY = 2000

# 代理地址
PROXY_URL = "默认代理地址"

# 最小延迟时间（秒）
MIN_DELAY_SECONDS = 10
# 最大延迟时间（秒）
MAX_DELAY_SECONDS = 30

# nocaptcha key
NOCAPTCHA_KEY = "nocaptcha key"

# 每个task之间的间隔 最小10秒，最大30秒
TASK_INTERVAL_MIN_SECONDS = 10
TASK_INTERVAL_MAX_SECONDS = 30

[eth]
chainId = 1
keep_mint_amount = 0.0054
keep_mint_max_amount = 0.0066
currency = "ETH"
all_amount = false # 是否使用全部数量, 如果是，则不保留金额
rpc = "https://rpc.ankr.com/eth"
max_fee = 3 # 最大gas fee, 单位为gwei, 超过会等待


# 跨链配置
[arbitrum]
chainId = 42161
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "ETH"
all_amount = true
rpc = "https://rpc.ankr.com/arbitrum"
max_fee = 3

[scroll]
chainId = 534352
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "ETH"
all_amount = true
rpc = "https://rpc.ankr.com/scroll"
max_fee = 3

[base]
chainId = 8453
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "ETH"
all_amount = true
rpc = "https://rpc.ankr.com/base"
max_fee = 3

[optimism]
chainId = 10
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "ETH"
all_amount = true
rpc = "https://rpc.ankr.com/optimism"
max_fee = 3

[linea]
chainId = 59144
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "ETH"
all_amount = true
rpc = "https://rpc.ankr.com/linea"
max_fee = 3

[bsc]
chainId = 56
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "BNB"
all_amount = true
rpc = "https://bsc-dataseed.binance.org/"
max_fee = 3

[polygon]
chainId = 137
keep_mint_amount = 0.003
keep_mint_max_amount = 0.008
currency = "POL"
all_amount = true
rpc = ""
max_fee = 3


[sepolia]
chainId = 11155111
currency = "SepoliaETH"
rpc = ""
max_fee = 100


[monad]
chainId = 10143
currency = "Mon"
rpc = "https://testnet-rpc.monad.xyz"
max_fee = 110


