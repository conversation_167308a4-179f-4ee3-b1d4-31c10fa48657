export default {
    // MON 兑换代币配置
    monToTokenConfig: {
        minAmount: 0.001,    // 最小兑换数量
        maxAmount: 0.03,    // 最大兑换数量
        decimals: 4         // 保留小数位数
    },

    // 代币兑换 MON 配置
    tokenToMonConfig: {
        USDC: {
            minAmount: 0.05,     // 最小兑换数量
            maxAmount: 0.3,     // 最大兑换数量
            decimals: 6        // USDC是6位精度
        },
        BEAN: {
            minAmount: 0.3,     // 最小兑换数量
            maxAmount: 0.8,     // 最大兑换数量
            decimals: 2         // 保留小数位数
        },
        JAI: {
            minAmount: 1000,    // 最小兑换数量
            maxAmount: 4000,    // 最大兑换数量
            decimals: 2         // 保留小数位数
        }
    },

    // 添加流动性配置
    liquidityConfig: {
        USDC: {
            minAmount: 0.001,     // 最小数量
            maxAmount: 0.5,     // 最大数量
            decimals: 2         // 保留小数位数
        },
        BEAN: {
            minAmount: 0.001,     // 最小数量
            maxAmount: 0.5,     // 最大数量
            decimals: 2         // 保留小数位数
        },
        JAI: {
            minAmount: 100,    // 最小数量
            maxAmount: 2000,    // 最大数量
            decimals: 2         // 保留小数位数
        }
    },

    // 代币配置
    tokens: {
        BEAN: {
            decimals: 18
        },
        USDC: {
            decimals: 6
        },
        JAI: {
            decimals: 6
        }
    }
};
