import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import config from "./bean.config.js";  // 导入配置文件
import { getHumanRandomFloat, getHumanRandomInt } from "../../../base/tools/common.js";

class BeanExchange {
  constructor(rpc) {
    this.rpc = rpc;
    // 初始化provider
    this.provider = new ethers.JsonRpcProvider(this.rpc);

    // 合约地址
    this.routerAddress = '******************************************';
    this.WMON = '******************************************';

    // 代币配置
    this.TOKEN_CONFIG = {
      'BEAN': {
        address: '******************************************',
        symbol: 'BEAN'
      },
      'USDC': {
        // 使用getChecksumAddress来获取正确的地址格式
        address: '******************************************',
        symbol: 'USDC'
      },
      'JAI': {
        address: '******************************************',
        symbol: 'JAI'
      }
    };

    // 在构造函数中就转换所有地址为正确的校验和格式
    Object.keys(this.TOKEN_CONFIG).forEach(key => {
      this.TOKEN_CONFIG[key].address = this.getChecksumAddress(this.TOKEN_CONFIG[key].address);
    });
    this.routerAddress = this.getChecksumAddress(this.routerAddress);
    this.WMON = this.getChecksumAddress(this.WMON);

    // Router ABI
    this.routerAbi = [
      "function getAmountsOut(uint amountIn, address[] memory path) public view returns (uint[] memory amounts)",
      "function getAmountsIn(uint amountOut, address[] memory path) public view returns (uint[] memory amounts)",
      "function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline) external payable returns (uint[] memory amounts)",
      "function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)",
      "function addLiquidityETH(address token, uint amountTokenDesired, uint amountTokenMin, uint amountETHMin, address to, uint deadline) external payable returns (uint amountToken, uint amountETH, uint liquidity)"
    ];

    // Token ABI
    this.tokenAbi = [
      "function balanceOf(address) view returns (uint256)",
      "function decimals() view returns (uint8)",
      "function approve(address spender, uint256 amount) returns (bool)",
      "function allowance(address owner, address spender) view returns (uint256)"
    ];

    // 加载配置
    this.config = config;
  }

  // 添加一个辅助方法来获取正确格式的地址
  getChecksumAddress(address) {
    try {
      return ethers.getAddress(address);
    } catch (error) {
      logger.error(`地址格式错误: ${address}`);
      throw error;
    }
  }

  async getTokenDecimals(tokenSymbol) {
    try {
      return this.config.tokens[tokenSymbol].decimals;
    } catch (error) {
      logger.error('获取代币精度失败:', error);
      throw error;
    }
  }

  // 获取钱包实例的辅助方法
  getWallet(privateKey) {
    return new ethers.Wallet(privateKey, this.provider);
  }

  async checkTokenBalance(address, tokenSymbol) {
    try {
      const tokenContract = new ethers.Contract(
        this.TOKEN_CONFIG[tokenSymbol].address,
        this.tokenAbi,
        this.provider
      );
      const decimals = await this.getTokenDecimals(tokenSymbol);
      const balance = await tokenContract.balanceOf(address);

      return {
        raw: balance,
        formatted: ethers.formatUnits(balance, decimals)
      };
    } catch (error) {
      logger.error('检查代币余额失败:', error);
      throw error;
    }
  }

  async checkAndApproveToken(privateKey, tokenSymbol, amount) {
    try {
      const wallet = this.getWallet(privateKey);
      const tokenContract = new ethers.Contract(
        this.TOKEN_CONFIG[tokenSymbol].address,
        this.tokenAbi,
        wallet
      );

      const currentAllowance = await tokenContract.allowance(wallet.address, this.routerAddress);

      if (currentAllowance < amount) {
        logger.info(`正在设置${tokenSymbol}授权额度...`);
        const approveTx = await tokenContract.approve(this.routerAddress, amount);
        try {
          await approveTx.wait();
          logger.success('授权完成');
          return {
            success: true,
            message: '授权完成',
          };
        } catch (error) {
          logger.error('授权失败:', error);
          return {
            success: false,
            message: error.message,
          };
        }
      }

    } catch (error) {
      logger.error('代币授权失败:', error);
      return {
        success: false,
        message: error.message,
      };
    }
  }

  async swapMONToToken(privateKey, toToken, amount = null) {
    if (!amount) {
      const { minAmount, maxAmount } = this.config.monToTokenConfig;
      amount = getHumanRandomFloat(minAmount, maxAmount);
      logger.info(`随机生成MON兑换数量: ${amount}`);
    }

    try {
      const wallet = this.getWallet(privateKey);
      const balance = await this.provider.getBalance(wallet.address);
      if (balance <= ethers.parseEther(amount.toString())) {
        logger.error(`余额不足，当前余额: ${ethers.formatEther(balance)} MON，需要: ${amount} MON`);
        return { success: false, message: '余额不足' };
      }
      const router = new ethers.Contract(
        this.getChecksumAddress(this.routerAddress),
        this.routerAbi,
        wallet
      );

      const tokenDecimals = await this.getTokenDecimals(toToken);
      const path = [
        this.getChecksumAddress(this.WMON),
        this.getChecksumAddress(this.TOKEN_CONFIG[toToken].address)
      ];

      const amountsOut = await router.getAmountsOut(
        ethers.parseEther(amount.toString()),
        path
      );

      const amountOutMin = amountsOut[1] * 950n / 1000n; // 5% 滑点

      logger.info(`准备兑换: ${amount} MON -> ${ethers.formatUnits(amountsOut[1], tokenDecimals)} ${toToken}`);

      const deadline = Math.floor(Date.now() / 1000) + 60 * 20;

      // 增加gas限制
      const gasEstimate = await router.swapExactETHForTokens.estimateGas(
        amountOutMin,
        path,
        wallet.address,
        deadline,
        {
          value: ethers.parseEther(amount.toString())
        }
      );

      const tx = await router.swapExactETHForTokens(
        amountOutMin,
        path,
        wallet.address,
        deadline,
        {
          value: ethers.parseEther(amount.toString()),
          gasLimit: gasEstimate * 120n / 100n // 增加20%的gas限制
        }
      );

      logger.info(`交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();
      logger.success(`交易已确认: ${receipt.hash}`);

      return {
        success: true,
        hash: receipt.hash,
        inputAmount: amount,
        outputAmount: ethers.formatUnits(amountsOut[1], tokenDecimals)
      };

    } catch (error) {
      // 添加更详细的错误信息
      if (error.reason) {
        logger.error(`Swap错误: ${error.reason}`);
        return { success: false, message: error.reason };
      }
      logger.error('Swap错误:', error);
      return { success: false, message: error.message };
    }
  }

  async swapTokenToMON(privateKey, fromToken) {
    try {
      const wallet = this.getWallet(privateKey);
      const router = new ethers.Contract(this.routerAddress, this.routerAbi, wallet);

      // 检查代币余额
      const tokenBalance = await this.checkTokenBalance(wallet.address, fromToken);
      const tokenDecimals = await this.getTokenDecimals(fromToken);  // 代币精度(6或18)

      // 打印当前余额
      logger.info(`当前${fromToken}余额:`, JSON.stringify({
        '原始值': tokenBalance.raw.toString(),
        '格式化': tokenBalance.formatted,
        '精度': tokenDecimals
      }));

      // 从配置中获取兑换金额范围
      const tokenConfig = this.config.tokenToMonConfig[fromToken];
      let amount; // 定义 amount 变量
      if (tokenBalance.raw === 0n) {
        logger.info(`${fromToken}余额不足，'当前余额': tokenBalance.formatted, 跳过兑换`);
        return {
          success: false,
          message: '代币余额不足'
        };
      } else {
        amount = Number(tokenBalance.formatted);
      }

      const formattedAmount = amount.toFixed(tokenConfig.decimals);
      const tokenAmount = ethers.parseUnits(formattedAmount, tokenDecimals);

      // 打印将要兑换的数量
      logger.info(`准备兑换数量:`, JSON.stringify({
        '数量': formattedAmount,
        '代币': fromToken,
        '代币精度': tokenDecimals
      }));

      // 检查授权额度
      const tokenContract = new ethers.Contract(
        this.TOKEN_CONFIG[fromToken].address,
        this.tokenAbi,
        wallet
      );
      const currentAllowance = await tokenContract.allowance(wallet.address, this.routerAddress);

      logger.info(`当前授权额度:`, JSON.stringify({
        '授权额度': currentAllowance.toString(),
        '需要额度': tokenAmount.toString()
      }));

      // 授权
      const approveResult = await this.checkAndApproveToken(privateKey, fromToken, tokenAmount);
      if (!approveResult.success) {
        return {
          success: false,
          message: approveResult.message,
        };
      }

      // 等待5秒确保授权生效
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 再次检查授权额度
      const newAllowance = await tokenContract.allowance(wallet.address, this.routerAddress);
      logger.info(`更新后授权额度:`, JSON.stringify({
        '授权额度': newAllowance.toString(),
        '需要额度': tokenAmount.toString()
      }));

      // 获取预期输出
      const path = [this.TOKEN_CONFIG[fromToken].address, this.WMON];
      const amountsOut = await router.getAmountsOut(tokenAmount, path);
      const minMonOut = amountsOut[1] * 950n / 1000n; // 5% 滑点

      logger.info('准备发送交易', JSON.stringify({
        '输入': `${ethers.formatUnits(tokenAmount, tokenDecimals)} ${fromToken}`,
        '预期输出': `${ethers.formatEther(amountsOut[1])} MON`
      }));

      const deadline = Math.floor(Date.now() / 1000) + 60 * 20;

      // 估算gas
      const gasEstimate = await router.swapExactTokensForETH.estimateGas(
        tokenAmount,
        minMonOut,
        path,
        wallet.address,
        deadline
      );

      // 增加20%的gas限制
      const gasLimit = gasEstimate * 120n / 100n;

      logger.info('Gas估算:', JSON.stringify({
        '预估gas': gasEstimate.toString(),
        '实际gas限制': gasLimit.toString()
      }));

      const tx = await router.swapExactTokensForETH(
        tokenAmount,
        minMonOut,
        path,
        wallet.address,
        deadline,
        {
          gasLimit: gasLimit
        }
      );

      logger.info(`交易已发送: ${tx.hash}`);
      try {
        const receipt = await tx.wait();
        logger.success(`交易已确认: ${receipt.hash}`);
        return {
          success: true,
          hash: receipt.hash,
          inputAmount: ethers.formatUnits(tokenAmount, tokenDecimals),
          outputAmount: ethers.formatEther(amountsOut[1])
        };
      } catch (error) {
        logger.error('交易失败:', error);
        return {
          success: false,
          message: error.message,
        };
      }

    } catch (error) {
      logger.error('Swap错误:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 添加流动性，使用tokenSymbol代币的全部金额
   * @param {string} privateKey 钱包私钥
   * @param {string} tokenSymbol 代币符号
   * @returns {Promise<Object>} 添加流动性结果
   */
  async addLiquidityWithMON(privateKey, tokenSymbol) {
    try {
      let tokenAmount = null;
      const wallet = this.getWallet(privateKey);
      const router = new ethers.Contract(this.routerAddress, this.routerAbi, wallet);

      // 获取代币实际精度
      const tokenDecimals = await this.getTokenDecimals(tokenSymbol);

      // 检查代币余额
      const tokenBalance = await this.checkTokenBalance(wallet.address, tokenSymbol);
      if (tokenBalance.raw === 0n) {
        if (tokenSymbol === 'JAI') {
          tokenAmount = getHumanRandomInt(this.config.liquidityConfig[tokenSymbol].minAmount, this.config.liquidityConfig[tokenSymbol].maxAmount);
        } else {
          tokenAmount = getHumanRandomFloat(this.config.liquidityConfig[tokenSymbol].minAmount, this.config.liquidityConfig[tokenSymbol].maxAmount);
        }
      } else {
        tokenAmount = Number(tokenBalance.formatted);
      }

      // 使用配置中的decimals来格式化显示金额
      const displayAmount = tokenAmount.toFixed(this.config.liquidityConfig[tokenSymbol].decimals);
      // 使用代币实际精度来转换链上数值
      const tokenAmountIn = ethers.parseUnits(displayAmount, tokenDecimals);

      // 检查代币余额是否足够
      if (tokenBalance.raw < tokenAmountIn) {
        const currentBalance = ethers.formatUnits(tokenBalance.raw, tokenDecimals);
        const needAmount = (tokenAmount - parseFloat(currentBalance))
          .toFixed(this.config.liquidityConfig[tokenSymbol].decimals); // 使用配置的小数位显示

        logger.info('代币余额不足，需要先兑换', JSON.stringify({
          '目标代币': tokenSymbol,
          '当前余额': parseFloat(currentBalance).toFixed(this.config.liquidityConfig[tokenSymbol].decimals),
          '需要数量': displayAmount,
          '缺少数量': needAmount
        }));

        // 计算需要的 MON 数量（添加 1% 滑点）
        const path = [this.WMON, this.TOKEN_CONFIG[tokenSymbol].address];
        const amountsIn = await router.getAmountsIn(
          ethers.parseUnits(needAmount, tokenDecimals), // 使用代币实际精度
          path
        );
        const monNeeded = ethers.formatEther(amountsIn[0] * 101n / 100n);

        // 先兑换所需代币
        const swapResult = await this.swapMONToToken(privateKey, tokenSymbol, parseFloat(monNeeded));
        if (!swapResult.success) {
          throw new Error(`兑换${tokenSymbol}失败: ${swapResult.message}`);
        }

        // 等待交易确认后再次检查余额
        await new Promise(resolve => setTimeout(resolve, 5000));
      }

      // 确保有足够的授权额度
      await this.checkAndApproveToken(privateKey, tokenSymbol, tokenAmountIn);

      // 获取添加流动性所需的MON数量
      const path = [this.TOKEN_CONFIG[tokenSymbol].address, this.WMON];
      const amounts = await router.getAmountsOut(tokenAmountIn, path);
      const monAmount = amounts[1];

      // 增加滑点容忍度到5%
      const tokenAmountMin = tokenAmountIn * 950n / 1000n;
      const monAmountMin = monAmount * 950n / 1000n;

      logger.info('准备添加流动性', JSON.stringify({
        [`${tokenSymbol}数量`]: displayAmount,
        'MON数量': ethers.formatEther(monAmount)
      }));

      const deadline = Math.floor(Date.now() / 1000) + 60 * 20;

      // 估算gas
      const gasEstimate = await router.addLiquidityETH.estimateGas(
        this.TOKEN_CONFIG[tokenSymbol].address,
        tokenAmountIn,
        tokenAmountMin,
        monAmountMin,
        wallet.address,
        deadline,
        {
          value: monAmount
        }
      );

      // 增加20%的gas限制
      const gasLimit = gasEstimate * 120n / 100n;

      logger.info('Gas估算:', JSON.stringify({
        '预估gas': gasEstimate.toString(),
        '实际gas限制': gasLimit.toString()
      }));

      const tx = await router.addLiquidityETH(
        this.TOKEN_CONFIG[tokenSymbol].address,
        tokenAmountIn,
        tokenAmountMin,
        monAmountMin,
        wallet.address,
        deadline,
        {
          value: monAmount,
          gasLimit: gasLimit
        }
      );

      logger.info(`交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();
      logger.success(`交易已确认: ${receipt.hash}`);

      return {
        success: true,
        hash: receipt.hash,
        tokenAmount: displayAmount,
        monAmount: ethers.formatEther(monAmount)
      };

    } catch (error) {
      logger.error('添加流动性错误:', error);
      return { success: false, message: error.message };
    }
  }
}

export default BeanExchange;
