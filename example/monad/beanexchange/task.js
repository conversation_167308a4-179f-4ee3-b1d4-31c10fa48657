import { resolveFromModule } from "../../../base/tools/path.js";
import {
  parseTOMLConfig,
  filterWalletsByIndex,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import BeanExchange from "./bean.js";
import CSV from "../../../base/tools/csv.js";
import logger from "../../../base/tools/logger.js";

class BeanExchangeTask {
  constructor(rpc) {
    this.bean = new BeanExchange(rpc);
    this.tokens = ['BEAN', 'USDC', 'JAI'];
  }

  getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  getRandomToken() {
    return this.tokens[Math.floor(Math.random() * this.tokens.length)];
  }

  generateRandomTasks() {
    const tasks = [];

    // 添加2-4次SWAP任务
    const swapCount = this.getRandomInt(2, 4);
    for (let i = 0; i < swapCount; i++) {
      // 随机选择是MON换代币还是代币换MON
      if (Math.random() < 0.5) {
        tasks.push({
          type: 'monToToken',
          token: this.getRandomToken()
        });
      } else {
        tasks.push({
          type: 'tokenToMon',
          token: this.getRandomToken()
        });
      }
    }

    // 添加1-2次LP任务
    const lpCount = this.getRandomInt(1, 2);
    for (let i = 0; i < lpCount; i++) {
      tasks.push({
        type: 'addLiquidity',
        token: this.getRandomToken()
      });
    }

    // 打乱任务顺序
    return tasks.sort(() => Math.random() - 0.5);
  }

  async handleWallet(wallet) {
    const { private_key, index, address } = wallet;

    try {
      // 生成随机任务列表
      const tasks = this.generateRandomTasks();
      logger.info(`钱包 ${index} 的任务列表: ${tasks.map(t => `${t.type}(${t.token})`).join(', ')}`);

      let allSuccess = true;
      let completedTasks = 0;

      // 执行每个任务
      for (const task of tasks) {
        logger.info(`钱包 ${index} 执行任务: ${task.type} ${task.token}`);

        let result;
        try {
          switch (task.type) {
            case 'monToToken':
              result = await this.bean.swapMONToToken(private_key, task.token);
              break;
            case 'tokenToMon':
              result = await this.bean.swapTokenToMON(private_key, task.token);
              break;
            case 'addLiquidity':
              result = await this.bean.addLiquidityWithMON(private_key, task.token);
              break;
          }

          if (result.success) {
            logger.success(`钱包 ${index} 任务成功: ${task.type} ${task.token}`);
            completedTasks++;
          } else {
            logger.error(`钱包 ${index} 任务失败: ${task.type} ${task.token} - ${result.message}`);
            // 如果是余额不足这类可以继续的错误，继续执行
            if (!result.message.includes('余额不足')) {
              allSuccess = false;
            }
          }

          // 任务间隔10-15秒
          if (tasks.indexOf(task) < tasks.length - 1) {
            const delay = getRandomValue(10, 15);
            logger.info(`等待 ${delay} 秒后执行下一个任务...`);
            await new Promise(resolve => setTimeout(resolve, delay * 1000));
          }

        } catch (taskError) {
          logger.error(`任务执行出错: ${taskError.message}`);
          allSuccess = false;
          break;
        }
      }

      return {
        success: allSuccess,
        message: allSuccess ? "所有任务完成" : `完成 ${completedTasks}/${tasks.length} 个任务`
      };

    } catch (error) {
      logger.error(`钱包 ${index} 处理出错:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  async start(indexArg) {
    logger.info("开始BeanExchange任务...");

    try {
      // 加载钱包
      const walletList = await CSV.read(getCSVFilePath());
      const filteredWallets = filterWalletsByIndex(indexArg, walletList);
      const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
      const totalWallets = shuffledWallets.length;

      logger.info(`成功加载 ${totalWallets} 个钱包`);

      // 统计变量
      let successCount = 0;
      let failedCount = 0;
      let failedIndexes = [];

      // 遍历处理每个钱包
      for (const [currentIndex, wallet] of shuffledWallets.entries()) {
        const { index, address } = wallet;

        if (!wallet.private_key?.trim()) {
          logger.warn(`钱包 ${index} 的私钥为空，跳过处理`);
          failedIndexes.push(index);
          failedCount++;
          continue;
        }

        logger.info(
          `开始处理第 ${currentIndex + 1}/${totalWallets} 个钱包，编号: ${index}，地址: ${address}`,
        );

        const result = await this.handleWallet(wallet);

        if (result.success) {
          successCount++;
        } else {
          failedCount++;
          failedIndexes.push(index);
        }

        // 如果不是最后一个钱包，添加随机延迟
        if (currentIndex < totalWallets - 1) {
          const delaySeconds = getRandomValue(20, 30);
          logger.info(`等待 ${delaySeconds} 秒后继续下一个钱包...`);
          await new Promise(resolve => setTimeout(resolve, delaySeconds * 1000));
        }
      }

      // 打印统计信息
      logger.info("------------------------");
      logger.info(`总计处理钱包: ${totalWallets}`);
      logger.info(`成功: ${successCount}`);
      logger.info(`失败: ${failedCount}`);
      if (failedIndexes.length > 0) {
        logger.info(`失败钱包编号: ${failedIndexes.join(", ")}`);
      }

    } catch (error) {
      logger.error("任务执行出错:", error);
    }
  }
}

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  // 加载配置
  const configPath = resolveFromModule(import.meta.url, "../config.toml");
  const config = parseTOMLConfig(configPath);
  const monadConfig = config.monad;
  const rpc = monadConfig.rpc;

  const task = new BeanExchangeTask(rpc);
  await task.start(indexArg);
}

// 运行主函数
main().catch(error => {
  logger.error("未处理的错误:", error);
  process.exit(1);
});

export default main;
