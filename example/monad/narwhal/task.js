import { resolveFromModule } from "../../../base/tools/path.js";
import {
  parseTOMLConfig,
  filterWalletsByIndex,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import Narwhal from "./narwhal.js";
import CSV from "../../../base/tools/csv.js";
import logger from "../../../base/tools/logger.js";
import { ethers } from "ethers";

class NarwhalTask {
  constructor(rpc) {
    this.rpc = rpc;
    this.narwhal = new Narwhal(rpc);
    this.gameTypes = ['dice', 'slots', 'rock', 'coinFlip'];
  }

  getRandomGames() {
    const numGames = getRandomValue(1, 3);  // 随机选择1-3个游戏
    const shuffled = [...this.gameTypes].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, numGames);
  }

  async handleWallet(wallet) {
    const { private_key, index, address } = wallet;

    try {
      // 1. 检查并执行mint
      const balance = await this.narwhal.getMockUSDTBalance(address);
      const balanceInToken = Number(ethers.formatEther(balance));

      if (balanceInToken < 1000) {
        logger.info(`钱包 ${index} 当前MockUSDT余额: ${balanceInToken.toFixed(2)}, 尝试mint`);
        const mintResult = await this.narwhal.mint(private_key);
        if (!mintResult.success) {
          return {
            success: false,
            message: `Mint失败: ${mintResult.message}`
          };
        }
      }

      // 2. 随机选择游戏
      const selectedGames = this.getRandomGames();
      logger.info(`钱包 ${index} 将玩 ${selectedGames.length} 个游戏: ${selectedGames.join(', ')}`);

      // 3. 执行每个游戏
      for (const gameType of selectedGames) {
        const playTimes = getRandomValue(2, 4);  // 每个游戏玩2-4次

        for (let i = 1; i <= playTimes; i++) {
          if (i > 1) {
            const delay = getRandomValue(10, 20);
            logger.info(`等待 ${delay} 秒后继续下一次游戏...`);
            await sleep(delay);
          }

          const result = await this.narwhal[gameType](private_key);
          if (!result.success) {
            return {
              success: false,
              message: `${gameType}游戏失败: ${result.message}`
            };
          }
        }

        // 游戏之间等待
        if (gameType !== selectedGames[selectedGames.length - 1]) {
          const delay = getRandomValue(20, 40);
          logger.info(`等待 ${delay} 秒后开始下一个游戏...`);
          await sleep(delay);
        }
      }

      return {
        success: true,
        message: "所有游戏完成"
      };

    } catch (error) {
      logger.error(`钱包 ${index} 处理出错:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  async start(indexArg) {
    logger.info("开始Narwhal游戏任务...");

    try {
      // 加载钱包
      const walletList = await CSV.read(getCSVFilePath());
      const filteredWallets = filterWalletsByIndex(indexArg, walletList);
      const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
      const totalWallets = shuffledWallets.length;

      logger.info(`成功加载 ${totalWallets} 个钱包`);

      // 统计变量
      let successCount = 0;
      let failedCount = 0;
      let failedIndexes = [];

      // 遍历处理每个钱包
      for (const [currentIndex, wallet] of shuffledWallets.entries()) {
        const { index, address } = wallet;

        if (!wallet.private_key?.trim()) {
          logger.warn(`钱包 ${index} 的私钥为空，跳过处理`);
          failedIndexes.push(index);
          failedCount++;
          continue;
        }

        logger.info(
          `开始处理第 ${currentIndex + 1}/${totalWallets} 个钱包，编号: ${index}，地址: ${address}`,
        );

        const result = await this.handleWallet(wallet);

        if (result.success) {
          successCount++;
        } else {
          failedCount++;
          failedIndexes.push(index);
        }

        // 如果不是最后一个钱包，添加随机延迟
        if (currentIndex < totalWallets - 1) {
          const delaySeconds = getRandomValue(10, 20);
          logger.info(`等待 ${delaySeconds} 秒后继续下一个钱包...`);
          await sleep(delaySeconds);
        }
      }

      // 打印统计信息
      logger.info("------------------------");
      logger.info(`总计处理钱包: ${totalWallets}`);
      logger.info(`成功: ${successCount}`);
      logger.info(`失败: ${failedCount}`);
      if (failedIndexes.length > 0) {
        logger.info(`失败钱包编号: ${failedIndexes.join(", ")}`);
      }

    } catch (error) {
      logger.error("任务执行出错:", error);
    }
  }
}

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  // 加载配置
  const configPath = resolveFromModule(import.meta.url, "../config.toml");
  const config = parseTOMLConfig(configPath);
  const monadConfig = config.monad;
  const rpc = monadConfig.rpc;

  const task = new NarwhalTask(rpc);

  // 如果没有参数，测试第一个钱包
  if (!indexArg) {
    const walletList = await CSV.read(getCSVFilePath());
    if (walletList.length > 0) {
      logger.info("测试模式：使用第一个钱包");
      await task.handleWallet(walletList[0]);
    }
  } else {
    // 正常模式：处理指定范围的钱包
    await task.start(indexArg);
  }
}

// 运行主函数
main().catch(error => {
  logger.error("未处理的错误:", error);
  process.exit(1);
});

export default main;
