import { ethers } from "ethers";

import { getHumanRandomInt, retry } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";

class Narwhal {
  constructor(rpc) {
    this.rpc = rpc;
    this.provider = new ethers.JsonRpcProvider(this.rpc);

    // 合约地址
    this.NARWHAL_ADDRESS = "******************************************";
    this.MOCK_USDT_ADDRESS = "******************************************";
    this.DICE_ADDRESS = "******************************************";
    this.SLOTS_ADDRESS = "******************************************";
    this.ROCK_ADDRESS = "******************************************";
    this.COINFLIP_ADDRESS = "******************************************";

    // 合约ABI
    this.NARWHAL_ABI = [
      {
        type: "function",
        name: "mint",
        inputs: [],
        outputs: [],
        stateMutability: "nonpayable"
      }
    ];

    this.ERC20_ABI = [
      {
        type: "function",
        name: "balanceOf",
        inputs: [{ name: "account", type: "address" }],
        outputs: [{ name: "", type: "uint256" }],
        stateMutability: "view"
      },
      {
        type: "function",
        name: "approve",
        inputs: [
          { name: "spender", type: "address" },
          { name: "amount", type: "uint256" }
        ],
        outputs: [{ name: "", type: "bool" }],
        stateMutability: "nonpayable"
      },
      {
        type: "function",
        name: "allowance",
        inputs: [
          { name: "owner", type: "address" },
          { name: "spender", type: "address" }
        ],
        outputs: [{ name: "", type: "uint256" }],
        stateMutability: "view"
      }
    ];

    this.DICE_ABI = [
      {
        inputs: [
          { name: "wager", type: "uint256" },
          { name: "multiplier", type: "uint32" },
          { name: "tokenAddress", type: "address" },
          { name: "isOver", type: "bool" },
          { name: "numBets", type: "uint32" },
          { name: "stopGain", type: "uint256" },
          { name: "stopLoss", type: "uint256" }
        ],
        name: "Dice_Play",
        outputs: [],
        stateMutability: "payable",
        type: "function"
      }
    ];

    this.SLOTS_ABI = [
      {
        inputs: [
          { name: "wager", type: "uint256" },
          { name: "tokenAddress", type: "address" },
          { name: "numBets", type: "uint32" },
          { name: "stopGain", type: "uint256" },
          { name: "stopLoss", type: "uint256" }
        ],
        name: "Slots_Play",
        outputs: [],
        stateMutability: "payable",
        type: "function"
      }
    ];

    this.ROCK_ABI = [
      {
        inputs: [
          { name: "wager", type: "uint256" },
          { name: "tokenAddress", type: "address" },
          { name: "action", type: "uint8" },
          { name: "numBets", type: "uint32" },
          { name: "stopGain", type: "uint256" },
          { name: "stopLoss", type: "uint256" }
        ],
        name: "RockPaperScissors_Play",
        outputs: [],
        stateMutability: "payable",
        type: "function"
      }
    ];

    this.COINFLIP_ABI = [
      {
        inputs: [
          { name: "wager", type: "uint256" },
          { name: "tokenAddress", type: "address" },
          { name: "isHeads", type: "bool" },
          { name: "numBets", type: "uint32" },
          { name: "stopGain", type: "uint256" },
          { name: "stopLoss", type: "uint256" }
        ],
        name: "CoinFlip_Play",
        outputs: [],
        stateMutability: "payable",
        type: "function"
      }
    ];

    // ... 其他游戏的ABI定义 ...

    // 配置
    this.config = {
      gameAmount: {
        min: 10,
        max: 1000
      }
    };
  }

  /**
   * 获取随机游戏金额
   */
  getRandomGameAmount() {
    return getHumanRandomInt(this.config.gameAmount.min, this.config.gameAmount.max);
  }

  /**
   * 检查并设置授权
   */
  async checkAndApprove(private_key, amount, spender) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const mockUSDT = new ethers.Contract(this.MOCK_USDT_ADDRESS, this.ERC20_ABI, wallet);

      const allowance = await mockUSDT.allowance(wallet.address, spender);
      const amountWei = ethers.parseEther(amount.toString());

      if (allowance < amountWei) {
        logger.info(`设置授权金额: ${amount} MockUSDT`);
        const tx = await mockUSDT.approve(spender, amountWei);
        logger.info(`授权交易已发送: ${tx.hash}`);
        try {
          const receipt = await tx.wait();
          if (receipt.status === 0) {
            logger.error(`授权失败: ${tx.hash}`);
            return {
              success: false,
              code: "APPROVE_FAILED",
              message: "授权失败"
            };
          }

          logger.success(`授权成功: ${tx.hash}`);
          return {
            success: true,
            code: "APPROVE_SUCCESS",
            data: {
              hash: tx.hash,
            }
          };
        } catch (error) {
          logger.error(`授权失败: ${error.message}`);
          return {
            success: false,
            code: "APPROVE_FAILED",
            message: "授权失败"
          };
        }
      }
    } catch (error) {
      logger.error(`授权失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行Mint
   */
  async mint(private_key) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.NARWHAL_ADDRESS, this.NARWHAL_ABI, wallet);

      // 检查当前余额
      const balance = await this.getMockUSDTBalance(wallet.address);
      const balanceInToken = Number(ethers.formatEther(balance));

      if (balanceInToken >= 1000) {
        logger.info(`MockUSDT余额已超过1000 (${balanceInToken.toFixed(2)})，跳过mint`);
        return {
          success: false,
          code: "BALANCE_LIMIT",
          message: "MockUSDT余额已超过限制"
        };
      }

      // 发送交易
      const tx = await contract.mint();
      logger.info(`Mint交易已发送: ${tx.hash}`);

      const receipt = await retry(async () => await tx.wait(), 3, 5000);
      logger.success(`Mint成功: ${tx.hash}`);

      return {
        success: true,
        hash: tx.hash
      };

    } catch (error) {
      logger.error("Mint失败:", error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 玩Dice游戏
   */
  async dice(private_key) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.DICE_ADDRESS, this.DICE_ABI, wallet);

      // 获取随机游戏金额
      const gameAmount = this.getRandomGameAmount();
      logger.info(`准备玩Dice游戏，金额: ${gameAmount} MockUSDT`);

      // 检查并设置授权
      await this.checkAndApprove(private_key, gameAmount, this.DICE_ADDRESS);

      // 构造游戏参数
      const wager = ethers.parseEther(gameAmount.toString());
      const multiplier = 19800; // 1.98x
      const isOver = true;
      const numBets = 1;
      const stopGain = ethers.MaxUint256;
      const stopLoss = ethers.MaxUint256;

      // 发送交易
      const tx = await contract.Dice_Play(
        wager,
        multiplier,
        this.MOCK_USDT_ADDRESS,
        isOver,
        numBets,
        stopGain,
        stopLoss,
        {
          value: ethers.parseEther("0.027000001350000001")
        }
      );

      logger.info(`Dice游戏交易已发送: ${tx.hash}`);
      const receipt = await retry(async () => await tx.wait(), 3, 5000);
      logger.success(`Dice游戏完成: ${tx.hash}`);

      return {
        success: true,
        hash: tx.hash
      };

    } catch (error) {
      logger.error("Dice游戏失败:", error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 玩Slots游戏
   */
  async slots(private_key) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.SLOTS_ADDRESS, this.SLOTS_ABI, wallet);

      // 获取随机游戏金额
      const gameAmount = this.getRandomGameAmount();
      logger.info(`准备玩Slots游戏，金额: ${gameAmount} MockUSDT`);

      // 检查并设置授权
      await this.checkAndApprove(private_key, gameAmount, this.SLOTS_ADDRESS);

      // 构造游戏参数
      const wager = ethers.parseEther(gameAmount.toString());
      const numBets = 1;
      const stopGain = ethers.MaxUint256;
      const stopLoss = ethers.MaxUint256;

      // 发送交易
      const tx = await contract.Slots_Play(
        wager,
        this.MOCK_USDT_ADDRESS,
        numBets,
        stopGain,
        stopLoss,
        {
          value: ethers.parseEther("0.027000001350000001")
        }
      );

      logger.info(`Slots游戏交易已发送: ${tx.hash}`);
      const receipt = await retry(async () => await tx.wait(), 3, 5000);
      logger.success(`Slots游戏完成: ${tx.hash}`);

      return {
        success: true,
        hash: tx.hash
      };

    } catch (error) {
      logger.error("Slots游戏失败:", error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 玩Rock Paper Scissors游戏
   */
  async rock(private_key) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.ROCK_ADDRESS, this.ROCK_ABI, wallet);

      // 获取随机游戏金额
      const gameAmount = this.getRandomGameAmount();
      logger.info(`准备玩Rock Paper Scissors游戏，金额: ${gameAmount} MockUSDT`);

      // 检查并设置授权
      await this.checkAndApprove(private_key, gameAmount, this.ROCK_ADDRESS);

      // 构造游戏参数
      const wager = ethers.parseEther(gameAmount.toString());
      const action = Math.floor(Math.random() * 3);  // 0: Rock, 1: Paper, 2: Scissors
      const numBets = 1;
      const stopGain = ethers.MaxUint256;
      const stopLoss = ethers.MaxUint256;

      const actionNames = ['Rock', 'Paper', 'Scissors'];
      logger.info(`选择: ${actionNames[action]}`);

      // 发送交易
      const tx = await contract.RockPaperScissors_Play(
        wager,
        this.MOCK_USDT_ADDRESS,
        action,
        numBets,
        stopGain,
        stopLoss,
        {
          value: ethers.parseEther("0.027000001350000001")
        }
      );

      logger.info(`Rock Paper Scissors游戏交易已发送: ${tx.hash}`);
      const receipt = await retry(async () => await tx.wait(), 3, 5000);
      logger.success(`Rock Paper Scissors游戏完成: ${tx.hash}`);

      return {
        success: true,
        hash: tx.hash,
        action: actionNames[action]
      };

    } catch (error) {
      logger.error("Rock Paper Scissors游戏失败:", error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 玩CoinFlip游戏
   */
  async coinFlip(private_key) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.COINFLIP_ADDRESS, this.COINFLIP_ABI, wallet);

      // 获取随机游戏金额
      const gameAmount = this.getRandomGameAmount();
      logger.info(`准备玩CoinFlip游戏，金额: ${gameAmount} MockUSDT`);

      // 检查并设置授权
      await this.checkAndApprove(private_key, gameAmount, this.COINFLIP_ADDRESS);

      // 构造游戏参数
      const wager = ethers.parseEther(gameAmount.toString());
      const isHeads = Math.random() < 0.5;  // 随机选择正反面
      const numBets = 1;
      const stopGain = ethers.MaxUint256;
      const stopLoss = ethers.MaxUint256;

      logger.info(`选择: ${isHeads ? '正面' : '反面'}`);

      // 发送交易
      const tx = await contract.CoinFlip_Play(
        wager,
        this.MOCK_USDT_ADDRESS,
        isHeads,
        numBets,
        stopGain,
        stopLoss,
        {
          value: ethers.parseEther("0.027000001350000001")
        }
      );

      logger.info(`CoinFlip游戏交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();
      logger.success(`CoinFlip游戏完成: ${tx.hash}`);

      return {
        success: true,
        hash: tx.hash,
        choice: isHeads ? '正面' : '反面'
      };

    } catch (error) {
      logger.error("CoinFlip游戏失败:", error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 获取MockUSDT余额
   */
  async getMockUSDTBalance(address) {
    try {
      const contract = new ethers.Contract(this.MOCK_USDT_ADDRESS, this.ERC20_ABI, this.provider);
      const balance = await contract.balanceOf(address);
      return balance;
    } catch (error) {
      logger.error(`获取MockUSDT余额失败: ${error.message}`);
      return ethers.parseEther("0");
    }
  }
}

export default Narwhal;

