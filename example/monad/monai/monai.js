import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";

// https://monai.gg/nft?tab=mint-box
class MonAI{
  constructor(rpc) {
    this.rpc = rpc;
    this.contractAddress = "******************************************";
    this.abi = MonAI_ABI;
    this.mintData = "0x94bf804d00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000"; // mint函数的input数据
    this.mintPrice = ethers.parseEther("0.1");
  }

  async getBalance(address, contractAddress=this.contractAddress) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const contract = new ethers.Contract(contractAddress, this.abi, provider);
    return await contract.balanceOf(address);
  }

  async mint(privateKey, walletIndex) {

    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(privateKey, provider);
    const contract = new ethers.Contract(this.contractAddress, this.abi, wallet);

    const balance = await this.getBalance(wallet.address, "******************************************");
    if (balance > 0) {
        logger.info(`钱包 ${walletIndex} 已经mint过，跳过处理`);
        return {
            success: false,
            code: 'ALREADY_MINTED',
            message: `钱包 ${walletIndex} 已经mint过，跳过处理`,
            data: { balance: balance.toString() }
        };
    }

    try {
      // 检查钱包余额是否足够
        const walletBalance = await provider.getBalance(wallet.address);
        const requiredAmount = this.mintPrice + ethers.parseEther("0.1"); // 0.5 + 0.2 MON

        if (walletBalance < requiredAmount) {
          const formattedBalance = ethers.formatEther(walletBalance);
          const formattedPrice = ethers.formatEther(requiredAmount);
          logger.error(
            `钱包 ${walletIndex} 余额不足: ${formattedBalance} MON, 需要: ${formattedPrice} MON`,
          );
          return {
            success: false,
            code: "INSUFFICIENT_FUNDS",
            message: `余额不足，无法支付mint费用`,
            data: {
              balance: formattedBalance,
              required: formattedPrice,
              missing: ethers.formatEther(this.mintPrice - walletBalance),
            },
          };
        }

        // 使用通用交易方法发送mint交易
        const txHash = await buildAndSendTransaction(
          this.rpc,
          privateKey,
          this.contractAddress,
          this.mintData,
          this.mintPrice.toString(),
        );

        logger.info(`钱包 ${walletIndex} mint已发送，等待确认`);
        const receipt = await txHash.wait();

        logger.success(`钱包 ${walletIndex} mint成功，交易哈希: ${receipt.hash}`);
        return {
            success: true,
            code: 'SUCCESS',
            message: `钱包 ${walletIndex} mint成功`,
            data: {
                txHash: receipt.hash,
                blockNumber: receipt.blockNumber,
                gasUsed: receipt.gasUsed.toString()
            }
        };
    } catch (error) {
        // 处理特定的错误类型
        if (error.message.includes("insufficient funds")) {
            return {
                success: false,
                code: 'INSUFFICIENT_FUNDS',
                message: "余额不足，无法支付gas费",
                data: { error: error.message }
            };
        } else if (error.message.includes("nonce")) {
            return {
                success: false,
                code: 'NONCE_ERROR',
                message: "Nonce错误，请等待之前的交易确认",
                data: { error: error.message }
            };
        } else if (error.message.includes("already minted")) {
            return {
                success: false,
                code: 'ALREADY_MINTED',
                message: "已经mint过了",
                data: { error: error.message }
            };
        }

        // 处理其他未知错误
        return {
            success: false,
            code: 'UNKNOWN_ERROR',
            message: `Mint失败: ${error.message}`,
            data: { error: error.message }
        };
    }
  }

  async open(privateKey, walletIndex) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(privateKey, provider);

    // 检查是否已经mint过
    // const balance = await this.getBalance(wallet.address);
    // if (balance > 0) {
    //   logger.info(`钱包 ${walletIndex} 已经open过，跳过处理`);
    //   return {
    //       success: false,
    //       code: 'ALREADY_MINTED',
    //       message: `钱包 ${walletIndex} 已经open过，跳过处理`,
    //       data: { balance: balance.toString() }
    //   };
    // }

    try {
        const mintData = "0xb1e5e2b70000000000000000000000000000000000000000000000000000000000000001";
        // 使用通用交易方法发送open交易
        const txHash = await buildAndSendTransaction(
          this.rpc,
          privateKey,
          this.contractAddress,
          mintData,
          "0"
        );

        logger.info(`钱包 ${walletIndex} open已发送，等待确认`);
        const receipt = await txHash.wait();

        logger.success(`钱包 ${walletIndex} open成功，交易哈希: ${receipt.hash}`);
        return {
            success: true,
            code: 'SUCCESS',
            message: `钱包 ${walletIndex} open成功`,
            data: {
                txHash: receipt.hash,
                blockNumber: receipt.blockNumber,
                gasUsed: receipt.gasUsed.toString()
            }
        };
    } catch (error) {
        // 处理特定的错误类型
        if (error.message.includes("insufficient funds")) {
            return {
                success: false,
                code: 'INSUFFICIENT_FUNDS',
                message: "余额不足，无法支付gas费",
                data: { error: error.message }
            };
        } else if (error.message.includes("nonce")) {
            return {
                success: false,
                code: 'NONCE_ERROR',
                message: "Nonce错误，请等待之前的交易确认",
                data: { error: error.message }
            };
        }

        // 处理其他未知错误
        return {
            success: false,
            code: 'UNKNOWN_ERROR',
            message: `Open失败: ${error.message}`,
            data: { error: error.message }
        };
    }
  }

  async task(privateKey, walletIndex) {
    // 先调用 mint 方法
    const mintResult = await this.mint(privateKey, walletIndex);

    // 如果 mint 失败且不是已经 mint 过的情况，直接返回错误结果
    if (!mintResult.success && mintResult.code !== 'ALREADY_MINTED') {
        return mintResult;
    }

    // 只有在 mint 成功的情况下才等待
    if (mintResult.success) {
        // 随机等待 30-60 秒
        const waitTime = Math.floor(Math.random() * (60 - 30 + 1)) + 30;
        logger.info(`钱包 ${walletIndex} mint成功，等待 ${waitTime} 秒后执行 open`);
        await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
    } else {
        logger.info(`钱包 ${walletIndex} 已经mint过，直接执行 open`);
    }

    // 调用 open 方法
    const openResult = await this.open(privateKey, walletIndex);

    return {
        success: openResult.success,
        code: openResult.code,
        message: openResult.message,
        data: {
            mint: mintResult,
            open: openResult.data
        }
    };
  }
}

// 只保留 balanceOf 函数的 ABI
const MonAI_ABI = [
  {
    "inputs": [
        {
            "internalType": "address",
            "name": "owner",
            "type": "address"
        }
    ],
    "name": "balanceOf",
    "outputs": [
        {
            "internalType": "uint256",
            "name": "balance",
            "type": "uint256"
        }
    ],
    "stateMutability": "view",
    "type": "function"
}
];

export { MonAI, MonAI_ABI };
