import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";

// https://magiceden.io/mint-terminal/monad-testnet/******************************************
class MonShape {
  constructor(rpc) {
    this.rpc = rpc;
    this.tokenId = 0;
    this.contractAddress = "******************************************";
    this.abi = MonShape_ABI;
    this.mintPrice = ethers.parseEther("1"); // 0.5 MON
  }

  async getBalance(address) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const contract = new ethers.Contract(this.contractAddress, this.abi, provider);
    return await contract.balanceOf(address, this.tokenId);
  }

  async mint(privateKey, walletIndex) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(privateKey, provider);

    // 检查是否已经mint过
    const balance = await this.getBalance(wallet.address);
    if (balance > 0) {
      logger.info(`钱包 ${walletIndex} 已经mint过，跳过处理`);
      return {
        success: false,
        code: "ALREADY_MINTED",
        message: `钱包 ${walletIndex} 已经mint过，跳过处理`,
        data: { balance: balance.toString() },
      };
    }

    try {

      const walletBalance = await provider.getBalance(wallet.address);
      const requiredAmount = this.mintPrice + ethers.parseEther("0.5"); // 0.5 + 0.2 MON

      if (walletBalance < requiredAmount) {
        const formattedBalance = ethers.formatEther(walletBalance);
        const formattedPrice = ethers.formatEther(requiredAmount);
        logger.error(
          `钱包 ${walletIndex} 余额不足: ${formattedBalance} MON, 需要: ${formattedPrice} MON`,
        );
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: `余额不足，无法支付mint费用`,
          data: {
            balance: formattedBalance,
            required: formattedPrice,
            missing: ethers.formatEther(this.mintPrice - walletBalance),
          },
        };
      }
      // 构建新的 mintData，替换钱包地址
      const mintDataBase = "0x9b4f3af5000000000000000000000000WALLET_ADDRESS_HERE0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000"
      const walletAddressWithout0x = wallet.address.slice(2).toLowerCase();
      const newMintData = mintDataBase.replace("WALLET_ADDRESS_HERE", walletAddressWithout0x);

      try {
        // 使用通用交易方法发送mint交易
        const txHash = await buildAndSendTransaction(
          this.rpc,
          privateKey,
          this.contractAddress,
          newMintData,
          this.mintPrice.toString(),
        );

        // 等待交易确认
        logger.info(`钱包 ${walletIndex} mint已发送，等待确认`);
        const receipt = await txHash.wait();

        logger.success(`钱包 ${walletIndex} mint成功，交易哈希: ${receipt.hash}`);
        return {
          success: true,
          code: "SUCCESS",
          message: `钱包 ${walletIndex} mint成功`,
          data: {
            txHash: receipt.hash,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed.toString(),
            value: ethers.formatEther(this.mintPrice),
          },
        };
      } catch (error) {
        if (error.message.includes("execution reverted")) {
          return {
            success: false,
            code: "MINT_UNAVAILABLE",
            message: "Mint失败: 可能是mint未开放或不在白名单中",
            data: { error: error.message },
          };
        }
        throw error;
      }
    } catch (error) {
      if (error.message.includes("insufficient funds")) {
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: "余额不足，无法支付gas费",
          data: { error: error.message },
        };
      } else if (error.message.includes("nonce")) {
        return {
          success: false,
          code: "NONCE_ERROR",
          message: "Nonce错误，请等待之前的交易确认",
          data: { error: error.message },
        };
      }

      logger.error(`Mint失败: ${error.message}`);
      return {
        success: false,
        code: "UNKNOWN_ERROR",
        message: `Mint失败: ${error.message}`,
        data: { error: error.message },
      };
    }
  }
}

// 只保留 balanceOf 函数的 ABI
const MonShape_ABI = [
  {
      "inputs": [
          {
              "internalType": "uint256",
              "name": "tokenId",
              "type": "uint256"
          },
          {
              "internalType": "uint32",
              "name": "qty",
              "type": "uint32"
          },
          {
              "internalType": "uint32",
              "name": "limit",
              "type": "uint32"
          },
          {
              "internalType": "bytes32[]",
              "name": "proof",
              "type": "bytes32[]"
          }
      ],
      "name": "mint",
      "outputs": [],
      "stateMutability": "payable",
      "type": "function"
  },
  {
      "inputs": [
          {
              "internalType": "address",
              "name": "account",
              "type": "address"
          },
          {
              "internalType": "uint256",
              "name": "id",
              "type": "uint256"
          }
      ],
      "name": "balanceOf",
      "outputs": [
          {
              "internalType": "uint256",
              "name": "",
              "type": "uint256"
          }
      ],
      "stateMutability": "view",
      "type": "function"
  }
];;

export { MonShape, MonShape_ABI };
