import logger from "../../../base/tools/logger.js";
import CSV from "../../../base/tools/csv.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { parseTOMLConfig, filterWalletsByIndex, getRandomValue, sleep } from "../../../base/tools/common.js";
import { OvernadsNFT } from "./overnads.js";

function getCSVFilePath() {
    return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

async function main() {
    const args = process.argv.slice(2);
    const indexArg = args[0];

    // 加载配置
    const configPath = resolveFromModule(import.meta.url, "../config.toml");
    const config = parseTOMLConfig(configPath);
    const monadConfig = config.monad;
    const rpc = monadConfig.rpc;
    const minDelaySeconds = config.MIN_DELAY_SECONDS || 5;
    const maxDelaySeconds = config.MAX_DELAY_SECONDS || 15;

    // 创建Overnads NFT实例
    const overnadsNFT = new OvernadsNFT(rpc);

    // 加载钱包
    const walletList = await CSV.read(getCSVFilePath());
    const filteredWallets = filterWalletsByIndex(indexArg, walletList);
    const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
    const totalWallets = shuffledWallets.length;

    // 统计变量
    let successCount = 0;
    let failedCount = 0;
    let failedIndexes = [];

    for (let [currentIndex, wallet] of shuffledWallets.entries()) {
        const { private_key, index, address } = wallet;

        if (!private_key?.trim()) {
            logger.warn(`钱包 ${index} 的私钥为空，跳过处理`);
            failedIndexes.push(index);
            failedCount++;
            continue;
        }

        logger.info(`开始处理第 ${currentIndex + 1}/${totalWallets} 个钱包，编号: ${index}，地址: ${address}`);

        try {
            // 尝试mint
            let retryCount = 0;
            const maxRetries = 3;

            while (retryCount < maxRetries) {
                try {
                    const result = await overnadsNFT.mint(private_key, index);

                    if (result.success) {
                        successCount++;
                        logger.success(`钱包 ${index} mint成功: ${result.message}`);
                        logger.info(`交易哈希: ${result.data.txHash}`);
                    } else {
                        // 处理特定的失败情况
                        if (result.code === 'ALREADY_MINTED') {
                            logger.info(`钱包 ${index} ${result.message}`);
                            successCount++; // 已经mint过的也算成功
                            break;
                        } else if (['MINT_UNAVAILABLE', 'INSUFFICIENT_FUNDS', 'NONCE_ERROR'].includes(result.code)) {
                            logger.error(`钱包 ${index} mint失败: ${result.message}`);
                            retryCount++;

                            if (retryCount === maxRetries) {
                                failedCount++;
                                failedIndexes.push(index);
                                logger.error(`钱包 ${index} 达到最大重试次数，跳过该钱包`);
                                break;
                            }

                            // 等待后重试
                            const waitTime = getRandomValue(minDelaySeconds, maxDelaySeconds);
                            logger.info(`准备等待 ${waitTime} 秒后重试...`);
                            await sleep(waitTime);
                            continue;
                        } else {
                            // 其他未知错误
                            logger.error(`钱包 ${index} mint失败: ${result.message}`);
                            failedCount++;
                            failedIndexes.push(index);
                            break;
                        }
                    }

                    // 如果是最后一个钱包，不需要等待
                    if (currentIndex === totalWallets - 1) {
                        break;
                    }

                    // 随机等待
                    const waitTime = getRandomValue(minDelaySeconds, maxDelaySeconds);
                    logger.info(`等待${waitTime}秒后处理下一个钱包...`);
                    await sleep(waitTime);
                    break;

                } catch (err) {
                    // 这里处理的是mint方法本身抛出的异常，而不是mint操作的失败
                    retryCount++;
                    logger.error(`钱包 ${index} mint过程发生异常 (重试 ${retryCount}/${maxRetries}): ${err.message}`);

                    if (retryCount === maxRetries) {
                        failedCount++;
                        failedIndexes.push(index);
                        logger.error(`钱包 ${index} 达到最大重试次数，跳过该钱包`);
                        break;
                    }

                    // 等待后重试
                    const waitTime = getRandomValue(minDelaySeconds, maxDelaySeconds);
                    logger.info(`准备等待 ${waitTime} 秒后重试...`);
                    await sleep(waitTime);
                }
            }
        } catch (err) {
            logger.error(`钱包 ${index} 处理失败: ${err.message}`);
            failedCount++;
            failedIndexes.push(index);
        }
    }

    // 输出统计结果
    const summaryMsg = failedCount > 0
        ? `共处理 ${totalWallets} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个，失败钱包编号: ${failedIndexes.join(', ')}`
        : `共处理 ${totalWallets} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个`;

    logger.success(summaryMsg);
}

main();
