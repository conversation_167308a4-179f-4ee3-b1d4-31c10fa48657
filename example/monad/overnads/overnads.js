import { ethers } from 'ethers';
import logger from "../../../base/tools/logger.js";

// https://magiceden.io/launchpad/monad-testnet/overnads
class OvernadsNFT {
    constructor(rpc) {
        this.rpc = rpc;
        this.tokenId = 0;
        this.contractAddress = "******************************************";
        this.abi = OVERNADS_ABI;
    }

    async getBalance(address) {
        const provider = new ethers.JsonRpcProvider(this.rpc);
        const contract = new ethers.Contract(this.contractAddress, this.abi, provider);
        return await contract.balanceOf(address, this.tokenId);
    }

    async mint(privateKey, walletIndex) {
        const provider = new ethers.JsonRpcProvider(this.rpc);
        const wallet = new ethers.Wallet(privateKey, provider);
        const contract = new ethers.Contract(this.contractAddress, this.abi, wallet);

        const balance = await this.getBalance(wallet.address);
        if (balance > 0) {
            logger.info(`钱包 ${walletIndex} 已经mint过，跳过处理`);
            return {
                success: false,
                code: 'ALREADY_MINTED',
                message: `钱包 ${walletIndex} 已经mint过，跳过处理`,
                data: { balance: balance.toString() }
            };
        }

        try {
            // 准备mint参数
            const qty = 10;
            const limit = 1;
            const proof = [];

            // 获取gas费用
            const feeData = await provider.getFeeData();
            const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;
            const maxFeePerGas = feeData.maxFeePerGas;

            logger.info(`当前gas费用: maxFeePerGas=${ethers.formatUnits(maxFeePerGas, 'gwei')} gwei, maxPriorityFeePerGas=${ethers.formatUnits(maxPriorityFeePerGas, 'gwei')} gwei`);

            // 先尝试估算gas
            try {
                const gasEstimate = await contract.mint.estimateGas(
                    this.tokenId,
                    qty,
                    limit,
                    proof,
                    {
                        maxFeePerGas,
                        maxPriorityFeePerGas
                    }
                );
                logger.info(`预估gas: ${gasEstimate}`);
            } catch (error) {
                // 解析错误信息
                if (error.message.includes("execution reverted")) {
                    return {
                        success: false,
                        code: 'MINT_UNAVAILABLE',
                        message: "Mint失败: 可能是mint未开放或不在白名单中",
                        data: { error: error.message }
                    };
                }
                throw error;
            }

            const tx = await contract.mint(
                this.tokenId,
                qty,
                limit,
                proof,
                {
                    maxFeePerGas,
                    maxPriorityFeePerGas
                }
            );

            logger.info(`钱包 ${walletIndex} mint已发送，等待确认`);
            const receipt = await tx.wait();

            logger.success(`钱包 ${walletIndex} mint成功，交易哈希: ${receipt.hash}`);
            return {
                success: true,
                code: 'SUCCESS',
                message: `钱包 ${walletIndex} mint成功`,
                data: {
                    txHash: receipt.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString()
                }
            };
        } catch (error) {
            // 处理特定的错误类型
            if (error.message.includes("insufficient funds")) {
                return {
                    success: false,
                    code: 'INSUFFICIENT_FUNDS',
                    message: "余额不足，无法支付gas费",
                    data: { error: error.message }
                };
            } else if (error.message.includes("nonce")) {
                return {
                    success: false,
                    code: 'NONCE_ERROR',
                    message: "Nonce错误，请等待之前的交易确认",
                    data: { error: error.message }
                };
            } else if (error.message.includes("already minted")) {
                return {
                    success: false,
                    code: 'ALREADY_MINTED',
                    message: "已经mint过了",
                    data: { error: error.message }
                };
            }

            // 处理其他未知错误
            return {
                success: false,
                code: 'UNKNOWN_ERROR',
                message: `Mint失败: ${error.message}`,
                data: { error: error.message }
            };
        }
    }
}

// Emmy NFT合约ABI
const OVERNADS_ABI = [
    {
        "inputs": [
            {
                "internalType": "uint256",
                "name": "tokenId",
                "type": "uint256"
            },
            {
                "internalType": "uint32",
                "name": "qty",
                "type": "uint32"
            },
            {
                "internalType": "uint32",
                "name": "limit",
                "type": "uint32"
            },
            {
                "internalType": "bytes32[]",
                "name": "proof",
                "type": "bytes32[]"
            }
        ],
        "name": "mint",
        "outputs": [],
        "stateMutability": "payable",
        "type": "function"
    },
    {
        "inputs": [
            {
                "internalType": "address",
                "name": "account",
                "type": "address"
            },
            {
                "internalType": "uint256",
                "name": "id",
                "type": "uint256"
            }
        ],
        "name": "balanceOf",
        "outputs": [
            {
                "internalType": "uint256",
                "name": "",
                "type": "uint256"
            }
        ],
        "stateMutability": "view",
        "type": "function"
    }
];

export { OvernadsNFT, OVERNADS_ABI };
