import { ethers, formatEther, parseEther } from "ethers";
import { Wallet } from "../../../base/evm/wallet.js";

import logger from "../../../base/tools/logger.js";
import { retry, getHumanNumber } from "../../../base/tools/common.js";

// https://www.magmastaking.xyz/
class Magma {
  constructor(privateKey, proxy, rpc) {
    this.privateKey = privateKey;
    this.proxy = proxy;
    this.rpc = rpc;
    this.provider = new ethers.JsonRpcProvider(this.rpc);
    this.wallet = new Wallet(privateKey, rpc);


    // 合约地址
    this.STAKE_ADDRESS = "******************************************";
    this.STAKED_TOKEN = "******************************************";

    // 合约ABI
    this.STAKE_ABI = [
      {
        type: "function",
        name: "stake",
        inputs: [],
        outputs: [],
        stateMutability: "payable",
      },
      {
        type: "function",
        name: "withdrawMon",
        inputs: [{ name: "amount", type: "uint256" }],
        outputs: [],
        stateMutability: "nonpayable",
      },
    ];

    // 配置
    this.config = {
      stakeAmount: {
        min: 0.001,
        max: 0.01,
      },
    };
  }

  async getStakedTokenBalance() {
    try {
      const contract = new ethers.Contract(
        this.STAKED_TOKEN,
        ["function balanceOf(address) view returns (uint256)"],
        this.provider,
      );
      const balance = await retry(async () => await contract.balanceOf(this.wallet.getAddress()), 3, 2000);
      return balance;
    } catch (error) {
      logger.error(`获取质押代币余额失败: ${error.message}`);
      return ethers.parseEther("0");
    }
  }

  /**
   * 获取质押金额
   * @param {number} maxAmount - 最大质押金额
   * @returns {number}
   */
  getStakeAmount(maxAmount = 0.01) {
    // const amounts = [
    //   ...Array(9)
    //     .fill()
    //     .map((_, i) => +(0.001 * (i + 1)).toFixed(3)),
    //   ...Array(9)
    //     .fill()
    //     .map((_, i) => +(0.01 * (i + 1)).toFixed(2)),
    //   0.1,
    // ];

    // // 获取小于等于输入金额的值
    // const filteredAmounts = amounts.filter((a) => a <= maxAmount);

    // // 如果没有找到合适的金额，返回最小值
    // if (filteredAmounts.length === 0) {
    //   return amounts[0].toString();
    // }

    // // 随机返回一个符合条件的金额
    // const selectedAmount = filteredAmounts[Math.floor(Math.random() * filteredAmounts.length)];

    // // 返回字符串格式，方便后续转换为 BigInt
    // return selectedAmount.toString();
    return getHumanNumber(maxAmount);
  }

  async stakeMon() {
    try {
      const stakedAmount = await this.getStakedTokenBalance();
      logger.info(`当前质押数量: ${formatEther(stakedAmount)}MON`);
      if (stakedAmount > 3n) {
        return {
          success: true,
          code: "ALREADY_STAKE",
          message: `已经质押(${formatEther(stakedAmount)}MON)，跳过质押`,
          data: {
            amount: formatEther(stakedAmount),
          },
        };
      }


      const balance = await this.provider.getBalance(this.wallet.getAddress());
      logger.info(`当前余额: ${ethers.formatEther(balance)}MON`);

      const amount = this.getStakeAmount(ethers.formatEther(balance));
      const amountInWei = ethers.parseEther(amount.toString());

      if (balance < amountInWei) {
        const formattedBalance = formatEther(balance);
        const formattedAmount = formatEther(amountInWei);

        logger.error(`余额不足, 当前余额: ${formattedBalance}MON, 需要: ${formattedAmount}MON`);
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: `余额不足支付质押费用`,
          data: {
            balance: formattedBalance,
            required: formattedAmount,
          },
        };
      }

      logger.info(`准备质押 ${ethers.formatEther(amountInWei)}MON`);

      // 发送质押交易
      const tx = await this.wallet.sendTransaction({
        to: this.STAKE_ADDRESS,
        value: amountInWei,
        data: "0xd5575982", // stake方法签名
      });

      const txHash = tx.hash;
      try {
        const receipt = await tx.wait();
        if (receipt.status !== 1) {
          throw new Error(`质押失败 | Hash: ${txHash} | Status: ${receipt.status}`);
        }
        logger.success(`质押成功`);
        return {
          success: true,
          code: "STAKE_SUCCESS",
          message: `质押成功 | Hash: ${txHash}`,
          data: {
            amount: ethers.formatEther(amountInWei),
            hash: txHash,
          },
        };
      } catch (error) {
        logger.error(`质押失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
        };
      }
    } catch (error) {
      logger.error(`质押失败: ${error.message}`);
      if (error.message.includes("insufficient balance")) {
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: error.message,
        };
      }
      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
      };
    }
  }

  async requestUnstake(isSkip = false) {
    try {
      // 检查质押余额
      const stakedAmount = await this.getStakedTokenBalance();
      const formattedStakedAmount = formatEther(stakedAmount);
      logger.info(`当前质押数量: ${formattedStakedAmount}MON`);
      if (formattedStakedAmount > 3 && isSkip) {
        return {
          success: true,
          code: "ALREADY_STAKE",
          message: `大额质押(${formattedStakedAmount}MON): 跳过取消质押`,
          data: {
            amount: formattedStakedAmount,
          },
        };
      }
      if (formattedStakedAmount <= 0) {
        logger.info(`没有找到可解除质押的代币`);
        return {
          success: true,
          code: "ZERO_AMOUNT",
          message: `没有找到可解除质押的代币`,
        };
      }

      logger.info(`准备解除质押: ${ethers.formatEther(stakedAmount)}MON`);

      // 发送解除质押交易
      const contract = new ethers.Contract(this.STAKE_ADDRESS, this.STAKE_ABI, this.wallet);
      const tx = await contract.withdrawMon(stakedAmount);

      let txHash = tx.hash;
      logger.info(`请求取消质押交易已发送 | Hash: ${txHash}`);


      try {
        const receipt = await tx.wait();
        if (receipt.status !== 1) {
          throw new Error(`请求取消质押失败 | Status: ${receipt.status}`);
        }

        logger.success(`请求取消质押成功 | Hash: ${txHash}`);
        return {
          success: true,
          code: "UNSTAKE_SUCCESS",
          message: `请求取消质押成功 | Hash: ${txHash}`,
          data: {
            amount: formattedStakedAmount,
            hash: txHash,
          },
        };
      } catch (error) {
        logger.error(`请求取消质押失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          data: {
            amount: formattedStakedAmount,
            hash: txHash,
          },
        };
      }
    } catch (error) {
      logger.error(`请求取消质押失败: ${error.message}`);
      return {
        success: false,
        code: "UNSTAKE_FAILED",
        message: error.message,
      };
    }
  }
}

export default Magma;
