import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";

// https://app.demask.finance/launchpad/******************************************/0
class DeMask{
  constructor(rpc) {
    this.rpc = rpc;
    this.contractAddress = "******************************************";
    this.nftContractAddress = "******************************************";
    this.abi = DeMask_ABI;
  }

  async getBalance(address, contractAddress=this.nftContractAddress) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const contract = new ethers.Contract(contractAddress, this.abi, provider);
    return await contract.balanceOf(address);
  }

  generateMintData(quantity) {
    const baseInput = "0x8fce8f2f000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000f4240";
    const quantityHex = quantity.toString(16).padStart(64, '0');
    const padding = "0000000000000000000000000000000000000000000000000000000000000000";

    return baseInput + quantityHex + padding;
  }

  async mint(privateKey, walletIndex, quantity = 1) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(privateKey, provider);
    const contract = new ethers.Contract(this.contractAddress, this.abi, wallet);

    const balance = await this.getBalance(wallet.address);
    if (balance > 0) {
        logger.info(`钱包 ${walletIndex} 已经mint过，跳过处理`);
        return {
            success: false,
            code: 'ALREADY_MINTED',
            message: `钱包 ${walletIndex} 已经mint过，跳过处理`,
            data: { balance: balance.toString() }
        };
    }

    try {
      const mintData = this.generateMintData(quantity);

      const totalMintPrice = ethers.parseEther((0.1 * quantity).toString());

      const walletBalance = await provider.getBalance(wallet.address);
      const requiredAmount = totalMintPrice + ethers.parseEther("0.1");

      if (walletBalance < requiredAmount) {
        const formattedBalance = ethers.formatEther(walletBalance);
        const formattedPrice = ethers.formatEther(requiredAmount);
        logger.error(
          `钱包 ${walletIndex} 余额不足: ${formattedBalance} MON, 需要: ${formattedPrice} MON`,
        );
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: `余额不足，无法支付mint费用`,
          data: {
            balance: formattedBalance,
            required: formattedPrice,
            missing: ethers.formatEther(totalMintPrice - walletBalance),
          },
        };
      }

      const txHash = await buildAndSendTransaction(
        this.rpc,
        privateKey,
        this.contractAddress,
        mintData,
        totalMintPrice.toString(),
      );

      logger.info(`钱包 ${walletIndex} mint已发送，等待确认`);
      const receipt = await txHash.wait();

      logger.success(`钱包 ${walletIndex} mint成功，交易哈希: ${receipt.hash}`);
      return {
          success: true,
          code: 'SUCCESS',
          message: `钱包 ${walletIndex} mint成功`,
          data: {
              txHash: receipt.hash,
              blockNumber: receipt.blockNumber,
              gasUsed: receipt.gasUsed.toString()
          }
      };
    } catch (error) {
        if (error.message.includes("insufficient funds")) {
            return {
                success: false,
                code: 'INSUFFICIENT_FUNDS',
                message: "余额不足，无法支付gas费",
                data: { error: error.message }
            };
        } else if (error.message.includes("nonce")) {
            return {
                success: false,
                code: 'NONCE_ERROR',
                message: "Nonce错误，请等待之前的交易确认",
                data: { error: error.message }
            };
        } else if (error.message.includes("already minted")) {
            return {
                success: false,
                code: 'ALREADY_MINTED',
                message: "已经mint过了",
                data: { error: error.message }
            };
        }

        return {
            success: false,
            code: 'UNKNOWN_ERROR',
            message: `Mint失败: ${error.message}`,
            data: { error: error.message }
        };
    }
  }
}

// 只保留 balanceOf 函数的 ABI
const DeMask_ABI = [
  {
    "inputs": [
        {
            "internalType": "address",
            "name": "owner",
            "type": "address"
        }
    ],
    "name": "balanceOf",
    "outputs": [
        {
            "internalType": "uint256",
            "name": "balance",
            "type": "uint256"
        }
    ],
    "stateMutability": "view",
    "type": "function"
}
];

export { DeMask, DeMask_ABI };
