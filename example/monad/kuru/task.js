import CSV from "../../../base/tools/csv.js";
import {
  filterWalletsByIndex,
  parseTOMLConfig,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import KuruSwap from "./kuru.js";
import logger from "../../../base/tools/logger.js";

const configPath = resolveFromModule(import.meta.url, "../config.toml");
const monadConfig = parseTOMLConfig(configPath).monad;
const rpc = monadConfig.rpc;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  for (let wallet of shuffledWallets) {
    const private_key = wallet.private_key;
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    logger.info(`${index}  ${address} 开始执行任务`);
    const kuru = new KuruSwap(private_key, proxy, rpc);
    await kuru.executeSwapRandom();
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    logger.success(`任务完成: ${index} ${address}, 耗时 ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (index === shuffledWallets.length) {
      break;
    }

    // 任务间随机延迟 5-15 秒
    const delay = getRandomValue(5, 25);
    await sleep(delay);
  }
}

// 执行任务
// node example/monad/domain/task.js 1-10
main();
