import { ethers } from "ethers";
import { Pair, Route } from "@uniswap/v2-sdk";
import { C<PERSON><PERSON>cyAmount, Token, Percent } from "@uniswap/sdk-core";

import logger from "../../../base/tools/logger.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { parseTOMLConfig, getRandomValue, sleep } from "../../../base/tools/common.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad || {};
const rpc = monadConfig.rpc || "https://testnet-rpc.monad.xyz/";
const defaultProxy = config.PROXY_URL || "";

export default class KuruSwap {
  /**
   * 创建 NAD 域名管理实例
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpc - RPC 节点地址
   */
  constructor(privateKey, proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.proxy = proxy || defaultProxy;
    this.logger = logger;

    this.V2_ROUTER_ADDRESS = "0x4c4eABd5Fb1D1A7234A48692551eAECFF8194CA7";
    this.V2_ROUTER_ABI = [
      "function factoryV2() external view returns (address)",
      "function multicall(uint256, bytes[] calldata) external payable",
      "function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to)",
    ];
    this.V2_FACTORY_ADDRESS = "******************************************";
    this.V2_FACTORY_ABI = [
      "function getPair(address tokenA, address tokenB) external view returns (address pair)",
    ];
    this.uniswapV2poolABI = [
      "function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)",
      "function token0() external view returns (address)",
      "function token1() external view returns (address)",
    ];
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.wallet = new ethers.Wallet(privateKey, this.provider);
    this.chainId = monadConfig.chainId;
    this.WMON_ADDRESS = "******************************************";
    this.CHOG_ADDRESS = "******************************************";
    this.CHOG_PAIR_ADDRESS = "******************************************";
    this.DAK_ADDRESS = "******************************************";
    this.DAK_PAIR_ADDRESS = "******************************************";
    this.YAKI_ADDRESS = "******************************************";
    this.YAKI_PAIR_ADDRESS = "******************************************";
    this.inputToken = new Token(this.chainId, this.WMON_ADDRESS, 18, "WMON", "WMON");
  }

  async getPoolData(outputToken, pairAddress) {
    // 获取工厂地址，不要删除这个注释掉的代码
    // const router = new ethers.Contract(this.V2_ROUTER_ADDRESS, this.V2_ROUTER_ABI, this.provider)
    // const factoryAddress = await router.factoryV2()
    // console.log('factoryAddress', factoryAddress)
    // const factoryContract = new ethers.Contract(this.V2_FACTORY_ADDRESS, this.V2_FACTORY_ABI, this.provider)
    // const pairAddress = await factoryContract.getPair(this.inputToken.address, outputToken.address)
    const pairContract = new ethers.Contract(pairAddress, this.uniswapV2poolABI, this.provider);
    // console.log('pairAddress', pairAddress)
    // 获取储备量（reserves）
    const reserves = await pairContract.getReserves();
    const [reserve0, reserve1] = reserves;
    const token0_address = await pairContract.token0();
    let token0 = undefined;
    let token1 = undefined;
    if (token0_address.toLowerCase() === this.inputToken.address.toLowerCase()) {
      token0 = this.inputToken;
      token1 = outputToken;
    } else {
      token0 = outputToken;
      token1 = this.inputToken;
    }

    // 计算当前价格（输出代币/输入代币）
    const pair = new Pair(
      CurrencyAmount.fromRawAmount(token0, reserve0.toString()),
      CurrencyAmount.fromRawAmount(token1, reserve1.toString()),
    );
    const route = new Route([pair], this.inputToken, outputToken);
    const midPrice = route.midPrice.toSignificant(6);
    this.logger.info(`${outputToken.symbol}/${this.inputToken.symbol} 当前价格: ${midPrice}`);

    return { pair, route };
  }

  async calculateSwapParams(outputToken, amountIn, pairAddress) {
    const { pair, route } = await this.getPoolData(outputToken, pairAddress);

    // 用户指定的输入代币数量（示例：0.01 WMON）
    const amountInBigNumber = ethers.parseUnits(amountIn, this.inputToken.decimals);

    // 根据池子价格和滑点计算最小输出代币数量
    const slippageTolerance = new Percent(5, 100); // 5% 滑点
    const amountsOut = route.midPrice.quote(
      CurrencyAmount.fromRawAmount(this.inputToken, amountInBigNumber.toString()),
    );
    const amountOutMin = amountsOut
      .multiply(new Percent(1).subtract(slippageTolerance))
      .toFixed(outputToken.decimals);

    return {
      amountInBigNumber: amountInBigNumber.toString(),
      amountOutMin: ethers.parseUnits(amountOutMin, outputToken.decimals).toString(),
      path: [this.inputToken.address, outputToken.address], // 交易路径
    };
  }

  async buildMulticallData(outputToken, amountIn, pairAddress) {
    const { amountInBigNumber, amountOutMin, path } = await this.calculateSwapParams(
      outputToken,
      amountIn,
      pairAddress,
    );
    const deadline = Math.floor(Date.now() / 1000) + 60 * 20; // 20 分钟有效期

    // 编码 swapExactTokensForTokens
    const swapCalldata = new ethers.Interface(this.V2_ROUTER_ABI).encodeFunctionData(
      "swapExactTokensForTokens",
      [amountInBigNumber, amountOutMin, path, this.wallet.address],
    );

    // 编码 Multicall
    return new ethers.Interface(this.V2_ROUTER_ABI).encodeFunctionData("multicall", [
      deadline,
      [swapCalldata],
    ]);
  }

  /**
   * 执行交易
   * @param {string} tokenOut - 输出代币地址，CHOG 或 DAK, YAKI
   * @param {string} amountIn - 输入代币数量，单位为 MON数量
   * @returns {Promise<boolean>} - true: 成功, false: 失败
   */
  async executeSwap(tokenOut, amountIn) {
    try {
      // 1. 基本验证
      if (!tokenOut) throw new Error("输出代币地址不能为空");
      if (!amountIn) throw new Error("输入代币数量不能为空");
      let tokenOutAddress = undefined;
      let pairAddress = undefined;
      if (tokenOut === "CHOG") {
        tokenOutAddress = this.CHOG_ADDRESS;
        pairAddress = this.CHOG_PAIR_ADDRESS;
      } else if (tokenOut === "DAK") {
        tokenOutAddress = this.DAK_ADDRESS;
        pairAddress = this.DAK_PAIR_ADDRESS;
      } else if (tokenOut === "YAKI") {
        tokenOutAddress = this.YAKI_ADDRESS;
        pairAddress = this.YAKI_PAIR_ADDRESS;
      }

      if (!tokenOutAddress) {
        this.logger.error("输出代币名称错误，必须是CHOG, DAK, YAKI");
        return false;
      }
      const outputToken = new Token(this.chainId, tokenOutAddress, 18, tokenOut, tokenOut);
      // 编码Multicall
      const multicallData = await this.buildMulticallData(outputToken, amountIn, pairAddress);

      const feeData = await this.provider.getFeeData();
      let fee = feeData.maxFeePerGas ? {
        maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
        maxFeePerGas: feeData.maxFeePerGas,
        type: 2  // EIP-1559交易
      } : {
        gasPrice: feeData.gasPrice,
        type: 0  // 传统交易
      };

      // 修正金额转换
      const parsedAmount = ethers.parseEther(amountIn.toString());

      // 估算gasLimit - 修正：应该发送到路由合约地址而不是代币地址
      const gasLimit = await this.provider.estimateGas({
        to: this.V2_ROUTER_ADDRESS,  // 修改为路由合约地址
        value: parsedAmount,
        data: multicallData,
      });

      const maxFeePerGas = fee.maxFeePerGas || fee.gasPrice;
      const gasCost = gasLimit * maxFeePerGas;
      const totalCost = parsedAmount + gasCost;
      const balance = await this.provider.getBalance(this.wallet.address);

      if (BigInt(balance) < totalCost) {
        this.logger.error(`余额不足，需要 ${ethers.formatEther(totalCost)} MON，当前余额 ${ethers.formatEther(balance)} MON`);
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: `余额不足，需要 ${ethers.formatEther(totalCost)} MON，当前余额 ${ethers.formatEther(balance)} MON`,  // 修正变量名
        };
      }

      // 发送交易
      const tx = await this.wallet.sendTransaction({
        to: this.V2_ROUTER_ADDRESS,
        data: multicallData,
        value: parsedAmount,  // 使用已转换的金额
      });
      this.logger.info(`${tokenOut}交易哈希: ${tx.hash}`);
      const tx_wait = await tx.wait();
      if (tx_wait.status) {
        this.logger.success(`${tokenOut}交易 成功`);
        return true;
      } else {
        this.logger.error(`${tokenOut}交易 失败`);
        return false;
      }
    } catch (error) {
      this.logger.error(`执行${tokenOut}交易失败: ${error.message}`);
      return false;
    }
  }

  /**
   *
   */
  async executeSwapRandom(minTokenType = 1, maxTokenType = 3) {
    try {
      // 随机执行CHOG, DAK, YAKI
      const tokenOuts = ["CHOG", "DAK", "YAKI"]
        .sort(() => Math.random() - 0.5)
        .slice(0, getRandomValue(minTokenType, maxTokenType));

      logger.info(`准备执行的代币序列: ${tokenOuts.join(", ")}`);

      for (let i = 0; i < tokenOuts.length; i++) {
        const tokenOut = tokenOuts[i];
        try {
          const amountIn = getRandomValue(1, 5) / 1000;
          logger.info(`开始执行 ${tokenOut} 交易，数量: ${amountIn.toFixed(3)}`);

          await this.executeSwap(tokenOut, amountIn.toFixed(3));

          // 如果不是最后一个代币，添加延迟
          if (i < tokenOuts.length - 1) {
            const delay = getRandomValue(5, 30);
            logger.info(`随机延迟 ${delay} 秒执行下一个swap`);
            await sleep(delay);
          }
        } catch (error) {
          logger.error(`${tokenOut} 交易执行失败: ${error.message}`);
          continue;
        }
      }
    } catch (error) {
      logger.error(`随机交易执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行全部交易
   */
  async executeSwapAll() {
    // 随机打乱顺序执行CHOG, DAK, YAKI, 每个代币交易数量从0.01到0.05随机
    const tokenOuts = ["CHOG", "DAK", "YAKI"].sort(() => Math.random() - 0.5);
    logger.info(tokenOuts);
    for (let tokenOut of tokenOuts) {
      const amountIn = getRandomValue(1, 5) / 1000;
      await this.executeSwap(tokenOut, amountIn.toFixed(3));
      // 任务间随机延迟 5-15 秒
      if (tokenOut != tokenOuts[-1]) {
        this.logger.info("随机延迟 5-30 秒执行下一个swap");
        const delay = getRandomValue(5, 30);
        await sleep(delay);
      }
    }
  }
}
