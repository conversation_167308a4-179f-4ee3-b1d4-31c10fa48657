import { ethers } from "ethers";
import axios from "axios";
import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";
import CSV from "../../../base/tools/csv.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import fs from "fs";
import { HttpsProxyAgent } from "https-proxy-agent";
import { getHeaders } from "../../../base/tools/fake_useragent.js";
import { faker } from '@faker-js/faker';


class Caddy {
  constructor(privateKey, walletIndex, rpc, proxy) {
    if (!privateKey || !walletIndex || !rpc) {
      throw new Error('必须提供私钥、钱包索引和RPC地址');
    }
    this.privateKey = privateKey;
    this.walletIndex = walletIndex;
    this.proxy = proxy;
    this.rpc = rpc;
    this.wallet = new Wallet(privateKey, rpc);
    this.address = this.wallet.getAddress();
    this.provider = new ethers.JsonRpcProvider(rpc);
    this.baseUrl = 'https://caddy-finance-backend-production-6f7f.up.railway.app/api';
    this.frontendUrl = 'https://alpha.caddy.finance';
    this.chainId = '10143';
    this.jwt = null;
    this.underlyingToken = '******************************************';
    this.optionContract = '******************************************';

    this.csvPath = resolveFromModule(import.meta.url, './caddy_users.csv');

    // 在构造函数末尾初始化用户数据
    this.#initUserData();

    // ERC20 ABI
    this.tokenAbi = [
      "function balanceOf(address) view returns (uint256)",
      "function decimals() view returns (uint8)",
      "function approve(address spender, uint256 amount) returns (bool)",
      "function allowance(address,address) view returns (uint256)",
      "function getCurrentTimestamp() view returns (uint256)",
      "function openPosition(address, uint256, uint256, uint256, uint8, uint256, uint256) returns (uint256)",
      "function optionPosition() view returns (address)",
      "function withdrawETH(uint256)",
      "function withdrawPremium(uint256)",
      "function withdrawToken(address)"
    ];

    // 创建请求配置
    const axiosConfig = {
      timeout: 30000,
      headers: getHeaders("https://alpha.caddy.finance", this.walletIndex)
    };

    // 如果提供了代理，添加代理配置
    if (proxy) {
      const proxyAgent = new HttpsProxyAgent(proxy);
      axiosConfig.httpsAgent = proxyAgent;
    }

    this.axiosInstance = axios.create(axiosConfig);
  }

  async #initUserData() {
    try {
      // 添加文件锁机制
      const lockFile = `${this.csvPath}.lock`;

      // 等待获取文件锁
      while (true) {
        try {
          // 尝试创建锁文件
          const fd = await fs.promises.open(lockFile, 'wx');
          await fd.close();
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            // 如果锁存在，等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 100));
            continue;
          }
          throw err;
        }
      }

      try {
        // 检查文件是否存在
        const exists = await CSV.exists(this.csvPath);

        // 如果文件不存在，创建新文件
        if (!exists) {
          await CSV.write(this.csvPath, [], {
            header: true,
            columns: ['index', 'address', 'referralCode', 'referredBy']
          });
          logger.info('初始化caddy_users.csv文件成功');
        }

        // 读取并格式化数据
        const records = await CSV.read(this.csvPath);

        // 使用原子操作更新内存数据
        const userDataMap = {};
        const referralCodesSet = new Set();

        for (const record of records) {
          if (record.address) {
            userDataMap[record.address] = record;
            if (record.referralCode) {
              referralCodesSet.add(record.referralCode);
            }
          }
        }

        // 原子性地更新实例变量
        this.userData = userDataMap;
        this.referralCodes = Array.from(referralCodesSet);

      } finally {
        // 释放文件锁
        try {
          await fs.promises.unlink(lockFile);
        } catch (err) {
          logger.error(`释放文件锁失败: ${err.message}`);
        }
      }

    } catch (error) {
      logger.error(`初始化用户数据失败: ${error.message}`);
      this.userData = {};
      this.referralCodes = [];
    }
  }

  // 添加一个安全的更新方法
  async #updateUserData(newUserData) {
    const lockFile = `${this.csvPath}.lock`;

    try {
      // 获取文件锁
      while (true) {
        try {
          const fd = await fs.promises.open(lockFile, 'wx');
          await fd.close();
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            await new Promise(resolve => setTimeout(resolve, 100));
            continue;
          }
          throw err;
        }
      }

      try {
        // 读取当前数据
        const records = await CSV.read(this.csvPath);

        // 更新或添加新数据
        const existingIndex = records.findIndex(r => r.address === newUserData.address);
        if (existingIndex !== -1) {
          records[existingIndex] = newUserData;
        } else {
          records.push(newUserData);
        }

        // 写入更新后的数据
        await CSV.write(this.csvPath, records, {
          header: true,
          columns: ['index', 'address', 'referralCode', 'referredBy']
        });

        // 更新内存中的数据
        this.userData[newUserData.address] = newUserData;
        if (newUserData.referralCode && !this.referralCodes.includes(newUserData.referralCode)) {
          this.referralCodes.push(newUserData.referralCode);
        }

      } finally {
        // 释放文件锁
        await fs.promises.unlink(lockFile);
      }

    } catch (error) {
      logger.error(`更新用户数据失败: ${error.message}`);
      throw error;
    }
  }

  generateSignMessage() {
    //     const now = new Date();
    //     const nonce = Math.floor(Math.random() * 1000);

    //     return `alpha.caddy.finance wants you to sign in with your **blockchain** account:
    // ${address}

    // URI: ${this.frontendUrl}/
    // Version: 1
    // Chain ID: eip155:${this.chainId}
    // Nonce: ${nonce}
    // Issued At: ${now.toISOString()}`;
    return "Login to Caddy Finance"
  }

  generateUserInfo() {
    const emailDomains = [
      'gmail.com',
      'yahoo.com',
      'hotmail.com',
      'outlook.com',
      'icloud.com',
      'protonmail.com',
      'aol.com',
      'zoho.com'
    ];

    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const domain = faker.helpers.arrayElement(emailDomains);
    const email = faker.internet.email({ firstName, lastName, provider: domain });

    return {
      firstName,
      lastName,
      email,
      telegramId: "",
      twitterId: ""
    };
  }

  async login() {
    try {
      const message = this.generateSignMessage();
      const signature = await this.wallet.signMessage(message);

      const response = await this.axiosInstance.post(`${this.baseUrl}/auth/verify-wallet`, {
        message: message,
        signature: signature,
        address: this.address,
        referralCode: ""
      });

      if (!response.data.success) {
        throw new Error(`${response.data.meta.message}`);
      }

      this.jwt = response.data.data.token;
      this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${this.jwt}`;
      logger.success(`钱包 [${this.walletIndex}] ${this.address} 登录成功`);

      return true;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 登录失败: ${error.response?.data || error.message}`);
      throw error;
    }
  }

  async referer() {
    try {
      let referralCode;

      // 检查当前地址是否已有推荐数据
      if (this.userData[this.address]) {
        return true;
      }

      // 如果没有推荐数据，从现有推荐码中随机选择一个
      if (this.referralCodes.length > 0) {
        const randomIndex = Math.floor(Math.random() * this.referralCodes.length);
        referralCode = this.referralCodes[randomIndex];
        logger.info(`钱包 [${this.walletIndex}] ${this.address} 使用随机推荐码: ${referralCode}`);
      } else {
        logger.warn(`钱包 [${this.walletIndex}] ${this.address} 没有找到推荐码`);
        return true;
      }


      const response = await this.axiosInstance.post(`${this.baseUrl}/referral/referral-login`, {
        referralCode
      });

      if (!response.data.success) {
        if (response.data.meta.error === "ALREADY_REFERRED") {
          logger.info(`钱包 [${this.walletIndex}] ${this.address} 已经邀请过`);
          return true;
        }
        logger.error(`钱包 [${this.walletIndex}] ${this.address} 邀请失败: ${response.data.meta.message}`);
        return false;
      }

      // 保存新的推荐关系到CSV
      const newUserData = {
        index: this.walletIndex,
        address: this.address,
        referralCode: response.data.data.newReferralCode,
        referredBy: referralCode
      };

      await this.#updateUserData(newUserData);

      logger.success(`钱包 [${this.walletIndex}] ${this.address} 邀请成功`);
      return true;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 邀请异常: ${error.response?.data || error.message}`);
      return false;
    }
  }

  async checkUserDetails() {
    try {
      const response = await this.axiosInstance.get(`${this.baseUrl}/auth/hasUserDetails`);
      return response.data.data.hasDetails;
    } catch (error) {
      logger.error('检查用户信息失败:', error.response?.data || error.message);
      throw error;
    }
  }

  async getUserDetails() {
    try {
      if (this.userData[this.address]) {
        return true;
      }

      const response = await this.axiosInstance.get(`${this.baseUrl}/auth/userDetails`);
      if (!response.data.success) {
        logger.error(`钱包 [${this.walletIndex}] ${this.address} 获取用户信息失败: ${response.data.meta.message}`);
        return false;
      }
      const { referralCode, referredBy } = response.data.data;

      const newUserData = {
        index: this.walletIndex,
        address: this.address,
        referralCode,
        referredBy
      };

      // 添加到CSV文件
      await this.#updateUserData(newUserData);

      // 更新内存中的数据
      this.userData[this.address] = newUserData;
      if (referralCode && !this.referralCodes.includes(referralCode)) {
        this.referralCodes.push(referralCode);
      }
      return true;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 获取用户信息异常: ${error.response?.data.meta.message || error.message}`);
      return false;
    }
  }

  async setUserDetails() {
    try {
      const userInfo = this.generateUserInfo();

      // 打印用户信息
      logger.info(`钱包 [${this.walletIndex}] ${this.address} 设置用户信息: firstName=${userInfo.firstName}, lastName=${userInfo.lastName}, email=${userInfo.email}`);

      const response = await this.axiosInstance.post(`${this.baseUrl}/auth/userDetails`, userInfo);
      if (!response.data.success) {
        logger.error(`钱包 [${this.walletIndex}] ${this.address} 设置用户信息失败: ${response.data.meta.message}`);
        throw new Error(`${response.data.meta.message}`);
      }

      logger.success(`钱包 [${this.walletIndex}] ${this.address} 设置用户信息成功!`);
      return true;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 设置用户信息异常: ${error.response?.data || error.message}`);
      throw error;
    }
  }

  async faucet() {
    try {
      const response = await this.axiosInstance.get(`${this.baseUrl}/transactions/faucet`);
      if (!response.data.success) {
        if (response.data.meta.error === "COOLDOWN_PERIOD") {
          logger.info(`钱包 [${this.walletIndex}] ${this.address} 已经领取stMON`);
          return true
        }
        logger.error(`钱包 [${this.walletIndex}] ${this.address} 领取stMON失败: ${response.data.meta.message}`);
        throw new Error(`${response.data.meta.message}`);
      }

      logger.success(`钱包 [${this.walletIndex}] ${this.address} 领取stMON成功`);

      // 7. 等待几秒后查询余额
      await new Promise(resolve => setTimeout(resolve, 5000));
      const balance = await this.getBalance();
      logger.info(`钱包 [${this.walletIndex}] ${this.address} stMON余额: ${balance}`);

      return true;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 领取stMON异常: ${error.response?.data || error.message}`);
      throw error;
    }
  }

  async getBalance() {
    try {
      const contract = new ethers.Contract(this.underlyingToken, this.tokenAbi, this.provider);

      const [balance, decimals] = await Promise.all([
        contract.balanceOf(this.address),
        contract.decimals()
      ]);

      return ethers.formatUnits(balance, decimals);
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 查询stMON余额失败: ${error.message}`);
      return "0";
    }
  }

  async getAllowance(owner, spender) {
    const contract = new ethers.Contract(this.underlyingToken, this.tokenAbi, this.provider);
    const [allowance, decimals] = await Promise.all([
      contract.allowance(owner, spender),
      contract.decimals()
    ]);
    const formatted = ethers.formatUnits(allowance, decimals);
    return formatted;
  }

  async approve(spender, amount) {
    try {
      const wallet = new ethers.Wallet(this.privateKey, this.provider);
      const contract = new ethers.Contract(this.underlyingToken, this.tokenAbi, wallet);

      const approveAmount = amount === -1 ? ethers.MaxUint256 : amount;

      logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始授权...`);
      const tx = await contract.approve(spender, approveAmount);
      logger.info(`钱包 [${this.walletIndex}] ${this.address} 发送授权: ${tx.hash}`);

      const receipt = await tx.wait();
      logger.success(`钱包 [${this.walletIndex}] ${this.address} 授权成功!`);

      return receipt;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 授权失败: ${error.message}`);
      throw error;
    }
  }

  async init() {
    try {
      // 1. 登录
      await this.login();
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 2. 邀请
      await this.referer();

      // 3. 检查用户信息
      const hasDetails = await this.checkUserDetails();
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 4. 获取用户信息, 保存referralCode和referredBy
      await this.getUserDetails();

      // 5. 如果没有设置用户信息，则设置
      if (!hasDetails) {
        await this.setUserDetails();
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      return true;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 初始化过程出错: ${error.message}`);
      throw error;
    }
  }

  async getPrice() {
    // 保存原有的headers
    const originalHeaders = { ...this.axiosInstance.defaults.headers };
    try {
      const priceId = '0xe786153cc54abd4b0e53b4c246d54d9f8eb3f3b5a34d4fc5a2e9a423b0ba5d6b';
      const url = `https://hermes-beta.pyth.network/v2/updates/price/latest?ids[]=${priceId}`;

      // 创建临时headers配置
      const headers = {
        'accept': '*/*',
        'accept-language': 'zh-HK,zh;q=0.9,zh-TW;q=0.8',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'Referer': 'https://alpha.caddy.finance/',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      };

      // 设置新的headers
      this.axiosInstance.defaults.headers = headers;

      const response = await this.axiosInstance.get(url);
      const priceData = response.data.parsed[0].price;
      const price = (parseFloat(priceData.price) * Math.pow(10, priceData.expo)).toFixed(2);
      // 恢复原有的headers
      this.axiosInstance.defaults.headers = originalHeaders;
      logger.success(`钱包 [${this.walletIndex}] ${this.address} 当前价格：${price}`);
      return price;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 获取价格失败: ${error.message}`);
      throw error;
    } finally {
      // 恢复原有的headers
      this.axiosInstance.defaults.headers = originalHeaders;
    }
  }

  async openPosition() {
    try {
      // 获取当前价格
      const currentPrice = await this.getPrice();
      const optionsChains = await this.getOptionsChain(currentPrice);

      // 随机选择一个chain
      const chain = optionsChains[Math.floor(Math.random() * optionsChains.length)];
      const optionType = chain.type;  // CALLS or PUTS
      const optionSide = chain.side; // BUY or SELL

      // 随机选择一个价格
      const priceOption = chain.price[Math.floor(Math.random() * chain.price.length)];
      const strikePrice = priceOption.strike;

      const balanceFormatted = await this.getBalance();
      // 生成0.01到balanceFormatted之间的随机数
      const minAmount = 0.01;
      const maxAmount = Number(balanceFormatted);
      const optionQuantity = Number((Math.random() * (maxAmount - minAmount) + minAmount).toFixed(2));

      // 计算总金额
      const optionPrice = optionSide === "BUY" ? priceOption.ask : priceOption.bid;
      const totalCost = optionQuantity * optionPrice;
      const totalAmount = Number(parseFloat(totalCost / currentPrice).toFixed(4));
      // 转换为wei
      const totalAmountInWei = ethers.parseEther(totalAmount.toString());

      // 检查余额
      if (Number(balanceFormatted) < totalAmount) {
        logger.error(`钱包 [${this.walletIndex}] ${this.address} stMON余额不足`);
        return;
      }

      // 检查授权额度
      const approveAmount = await this.getAllowance(this.address, this.optionContract);
      if (Number(approveAmount) < totalAmount) {
        // 授权额度不足，设置为-1（无限授权）
        await this.approve(this.optionContract, -1);
      }

      // 确定期权类型
      const positionType = optionType === "CALLS"
        ? (optionSide === "BUY" ? "LONG_CALL" : "SHORT_CALL")
        : (optionSide === "BUY" ? "LONG_PUT" : "SHORT_PUT");

      const positionTypeId = positionType === "LONG_CALL" ? 0
        : positionType === "SHORT_CALL" ? 1
          : positionType === "LONG_PUT" ? 2
            : 3;

      // 获取当前时间戳
      const contract = new ethers.Contract(this.optionContract, this.tokenAbi, this.provider);
      const currentTimestamp = await contract.getCurrentTimestamp();
      const expiryTimestamp = currentTimestamp + BigInt(100);

      // 转换strike为wei
      const strikePriceInWei = ethers.parseEther(strikePrice.toString());

      // 创建交易
      const wallet = new ethers.Wallet(this.privateKey, this.provider);
      const contractWithSigner = new ethers.Contract(this.optionContract, this.tokenAbi, wallet);

      const tx = await contractWithSigner.openPosition(
        this.underlyingToken,      // underlying token address
        totalAmountInWei,         // amount in wei
        strikePriceInWei,        // strike price in wei
        expiryTimestamp,         // expiry timestamp
        positionTypeId,          // position type (0-3)
        totalAmountInWei,        // premium in wei
        BigInt(0)                // wallet type
      );
      logger.info(`钱包 [${this.walletIndex}] ${this.address} 准备开仓:
        期权类型: ${optionType} ${optionSide}
        开仓数量: ${optionQuantity}
        执行价格: ${strikePrice}
        期权费用: ${optionPrice}
        总花费(stMON): ${totalAmount}
      `);

      logger.info(`钱包 [${this.walletIndex}] ${this.address} 开仓交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();
      logger.success(`钱包 [${this.walletIndex}] ${this.address} 开仓成功！`);

      return receipt;
    } catch (error) {
      logger.error(`钱包 [${this.walletIndex}] ${this.address} 开仓失败: ${error.message}`);
      return false;
    }
  }

  async claim() {
    // todo
  }

  async execute() {
    await this.init();
    await this.faucet();
    await this.openPosition();
    await this.claim();
    return true;
  }


  async getOptionsChain(price) {
    function s(f, h, p) {
      const m = (Math.log(f / h) + (0.05 + Math.pow(0.3, 2) / 2) * 0.1) / (0.3 * Math.sqrt(0.1)),
        g = m - 0.3 * Math.sqrt(0.1),
        y = v => {
          const b = 1 / (1 + 0.2316419 * Math.abs(v)),
            T = 0.3989423 * Math.exp(-v * v / 2) * b * (0.3193815 + b * (-0.3565638 + b * (1.781478 + b * (-1.821256 + b * 1.330274))));
          return v > 0 ? 1 - T : T;
        };
      return p ? f * y(m) - h * Math.exp(-0.05 * 0.1) * y(g) : h * Math.exp(-0.05 * 0.1) * y(-g) - f * y(-m);
    }

    const o = [],
      a = 10,
      c = Number(price) * 0.05;
    for (let f = -a; f <= a; f++) {
      const h = Number(price) + f * c;
      o.push(Number(h.toFixed(2)));
    }
    const l = o.filter((f, h) => h >= a - 3 && h <= a + 3),
      u = l.map(f => {
        const h = s(price, f, true),
          p = h * 0.05;
        return {
          strike: f,
          bid: Number((h - p / 2).toFixed(4)),
          ask: Number((h + p / 2).toFixed(4)),
          spread: Number(p.toFixed(4))
        };
      }),
      d = l.map(f => {
        const h = s(price, f, false),
          p = h * 0.05;
        return {
          strike: f,
          bid: Number((h - p / 2).toFixed(4)),
          ask: Number((h + p / 2).toFixed(4)),
          spread: Number(p.toFixed(4))
        };
      });
    return [
      {
        "type": "CALLS",
        "side": "SELL",
        "price": u
      }, {
        "type": "PUTS",
        "side": "BUY",
        "price": d
      }
    ];
  }
}

export default Caddy;



