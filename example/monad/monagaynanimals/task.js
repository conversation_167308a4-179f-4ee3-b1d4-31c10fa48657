import { resolveFromModule } from "../../../base/tools/path.js";
import {
    parseTOMLConfig,
    filterWalletsByIndex,
    getRandomValue,
    sleep,
} from "../../../base/tools/common.js";
import MonagayAnimals from "./monagaynanimals.js";
import CSV from "../../../base/tools/csv.js";
import logger from "../../../base/tools/logger.js";

class MonagayAnimalsTask {
    constructor(rpc) {
        this.monagayAnimals = new MonagayAnimals(rpc);
        this.MIN_GAME_DELAY = 15;
        this.MAX_GAME_DELAY = 30;
    }

    async handleWallet(wallet) {
        const { private_key, index, address } = wallet;

        try {
            const games = getRandomValue(5, 7);
            logger.info(`钱包 ${index} 开始游戏, 计划进行 ${games} 局`);

            for (let i = 0; i < games; i++) {
                logger.info(`钱包 ${index} 开始第 ${i + 1}/${games} 局游戏`);
                const kills = getRandomValue(1, 10);
                await this.monagayAnimals.submitKills(wallet, kills);
                await sleep(getRandomValue(2, 5));
                await this.monagayAnimals.submitDeaths(wallet, 1);
                logger.info(`钱包 ${index} 完成第 ${i + 1} 局游戏 [击杀: ${kills}, 死亡: 1]`);

                if (i < games - 1) {
                    await sleep(getRandomValue(this.MIN_GAME_DELAY, this.MAX_GAME_DELAY));
                }
            }

            logger.success(`钱包 ${index} 完成 MonagayAnimals 任务`);
            return true;
        } catch (error) {
            logger.error('MonagayAnimals 任务执行失败', { '错误': error.message });
            return false;
        }
    }

    async start(indexArg) {
        logger.info("开始MonagayAnimals游戏任务...");

        try {
            const walletList = await CSV.read(getCSVFilePath());
            const filteredWallets = filterWalletsByIndex(indexArg, walletList);
            const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
            const totalWallets = shuffledWallets.length;

            logger.info(`成功加载 ${totalWallets} 个钱包`);

            let successCount = 0;
            let failedCount = 0;
            let failedIndexes = [];

            for (const [currentIndex, wallet] of shuffledWallets.entries()) {
                const { index, address } = wallet;

                if (!wallet.private_key?.trim()) {
                    logger.warn(`钱包 ${index} 的私钥为空，跳过处理`);
                    failedIndexes.push(index);
                    failedCount++;
                    continue;
                }

                logger.info(`开始处理第 ${currentIndex + 1}/${totalWallets} 个钱包，编号: ${index}，地址: ${address}`);

                const result = await this.handleWallet(wallet);

                if (result) {
                    successCount++;
                } else {
                    failedCount++;
                    failedIndexes.push(index);
                }

                if (currentIndex < totalWallets - 1) {
                    const delaySeconds = getRandomValue(10, 20);
                    logger.info(`等待 ${delaySeconds} 秒后继续下一个钱包...`);
                    await sleep(delaySeconds);
                }
            }

            logger.info("------------------------");
            logger.info(`总计处理钱包: ${totalWallets}`);
            logger.info(`成功: ${successCount}`);
            logger.info(`失败: ${failedCount}`);
            if (failedIndexes.length > 0) {
                logger.info(`失败钱包编号: ${failedIndexes.join(", ")}`);
            }

        } catch (error) {
            logger.error("任务执行出错:", error);
        }
    }
}

function getCSVFilePath() {
    return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

async function main() {
    const args = process.argv.slice(2);
    const indexArg = args[0];

    const configPath = resolveFromModule(import.meta.url, "../config.toml");
    const config = parseTOMLConfig(configPath);
    const monadConfig = config.monad;
    const rpc = monadConfig.rpc;

    const task = new MonagayAnimalsTask(rpc);

    if (!indexArg) {
        const walletList = await CSV.read(getCSVFilePath());
        if (walletList.length > 0) {
            logger.info("测试模式：使用第一个钱包");
            await task.handleWallet(walletList[0]);
        }
    } else {
        await task.start(indexArg);
    }
}

main().catch(error => {
    logger.error("未处理的错误:", error);
    process.exit(1);
});

export default MonagayAnimalsTask;
