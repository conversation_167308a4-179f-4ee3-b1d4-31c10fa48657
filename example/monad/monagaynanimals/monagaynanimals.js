import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";

class MonagayAnimals {
    constructor(rpc) {
        this.rpc = rpc;
        this.contractAddress = "******************************************";
        this.provider = new ethers.JsonRpcProvider(this.rpc);
    }

    /**
     * 获取GAS参数
     */
    async getGasParams() {
        try {
            const latestBlock = await this.provider.getBlock("latest");
            const baseFee = latestBlock.baseFeePerGas;
            const maxPriorityFee = await this.provider.send("eth_maxPriorityFeePerGas", []);
            const maxPriorityFeePerGas = BigInt(maxPriorityFee);
            const maxFeePerGas = baseFee + maxPriorityFeePerGas;

            return {
                maxFeePerGas,
                maxPriorityFeePerGas,
                gasLimit: 100000
            };
        } catch (error) {
            logger.error(`获取 gas 参数失败: ${error.message}`);
            return {
                maxFeePerGas: ethers.parseUnits("0.1", "gwei"),
                maxPriorityFeePerGas: ethers.parseUnits("0.1", "gwei"),
                gasLimit: 100000
            };
        }
    }

    /**
     * 提交游戏分数
     * @param {string} score 分数字符串
     */
    async setScore(wallet, score) {
        try {
            const contract = await this.getContract(wallet);
            const gasSettings = await this.getGasParams();

            logger.info(`准备提交分数: ${score}，使用的gas设置:`, gasSettings);

            const tx = await contract.setScore(score, { ...gasSettings });

            logger.info(`钱包 ${wallet.index} 提交分数: ${score}, 交易: ${tx.hash}`);
            const receipt = await tx.wait();

            logger.info(`交易确认完成: ${tx.hash}, 状态: ${receipt.status}`);
            return receipt.status === 1;
        } catch (error) {
            logger.error(`提交分数失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取当前分数
     */
    async getScore(wallet) {
        try {
            const contract = await this.getContract(wallet);
            const score = await contract.score();
            return score;
        } catch (error) {
            logger.error(`获取分数失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 提交击杀数据
     * @param {number} kills 击杀数量
     */
    async submitKills(wallet, kills) {
        return await this.setScore(wallet, kills.toString());
    }

    /**
     * 提交死亡数据
     * @param {number} deaths 死亡次数
     */
    async submitDeaths(wallet, deaths) {
        return await this.setScore(wallet, deaths.toString());
    }

    /**
     * 获取合约实例
     */
    async getContract(wallet) {
        const signer = new ethers.Wallet(wallet.private_key, this.provider);
        return new ethers.Contract(this.contractAddress, MonagayAnimalsABI, signer);
    }

    /**
     * 测试方法
     */
    static async test() {
        const task = new MonagayAnimals()
        const wallet = walletManager.getWallet(0)

        // 测试提交击杀数据
        await task.submitKills(wallet, 2)

        // 测试提交死亡数据
        await task.submitDeaths(wallet, 1)

        // 获取当前分数
        const score = await task.getScore(wallet)
        logger.info(`当前分数: ${score}`)
    }

    /**
     * 执行一局游戏
     */
    async playOneGame(wallet) {
        try {
            const kills = Math.floor(Math.random() * 10) + 1;
            await this.submitKills(wallet, kills);
            await this.submitDeaths(wallet, 1);
            return true;
        } catch (error) {
            logger.error(`游戏执行失败: ${error.message}`);
            return false;
        }
    }
}

// 合约 ABI
const MonagayAnimalsABI = [
    {
        inputs: [{
            internalType: "string",
            name: "_score",
            type: "string"
        }],
        name: "setScore",
        outputs: [],
        stateMutability: "nonpayable",
        type: "function"
    },
    {
        inputs: [],
        name: "score",
        outputs: [{
            internalType: "string",
            name: "",
            type: "string"
        }],
        stateMutability: "view",
        type: "function"
    }
]

export default MonagayAnimals;
