import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";

// https://conft.app/（Monad Testnet mint nft）
class CommunityMember{
  constructor(rpc) {
    this.rpc = rpc;
    this.contractAddress = "******************************************";
    this.abi = CommunityMember_ABI;
    this.mintData = "0x1249c58b"; // mint函数的input数据
    this.mintPrice = ethers.parseEther("0.01");
  }

  async getBalance(address) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const contract = new ethers.Contract(this.contractAddress, this.abi, provider);
    return await contract.balanceOf(address);
  }

  async mint(privateKey, walletIndex) {

    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(privateKey, provider);
    const contract = new ethers.Contract(this.contractAddress, this.abi, wallet);

    const balance = await this.getBalance(wallet.address);
    if (balance > 0) {
        logger.info(`钱包 ${walletIndex} 已经mint过，跳过处理`);
        return {
            success: false,
            code: 'ALREADY_MINTED',
            message: `钱包 ${walletIndex} 已经mint过，跳过处理`,
            data: { balance: balance.toString() }
        };
    }

    try {
        // 使用通用交易方法发送mint交易
        const txHash = await buildAndSendTransaction(
          this.rpc,
          privateKey,
          this.contractAddress,
          this.mintData,
          this.mintPrice.toString(),
        );

        logger.info(`钱包 ${walletIndex} mint已发送，等待确认`);
        const receipt = await txHash.wait();

        logger.success(`钱包 ${walletIndex} mint成功，交易哈希: ${receipt.hash}`);
        return {
            success: true,
            code: 'SUCCESS',
            message: `钱包 ${walletIndex} mint成功`,
            data: {
                txHash: receipt.hash,
                blockNumber: receipt.blockNumber,
                gasUsed: receipt.gasUsed.toString()
            }
        };
    } catch (error) {
        // 处理特定的错误类型
        if (error.message.includes("insufficient funds")) {
            return {
                success: false,
                code: 'INSUFFICIENT_FUNDS',
                message: "余额不足，无法支付gas费",
                data: { error: error.message }
            };
        } else if (error.message.includes("nonce")) {
            return {
                success: false,
                code: 'NONCE_ERROR',
                message: "Nonce错误，请等待之前的交易确认",
                data: { error: error.message }
            };
        } else if (error.message.includes("already minted")) {
            return {
                success: false,
                code: 'ALREADY_MINTED',
                message: "已经mint过了",
                data: { error: error.message }
            };
        }

        // 处理其他未知错误
        return {
            success: false,
            code: 'UNKNOWN_ERROR',
            message: `Mint失败: ${error.message}`,
            data: { error: error.message }
        };
    }
  }
}

// 只保留 balanceOf 函数的 ABI
const CommunityMember_ABI = [
    {
        "inputs": [
            {
                "internalType": "address",
                "name": "owner",
                "type": "address"
            }
        ],
        "name": "balanceOf",
        "outputs": [
            {
                "internalType": "uint256",
                "name": "balance",
                "type": "uint256"
            }
        ],
        "stateMutability": "view",
        "type": "function"
    }
];

export { CommunityMember, CommunityMember_ABI };
