import { ethers, parseEther, formatEther } from "ethers";
import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";
import fakeUa from "fake-useragent";

import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";
import { getRandomDomain } from "../../../base/tools/domain.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { parseTOMLConfig } from "../../../base/tools/common.js";
import { getHeaders } from "../../../base/tools/fake_useragent.js";
import { faker } from "@faker-js/faker";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const defaultProxy = config.PROXY_URL || "";


class Accountable {
  constructor(rpc, proxy = "") {
    this.rpc = rpc;
    this.abi = Accountable_ABI;
    this.name = "Monanimal";
    this.proxy = proxy || defaultProxy;
    this.contractAddress = "******************************************";
  }

  generateInputData(tokenId, signature) {
    // 函数选择器 "db7fd408"
    const functionSelector = "0xdb7fd408";

    // 将tokenId转换为64位的16进制，并补0
    const tokenIdHex = tokenId.toString(16).padStart(64, '0');

    // 固定值40，表示第二个参数的偏移量，需要补齐64位
    const offsetHex = "40".padStart(64, '0');

    // 签名长度41，需要补齐64位
    const sigLengthHex = "41".padStart(64, '0');

    // 签名本身需要补齐到64位的倍数（去掉0x前缀）
    const signatureWithoutPrefix = signature.slice(2);
    // 计算需要补齐多少个0，确保总长度是64的倍数
    const paddingLength = 64 - (signatureWithoutPrefix.length % 64);
    const signatureHex = signatureWithoutPrefix + "0".repeat(paddingLength);

    // 组合所有部分
    const inputData = functionSelector +
                     tokenIdHex +
                     offsetHex +
                     sigLengthHex +
                     signatureHex;

    return inputData;
  }

  async getBalance(address, tokenId) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const contract = new ethers.Contract(this.contractAddress, this.abi, provider);
    return await contract.balanceOf(address, tokenId);
  }

  async register(address, username) {
    const jsonData = {
        username: username.trim(),
        wallet_address: address.toLowerCase(),
      };

      // 设置请求配置
      const axiosConfig = {
        headers: getHeaders("https://game.accountable.capital"),
        timeout: 15000, // 15秒超时
        httpsAgent: this.proxy ? new HttpsProxyAgent(this.proxy) : null,
      };

      const response = await axios.post(
        "https://game.accountable.capital/api/register_player",
        jsonData,
        axiosConfig
      );

      // 返回结果
      //
        //  {
        //     "message": "Player registered successfully",
        //     "player": {
        //         "chat_id": "8080495014964969360",
        //         "created_at": "2025-03-10T09:24:01.376068",
        //         "is2fa": false,
        //         "updated_at": "2025-03-10T09:24:01.501001",
        //         "username": "deanverge",
        //         "wallet_address": "******************************************",
        //         "was_updated": false
        //     },
        //     "success": true
        // }
      return response.data;
  }

  async getCreateSignature(address, username) {
    const jsonData = {
        username: username.trim(),
        wallet_address: address.toLowerCase(),
      };

      // 设置请求配置
      const axiosConfig = {
        headers: getHeaders("https://game.accountable.capital"),
        timeout: 15000, // 15秒超时
        httpsAgent: this.proxy ? new HttpsProxyAgent(this.proxy) : null,
      };

      const response = await axios.post(
        "https://game.accountable.capital/api/generate-signature-create-player",
        jsonData,
        axiosConfig
      );
  }

  async getMintNFTSignature(address, tokenId) {
    const jsonData = {
      userAddress: address.toLowerCase(),
      tokenId: tokenId,
    };

    // 设置请求配置
    const axiosConfig = {
      headers: getHeaders("https://game.accountable.capital"),
      timeout: 15000, // 15秒超时
      httpsAgent: this.proxy ? new HttpsProxyAgent(this.proxy) : null,
    };

    let retryCount = 0;
    const maxRetries = 3;
    const minDelaySeconds = config.MIN_DELAY_SECONDS || 5;
    const maxDelaySeconds = config.MAX_DELAY_SECONDS || 15;

    while (retryCount < maxRetries) {
      try {
        const response = await axios.post(
          "https://game.accountable.capital/api/generate-signature-mint",
          jsonData,
          axiosConfig
        );

        if (response.status !== 200) {
          throw new Error(`tokneId:${tokenId} address:${address} 获取签名失败. Status code: ${response.status}`);
        }

        const data = response.data;
        const nonce = data.nonce || 0;
        logger.success(`Successfully got mint signature for token #${tokenId} (minted: ${nonce})`);
        return data.signature;

      } catch (error) {
        retryCount++;
        logger.error(`Attempt ${retryCount}/${maxRetries} failed: ${error.message}`);

        if (retryCount === maxRetries) {
          logger.error(`Failed to get signature after ${maxRetries} attempts`);
          return null;
        }

        // 随机延迟后重试
        const waitTime = Math.floor(Math.random() * (maxDelaySeconds - minDelaySeconds + 1)) + minDelaySeconds;
        logger.info(`Waiting ${waitTime} seconds before retry...`);
        await sleep(waitTime);
      }
    }
  }
}

const Accountable_ABI = [
    {
        "inputs": [
            {
                "internalType": "address",
                "name": "account",
                "type": "address"
            },
            {
                "internalType": "uint256",
                "name": "id",
                "type": "uint256"
            }
        ],
        "name": "balanceOf",
        "outputs": [
            {
                "internalType": "uint256",
                "name": "",
                "type": "uint256"
            }
        ],
        "stateMutability": "view",
        "type": "function"
    }
  ];

  export  {Accountable, Accountable_ABI};

