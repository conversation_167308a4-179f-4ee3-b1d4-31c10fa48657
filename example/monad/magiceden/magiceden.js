import { ethers, parseEther, formatEther } from "ethers";
import axios from "axios";

import { HttpsProxyAgent } from "https-proxy-agent";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";
import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";
import { getHeaders } from "../../../base/tools/fake_useragent.js";
import CSV from "../../../base/tools/csv.js";
import { retry, randomSleep, sleep, getRandomValue } from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { parseStructTag } from "@mysten/sui.js/utils";

class MagicEden {
    constructor(privateKey, walletIndex, rpc, proxy) {
        this.privateKey = privateKey;
        this.walletIndex = walletIndex;
        this.rpc = rpc;
        this.proxy = proxy;
        this.wallet = new Wallet(privateKey, rpc);
        this.address = this.wallet.getAddress();
        this.projectEnvironmentId = "c1314b5b-ece8-4b4f-a879-3894dda364e4"
        this.contractAddress = "******************************************";
    }

    async getBalance(address) {
        const provider = new ethers.JsonRpcProvider(this.rpc);
        const contract = new ethers.Contract(this.contractAddress, this.abi, provider);
        return await contract.balanceOf(address, this.tokenId);
    }

    #createRequestConfig(additionalHeaders = {}) {
        let headers = getHeaders("https://magiceden.io", this.walletIndex);
        if (this.token) {
            headers["authorization"] = `Bearer ${this.token}`;
        }
        // 合并额外的 headers
        headers = { ...headers, ...additionalHeaders };

        let proxyAgent = null;
        if (this.proxy) {
            proxyAgent = new HttpsProxyAgent(this.proxy);
        }

        const config = {
            headers: headers,
            timeout: 30000,
        };

        if (proxyAgent) {
            config.httpsAgent = proxyAgent;
        }
        return config;
    }

    async #_request(method, url, additionalHeaders = {}, data = null) {
        const config = this.#createRequestConfig(additionalHeaders);
        try {
            const response = await axios({
                method,
                url,
                ...config,
                ...(data ? { data: JSON.stringify(data) } : {})
            });
            return response;
        } catch (error) {
            // 处理 HTTP 错误
            if (error.response) {
                // 服务器返回了错误状态码
                const errMsg = `请求失败: HTTP ${error.response.status} - ${error.response.data?.message || error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            } else if (error.request) {
                // 请求已发送但没有收到响应
                const errMsg = `请求超时或无响应: ${error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            } else {
                // 请求配置出错
                const errMsg = `请求配置错误: ${error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            }
        }
    }

    async login() {
        try {
            // 构造签名消息
            const messageData = {
                domain: 'magiceden.io',
                address: this.address,
                statement: 'Welcome to Magic Eden. Signing is the only way we can truly know that you are the owner of the wallet you are connecting. Signing is a safe, gas-less transaction that does not in any way give Magic Eden permission to perform any transactions with your wallet.',
                uri: 'https://magiceden.io/',
                version: '1',
                chainId: '10143',
                nonce: this.#generateNonce(), // 生成随机nonce
                issuedAt: new Date().toISOString(),
                requestId: this.projectEnvironmentId // 生成随机requestId
            };

            // 构造要签名的消息
            const message = `magiceden.io wants you to sign in with your Ethereum account:\n${messageData.address}\n\n${messageData.statement}\n\nURI: ${messageData.uri}\nVersion: ${messageData.version}\nChain ID: ${messageData.chainId}\nNonce: ${messageData.nonce}\nIssued At: ${messageData.issuedAt}\nRequest ID: ${messageData.requestId}`;

            // 使用钱包签名消息
            const signature = await this.wallet.signMessage(message);

            logger.info(`登录签名成功: ${signature}`);

            return {
                success: true,
                message: "签名成功",
                data: {
                    signature,
                    messageData
                }
            };
        } catch (error) {
            logger.error("登录签名失败:", error);
            return {
                success: false,
                message: `签名失败: ${error.message}`,
                error
            };
        }
    }

    async verifyLogin(signature, messageData) {

    }

    async getCollections() {
        try {
            const url = "https://stats-mainnet.magiceden.io/collection_stats/search/monad-testnet";
            const params = {
                offset: 0,
                window: "1d",
                limit: 10,
                sort: "floorPrice",
                direction: "asc",
                filter: JSON.stringify({
                    qc: {
                        isVerified: true,
                        minOwnerCount: 30,
                        minTxns: 5
                    }
                })
            };

            // 构建完整的URL，包含查询参数
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = `${url}?${queryString}`;

            // 添加特定的请求头
            const additionalHeaders = {
                "if-none-match": 'W/"25f7-cnTncmYwFFvfC7KPjaKBeZlXm9w"',
            };

            const response = await this.#_request('GET', fullUrl, additionalHeaders);

            if (response.status === 200) {
                logger.info(`成功获取集合列表，共 ${response.data.length} 个集合`);
                return {
                    success: true,
                    data: response.data
                };
            } else {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }
        } catch (error) {
            logger.error(`获取集合列表失败: ${error.message}`);
            return {
                success: false,
                message: error.message
            };
        }
    }

    async getCollection(collectionId) {
        try {
            const url = "https://api-mainnet.magiceden.io/v3/rtp/monad-testnet/tokens/v7";

            // 构建查询参数
            const params = {
                includeQuantity: true,
                includeLastSale: true,
                excludeSpam: true,
                excludeBurnt: true,
                collection: collectionId,
                sortBy: 'floorAskPrice',
                sortDirection: 'asc',
                limit: 50,
                includeAttributes: false
            };

            // 添加excludeSources参数
            params['excludeSources[]'] = ['nftx.io', 'sudoswap.xyz'];

            // 构建完整的URL，包含查询参数
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = `${url}?${queryString}`;

            // 添加特定的请求头
            const additionalHeaders = {
                "if-modified-since": new Date().toUTCString(),
                "priority": "u=1, i",
            };

            const response = await this.#_request('GET', fullUrl, additionalHeaders);

            if (response.status === 200) {
                logger.info(`成功获取集合 ${collectionId} 的NFT列表，共 ${response.data.tokens.length} 个NFT`);
                return {
                    success: true,
                    data: response.data.tokens[0].token
                };
            } else {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }
        } catch (error) {
            logger.error(`获取集合 ${collectionId} 的NFT列表失败: ${error.message}`);
            return {
                success: false,
                message: error.message
            };
        }
    }

    async getBuyData(tokenAddress, tokenId, is1155, quantity) {
        try {
            const url = "https://api-mainnet.magiceden.io/v3/rtp/monad-testnet/execute/buy/v7";

            const requestData = {
                items: [{
                    key: `${tokenAddress}:${tokenId}`,
                    token: `${tokenAddress}:${tokenId}`,
                    is1155: is1155,
                    source: "magiceden.io",
                    fillType: "trade",
                    quantity: quantity
                }],
                taker: this.address,
                source: "magiceden.io",
                partial: true,
                currency: "0x0000000000000000000000000000000000000000",
                currencyChainId: 10143,
                normalizeRoyalties: false
            };

            const additionalHeaders = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
                "x-rkc-version": "2.5.4"
            };

            const response = await this.#_request('POST', url, additionalHeaders, requestData);

            if (response.status === 200) {
                const txData = response.data.steps[0].items[0].data.data;
                const totalRawPrice = response.data.path[0].totalRawPrice;

                logger.info(`获取购买数据成功，总价: ${totalRawPrice}`);

                return {
                    success: true,
                    data: {
                        txData,
                        totalRawPrice
                    }
                };
            } else {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }
        } catch (error) {
            logger.error(`获取购买数据失败: ${error.message}`);
            return {
                success: false,
                message: error.message
            };
        }


    }

    async buyNFT() {
        const collections = await this.getCollections();
        if (!collections.success) {
            logger.error(`获取集合列表失败: ${collections.message}`);
            return;
        }
        const randomIndex = Math.floor(Math.random() * collections.data.length);
        const selectedCollection = collections.data[randomIndex];
        logger.info(`钱包 [${this.walletIndex}] ${this.address} 选择: ${selectedCollection.name}(${selectedCollection.collectionId}) 进行购买`);

        const collection = await this.getCollection(selectedCollection.collectionId);
        if (!collection.success) {
            logger.error(`获取集合 ${selectedCollection.collectionId} 失败: ${collection.message}`);
            return;
        }
        const tokenAddress = collection.data.contract;
        const tokenId = collection.data.tokenId;
        const name = collection.data.name;
        logger.info(`钱包 [${this.walletIndex}] ${this.address} 选择: ${name}(${tokenAddress}:${tokenId}) 进行购买`);
        const is1155 = collection.data.kind === "erc1155";
        const buyData = await this.getBuyData(tokenAddress, tokenId, is1155, 1);
        if (!buyData.success) {
            logger.error(`获取购买数据失败: ${buyData.message}`);
            return;
        }
        const { txData, totalRawPrice } = buyData.data;
        const txHash = await buildAndSendTransaction(
            this.rpc,
            this.privateKey,
            this.contractAddress,
            txData,
            parseEther(totalRawPrice).toString()
        );
        const receipt = await txHash.wait();
        logger.success(`钱包 [${this.walletIndex}] ${this.address} 购买 ${name}(${tokenAddress}:${tokenId}) 成功，交易哈希: ${receipt.hash}`);
        return true;
    }

    // 生成随机nonce
    #generateNonce() {
        return Array.from(crypto.getRandomValues(new Uint8Array(16)))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    }

    // 生成随机requestId
    #generateRequestId() {
        const uuid = crypto.randomUUID();
        return uuid;
    }


}

export default MagicEden;
