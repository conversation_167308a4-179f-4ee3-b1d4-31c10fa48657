import { ethers } from "ethers";
import axios from "axios";
import crypto from 'crypto';

import { HttpsProxyAgent } from "https-proxy-agent";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";
import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";
import { getHeaders } from "../../../base/tools/fake_useragent.js";
import { retry, sleep, getHumanRandomInt } from "../../../base/tools/common.js";

const EIP712_DOMAIN = [
    { name: "name", type: "string" },
    { name: "version", type: "string" },
    { name: "chainId", type: "uint256" },
    { name: "verifyingContract", type: "address" },
];

const ORDER_STRUCTURE = [
    { name: "salt", type: "uint256" },
    { name: "maker", type: "address" },
    { name: "signer", type: "address" },
    { name: "taker", type: "address" },
    { name: "tokenId", type: "uint256" },
    { name: "makerAmount", type: "uint256" },
    { name: "takerAmount", type: "uint256" },
    { name: "expiration", type: "uint256" },
    { name: "nonce", type: "uint256" },
    { name: "feeRateBps", type: "uint256" },
    { name: "side", type: "uint8" },
    { name: "signatureType", type: "uint8" },
];

// 模拟 w 模块的常量
const PROTOCOL_VERSION = "1"; // 协议版本
const ZX = "0x"; // 哈希前缀

// 模拟 m.SignatureType
const SignatureType = {
    EOA: 0,
    POLY_GNOSIS_SAFE: 2,
};

class OrderBuilder {
    constructor(contractAddress, chainId, wallet, generateSalt = () => Math.round(Math.random() * Date.now()) + "") {
        this.contractAddress = contractAddress;
        this.chainId = chainId;
        this.wallet = wallet;
        this.generateSalt = generateSalt;
    }

    encodeSignatures(signatures) {
        signatures.sort((a, b) => a.signer.toLowerCase().localeCompare(b.signer.toLowerCase()));
        let t = "0x";
        let n = "";
        for (let sig of signatures) {
            if (sig.dynamic) {
                const offset = (65 * signatures.length + n.length / 2).toString(16).padStart(64, "0");
                const length = (sig.data.slice(2).length / 2).toString(16).padStart(64, "0");
                const header = `${sig.signer.slice(2).padStart(64, "0")}${offset}00`;
                const data = `${length}${sig.data.slice(2)}`;
                t += header;
                n += data;
            } else {
                t += sig.data.slice(2);
            }
        }
        return t + n;
    }

    async buildOrder(params) {
        const {
            maker,
            taker,
            tokenId,
            makerAmount,
            takerAmount,
            side,
            feeRateBps,
            nonce = "0",
            signer,
            expiration,
            signatureType = SignatureType.POLY_GNOSIS_SAFE,
        } = params;

        return {
            salt: this.generateSalt(),
            maker,
            signer: signer,
            taker,
            tokenId,
            makerAmount,
            takerAmount,
            expiration: expiration,
            nonce,
            feeRateBps,
            side,
            signatureType: signatureType,
        };
    }

    buildOrderTypedData(order) {
        return {
            primaryType: "Order",
            types: {
                EIP712Domain: EIP712_DOMAIN,
                Order: ORDER_STRUCTURE,
            },
            domain: {
                name: "OLAB CTF Exchange",
                version: PROTOCOL_VERSION,
                chainId: this.chainId,
                verifyingContract: this.contractAddress,
            },
            message: { ...order },
        };
    }

    async buildOrderSignature(typedData) {
        try {
            // 删除 types 中的 EIP712Domain（签名时不需要）
            delete typedData.types.EIP712Domain;
            // 使用 ethers.js 签名 EIP-712 数据
            return await this.wallet.signTypedData(typedData.domain, typedData.types, typedData.message);
        } catch (error) {
            logger.error(`签名失败: ${error.message}`);
            throw new Error(`签名失败: ${error.message}`);
        }
    }

    async buildSignedOrder(params) {
        const order = await this.buildOrder(params);
        const typedData = this.buildOrderTypedData(order);
        const signature = await this.buildOrderSignature(typedData);
        // 编码为 Gnosis Safe 签名
        const encodedSignature = this.encodeSignatures([{ signer: order.signer, data: signature }]);
        return { ...order, signature: encodedSignature };
    }
}


class OpinionLabs {
    constructor(privateKey, walletIndex, rpc, proxy) {
        this.proxy = proxy;
        this.rpc = rpc;
        this.wallet = new Wallet(privateKey, rpc);
        this.address = this.wallet.getAddress();
        this.walletIndex = walletIndex;
        this.privateKey = privateKey;
        this.token = null;
        this.exchangeAddress = "******************************************";
        this.provider = new ethers.JsonRpcProvider(this.rpc, {
            chainId: 10143,
            name: 'monad'
        });
        this.chainId = 10143;
    }

    #axiosConfig(additionalHeaders = {}) {
        let headers = getHeaders("https://app.olab.xyz", this.walletIndex);
        if (this.token) {
            headers["authorization"] = `Bearer ${this.token}`;
        }
        if (!headers["x-device-fingerprint"]) {
            headers["x-device-fingerprint"] = this.generateDeviceFingerprint();
        }
        headers["x-device-kind"] = "web";
        headers["x-aws-waf-token"] = "";

        // 合并额外的 headers
        headers = { ...headers, ...additionalHeaders };

        let proxyAgent = null;
        if (this.proxy) {
            proxyAgent = new HttpsProxyAgent(this.proxy);
        }

        const config = {
            headers: headers,
            timeout: 30000,
        };

        if (proxyAgent) {
            config.httpsAgent = proxyAgent;
        }
        return config;
    }

    async #request(method, url, additionalHeaders = {}, params = null) {
        const config = this.#axiosConfig(additionalHeaders);
        try {
            const response = await axios({
                method,
                url,
                ...config,
                ...(params ? { data: JSON.stringify(params) } : {})
            });
            return response;
        } catch (error) {
            // 处理 HTTP 错误
            if (error.response) {
                // 服务器返回了错误状态码
                const errMsg = `请求失败: HTTP ${error.response.status} - ${error.response.data?.message || error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            } else if (error.request) {
                // 请求已发送但没有收到响应
                const errMsg = `请求超时或无响应: ${error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            } else {
                // 请求配置出错
                const errMsg = `请求配置错误: ${error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            }
        }
    }

    generateDeviceFingerprint() {
        return crypto.randomBytes(16).toString('hex');
    }

    getNonceAndTimestamp() {
        const min = 65535;
        const max = 0xffffffffffff; // 十进制为 281474976710655
        const nonce = Math.floor(Math.random() * (max - min + 1)) + min;

        const timestamp = Math.round(Date.now() / 1000);
        return { nonce, timestamp };
    }


    async signMessage() {
        const { nonce, timestamp } = this.getNonceAndTimestamp();
        const message = JSON.stringify({
            nonce: nonce.toString(),
            timestamp: timestamp,
        });
        const signature = await this.wallet.signMessage(message);
        return { nonce, timestamp, signature };
    }

    async getAccessToken() {
        const { nonce, timestamp, signature } = await this.signMessage();
        const params = {
            "invite_code": "",
            "nonce": nonce.toString(),
            "sign": signature.slice(2),
            "sign_in_wallet_plugin": "com.okex.wallet",
            "sources": "web",
            "timestamp": timestamp
        }
        const response = await this.#request("POST", "https://api-base.olab.xyz/api/v1/user/token", {}, params);
        if (response.data.errno === 0 && response.data.result) {
            this.token = response.data.result.token;
            this.expire = response.data.result.expire;
            return {
                token: response.data.result.token,
                address: response.data.result.address,
                expire: response.data.result.expire,
            };
        } else {
            logger.error(`获取 token 失败: ${response.data.errmsg}`);
            throw new Error(`获取 token 失败: ${response.data.errmsg}`);
        }
    }

    async getUserInfo() {
        const url = `https://api-base.olab.xyz/api/v2/user/${this.address}/profile`;
        const response = await this.#request("GET", url, {}, {});
        if (response.data.errno === 0 && response.data.result) {
            return response.data.result;
        } else {
            logger.error(`获取用户信息失败: ${response.data.errmsg}`);
            throw new Error(`获取用户信息失败: ${response.data.errmsg}`);
        }
    }

    async checkIsNewUser() {
        const url = `https://api-base.olab.xyz/api/v1/user/is/new/user?wallet_address=${this.address}`;
        const response = await this.#request("GET", url, {}, null);
        if (response.data.errno === 0 && response.data.result) {
            return response.data.result.result;
        } else {
            logger.error(`检查新用户状态失败: ${response.data.errmsg}`);
            throw new Error(`检查新用户状态失败: ${response.data.errmsg}`);
        }
    }

    async getMultiSignedWalletAddress() {
        const url = `https://api-base.olab.xyz/api/v2/user/${this.address}/multiSignedWalletAddress`;
        const response = await this.#request("GET", url, {}, null);
        if (response.data.errno === 0 && response.data.result) {
            const multiSignedWalletAddress = response.data.result.multiSignedWalletAddress?.[this.chainId] || "";
            this.maker = multiSignedWalletAddress;
            return {
                success: true,
                code: "SUCCESS",
                message: "获取多签钱包地址成功",
                data: response.data.result.multiSignedWalletAddress
            };
        } else {
            logger.error(`获取多签钱包地址失败: ${response.data.errmsg}`);
            return {
                success: false,
                code: "ERROR",
                message: response.data.errmsg || "Unknown error",
                data: null
            };
        }
    }

    async createMultiSignedWallet() {
        try {
            // 发送创建多签钱包的请求
            const url = `https://api-monad.olab.xyz/api/v2/gnosis_safe/${this.address}`;
            const params = { chainId: this.chainId };

            const response = await this.#request("POST", url, {}, params);
            if (!response.data || response.data.errno !== 0 || !response.data.result) {
                logger.error(`创建多签钱包失败: ${response.data?.errmsg || '未知错误'}`);
                return {
                    success: false,
                    code: "ERROR",
                    message: response.data?.errmsg || "创建多签钱包失败",
                    data: null
                };
            }

            const { calldata, gnosis_safe_proxy_factory, success, is_created } = response.data.result;
            if (!success) {
                return {
                    success: false,
                    code: "ERROR",
                    message: "多签钱包创建失败, success 为 false",
                    data: null
                };
            }


            const tx = await retry(
                async () => {
                    try {
                        const result = await buildAndSendTransaction(
                            this.rpc,
                            this.privateKey,
                            gnosis_safe_proxy_factory,
                            calldata
                        );

                        if (!result || !result.hash) {
                            throw new Error('发送交易失败: 返回数据格式错误');
                        }

                        return result;
                    } catch (error) {
                        // 处理特定的错误类型
                        if (error.message.includes('insufficient funds') || error.message.includes('insufficient balance')) {
                            logger.error(`钱包 [${this.walletIndex}] ${this.address} 余额不足，无法发送交易`);
                            return null;
                        } else if (error.message.includes('nonce too low')) {
                            logger.error(`钱包 [${this.walletIndex}] ${this.address} nonce 错误，请重试`);
                            throw new Error('nonce 错误，请重试');
                        } else if (error.message.includes('replacement fee too low')) {
                            throw new Error('gas 费用太低，无法替换交易');
                        }
                        // 其他错误直接抛出
                        throw error;
                    }
                }
            );

            // 如果交易发送失败，直接返回
            if (!tx) {
                return {
                    success: false,
                    code: "ERROR",
                    message: '创建多签钱包失败, 交易发送失败'
                };
            }

            logger.info(`钱包 [${this.walletIndex}] ${this.address} 交易已发送，等待确认: ${tx.hash}`);

            // 等待交易确认
            const receipt = await tx.wait();
            if (!receipt || typeof receipt.status === 'undefined') {
                return {
                    success: false,
                    code: "ERROR",
                    message: '创建多签钱包失败, 返回数据格式错误'
                };
            }

            if (receipt.status === 1) {
                logger.success(`钱包 [${this.walletIndex}] ${this.address} 交易确认成功: ${receipt.hash}`);
                return {
                    success: true,
                    code: "SUCCESS",
                    message: '创建多签钱包成功',
                    data: {
                        txHash: receipt.hash,
                        ...response.data.result
                    }
                };
            }

            return {
                success: false,
                code: "ERROR",
                message: '创建多签钱包失败'
            };
        } catch (error) {
            logger.error(`创建多签钱包异常: ${error.message}`);
            return {
                success: false,
                code: "ERROR",
                message: error.message,
            };
        }
    }

    async getUserProfile() {
        try {
            const url = `https://api-monad.olab.xyz/api/v2/user/${this.address}/profile?chainId=${this.chainId}`;
            const response = await this.#request("GET", url, {}, null);

            if (!response.data || response.data.errno !== 0 || !response.data.result) {
                logger.error(`获取用户信息失败: ${response.data?.errmsg || '未知错误'}`);
                return {
                    success: false,
                    code: "ERROR",
                    message: response.data?.errmsg || "获取用户信息失败",
                    data: null
                };
            }

            const profileData = response.data.result;

            // 检查多签钱包地址
            const multiSignedWalletAddress = profileData.multiSignedWalletAddress?.[this.chainId] || "";
            if (multiSignedWalletAddress) {
                this.maker = multiSignedWalletAddress;
            }

            return {
                success: true,
                code: "SUCCESS",
                message: "获取用户信息成功",
                data: {
                    userName: profileData.userName,
                    walletAddress: profileData.walletAddress,
                    multiSignedWalletAddress,  // 直接返回当前链的多签钱包地址
                    balance: profileData.balance,
                    volume: profileData.Volume,
                    volumeIncRate: profileData.VolumeIncRate,
                    netWorth: profileData.netWorth,
                    totalProfit: profileData.totalProfit,
                    profitIncRate: profileData.profitIncRate,
                    rankTheWeek: profileData.rankTheWeek,
                    score: profileData.score,
                    follower: profileData.follower,
                    following: profileData.following,
                    followed: profileData.followed,
                    avatarUrl: profileData.avatarUrl,
                    email: profileData.email,
                    introduction: profileData.introduction,
                    location: profileData.location
                }
            };
        } catch (error) {
            logger.error(`获取用户信息异常: ${error.message}`);
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            };
        }
    }

    async getTopic(params = {}) {
        const defaultParams = {
            labelId: '',
            keywords: '',
            sortBy: 1,
            chainId: '10143',
            page: 1,
            limit: 12,
            status: 2,
            isShow: 1,
            topicType: 2
        };

        // 合并默认参数和传入的参数
        const queryParams = { ...defaultParams, ...params };

        // 构建查询字符串
        const queryString = Object.entries(queryParams)
            .map(([key, value]) => `${key}=${value}`)
            .join('&');

        const url = `https://api-monad.olab.xyz/api/v2/topic?${queryString}`;
        const response = await this.#request("GET", url, {}, null);

        if (response.data.errno === 0 && response.data.result) {
            // 过滤指定 currencyAddress 的 topics
            const filteredTopics = response.data.result.list.filter(
                topic => topic.currencyAddress === '0x024dDBC9559b7d1D076A83dD03a3e44aa7eD916f'
            );

            if (filteredTopics.length === 0) {
                logger.error('没有找到符合条件的 topic');
                return {};
            }

            // 随机选择一个 topic
            const randomIndex = Math.floor(Math.random() * filteredTopics.length);
            return filteredTopics[randomIndex];
        } else {
            logger.error(`获取话题列表失败: ${response.data.errmsg}`);
            return {};
        }
    }

    async getTopicDetail(topicId) {
        const url = `https://api-base.olab.xyz/api/v2/topic/${topicId}`;
        const response = await this.#request("GET", url, {}, null);

        if (response.data.errno === 0 && response.data.result) {
            return response.data.result.data;
        } else {
            logger.error(`获取话题详情失败: ${response.data.errmsg}`);
            throw new Error(`获取话题详情失败: ${response.data.errmsg}`);
        }
    }

    async buildOrder(tokenId, maker, makerAmount, taker = "******************************************", takerAmount = "0", side = 0, expiration = "0", feeRateBps = "0") {
        const wallet = new ethers.Wallet(this.privateKey, this.provider);
        const orderBuilder = new OrderBuilder(this.exchangeAddress, this.chainId, wallet);
        const makerAmountInWei = ethers.parseUnits(makerAmount.toString(), 'ether').toString();
        const order = await orderBuilder.buildOrder({
            maker,
            signer: this.address,
            taker,
            tokenId,
            makerAmount: makerAmountInWei,
            takerAmount,
            side,
            feeRateBps,
            nonce: "0",
            expiration
        });

        // 生成签名订单
        const signedOrder = await orderBuilder.buildSignedOrder(order);

        // 计算订单哈希（可选，供验证使用）
        const typedData = orderBuilder.buildOrderTypedData(signedOrder);
        // const orderHash = orderBuilder.buildOrderHash(typedData);

        return {
            order: signedOrder,
            // orderHash,
        };
    } catch(error) {
        // 错误处理
        if (error.message.includes("Signer does not match")) {
            return { error: "Signer address does not match maker address" };
        }
        if (error.code === "NETWORK_ERROR" || error.message.includes("network")) {
            return { error: "Network error: Failed to connect to the blockchain" };
        }
        if (error.code === 4001 || error.message.includes("user rejected")) {
            return { error: "Transaction rejected by user" };
        }
    }

    async createOrder(topic) {
        try {
            const tokens = [topic.noPos, topic.yesPos]
            const tokenId = tokens[Math.floor(Math.random() * tokens.length)]
            const makerAmount = getHumanRandomInt(1, 10)
            const { order } = await this.buildOrder(tokenId, this.maker, makerAmount);
            const params = {
                topicId: topic.topicId,
                contractAddress: "",
                price: "0",
                tradingMethod: 1,
                ...order,
                signatureType: String(order.signatureType),
                takerAmount: order.takerAmount,
                makerAmount: order.makerAmount,
                feeRateBps: "0",
                tokenId: order.tokenId,
                side: String(order.side),
                timestamp: Math.round(new Date().getTime() / 1e3),
                sign: order.signature,
                safeRate: "0",
                orderExpTime: order.expiration,
                currencyAddress: topic.currencyAddress,
                expiration: "0",
                chainId: this.chainId
            }
            console.log(params);
            const url = `https://api-base.olab.xyz/api/v2/order`;
            const response = await this.#request("POST", url, {}, params);
            if (response.data.errno === 0) {
                logger.info(`创建订单成功: ${JSON.stringify(response.data.result)}`);
                return {
                    success: true,
                    code: "SUCCESS",
                    message: "创建订单成功",
                    data: response.data.result
                };
            } else {
                return {
                    success: false,
                    code: "ERROR",
                    message: response.data.errmsg || "Unknown error",
                    data: null
                };
            }
        } catch (error) {
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            };
        }
    }

    async execute() {
        // 1. 检查是否为新用户,
        const isNewUser = await this.checkIsNewUser();
        logger.info(`是否为新用户: ${isNewUser}`);

        // 2. 获取 token
        const token = await this.getAccessToken();
        logger.info(`获取 token 成功: ${JSON.stringify(token)}`);

        // 3. 如果是新用户则设置用户名

        // 4. 查询用户资料，判断multiSignedWalletAddress是否为空，如果为空则创建多签钱包
        const userProfile = await this.getUserProfile();
        logger.info(`获取用户资料成功: ${JSON.stringify(userProfile)}`);
        if (userProfile.success && !userProfile.data.multiSignedWalletAddress) {
            logger.info(`多签钱包地址为空，创建多签钱包`);
            const createResult = await this.createMultiSignedWallet();

            // 如果createResult.success为true，则循环调用this.getUserProfile()，直到返回的multiSignedWalletAddress不为空
            if (createResult.success) {
                let retryCount = 0;
                const maxRetries = 60; // 最多尝试60次，相当于等待1分钟
                while (retryCount < maxRetries) {
                    const userProfile = await this.getUserProfile();
                    logger.info(`获取用户资料成功: ${JSON.stringify(userProfile)}`);
                    if (userProfile.success && userProfile.data.multiSignedWalletAddress) {
                        break;
                    }
                    await sleep(1); // 每次等待1秒
                    retryCount++;
                }
                if (retryCount >= maxRetries) {
                    logger.error('等待多签钱包地址超时（1分钟）');
                    throw new Error('等待多签钱包地址超时');
                }
            }
        }
        // 5. 查询多签钱包地址
        const multiSignedWalletAddress = await this.getMultiSignedWalletAddress();
        if (!multiSignedWalletAddress.success || !multiSignedWalletAddress.data) {
            logger.error(`获取多签钱包地址失败: ${multiSignedWalletAddress.message}`);
            return;
        }
        logger.info(`获取多签钱包地址成功: ${JSON.stringify(multiSignedWalletAddress)}`);

        // 6. 查询话题列表
        const topic = await this.getTopic();
        logger.info(`获取话题列表成功: ${JSON.stringify(topic)}`);

        // 7. 创建订单
        const order = await this.createOrder(topic);
        logger.info(`创建订单: ${JSON.stringify(order)}`);

        // const order = await this.createOrder(topic);
        // logger.info(`创建订单成功: ${JSON.stringify(order)}`);
        // const topicDetail = await this.getTopicDetail(374);
        // logger.info(`获取话题详情成功: ${JSON.stringify(topicDetail)}`);
    }
}

export default OpinionLabs;
