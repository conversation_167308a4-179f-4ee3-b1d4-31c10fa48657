
import { ethers, formatEther, parseEther } from "ethers";
import { getHumanNumber } from "../../../base/tools/common.js";

import { Wallet } from "../../../base/evm/wallet.js";
import { parseTOMLConfig, retry } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";
import { resolveFromModule } from "../../../base/tools/path.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad || {};
const rpc = monadConfig.rpc || "https://testnet-rpc.monad.xyz/";

// https://shmonad.xyz/
export default class SHMonad {
  /**
   * 创建 SHMonad 实例
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param rpcUrl
   */
  constructor(privateKey, proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.logger = logger;

    this.CONTRACT_ADDRESS = "******************************************";

    this.contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      [
        {
          type: "function",
          name: "deposit",
          inputs: [
            { name: "assets", type: "uint256", internalType: "uint256" },
            { name: "receiver", type: "address", internalType: "address" },
          ],
          outputs: [{
            name: "",
            type: "uint256",
            internalType: "uint256"
          }],
          stateMutability: "payable",
        },
        {
          type: "function",
          name: "redeem",
          inputs: [{
            name: "shares",
            type: "uint256",
            internalType: "uint256"
          }, {
            name: "receiver",
            type: "address",
            internalType: "address"
          }, {
            name: "owner",
            type: "address",
            internalType: "address"
          }],
          outputs: [{
            name: "",
            type: "uint256",
            internalType: "uint256"
          }],
          stateMutability: "nonpayable"
        },
        {
          type: "function",
          name: "bond",
          inputs: [{
            name: "policyID",
            type: "uint64",
            internalType: "uint64"
          }, {
            name: "bondRecipient",
            type: "address",
            internalType: "address"
          }, {
            name: "amount",
            type: "uint256",
            internalType: "uint256"
          }],
          outputs: [],
          stateMutability: "nonpayable"
        },
        {
          type: "function",
          name: "unbond",
          inputs: [{
            name: "policyID",
            type: "uint64",
            internalType: "uint64"
          }, {
            name: "amount",
            type: "uint256",
            internalType: "uint256"
          }, {
            name: "newMinBalance",
            type: "uint256",
            internalType: "uint256"
          }],
          outputs: [{
            name: "unbondBlock",
            type: "uint256",
            internalType: "uint256"
          }],
          stateMutability: "nonpayable"
        },
        {
          type: "function",
          name: "getPolicy",
          inputs: [{
            name: "policyID",
            type: "uint64",
            internalType: "uint64"
          }],
          outputs: [{
            name: "",
            type: "tuple",
            internalType: "struct Policy",
            components: [{
              name: "escrowDuration",
              type: "uint48",
              internalType: "uint48"
            }, {
              name: "active",
              type: "bool",
              internalType: "bool"
            }]
          }],
          stateMutability: "view"
        },
        {
          type: "function",
          name: "claim",
          inputs: [{
            name: "policyID",
            type: "uint64",
            internalType: "uint64"
          }, {
            name: "amount",
            type: "uint256",
            internalType: "uint256"
          }],
          outputs: [],
          stateMutability: "nonpayable"
        }
      ],
      this.wallet.wallet,
    );

    this.query_contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      [
        {
          type: "function",
          name: "balanceOf",
          inputs: [{
            name: "account",
            type: "address",
            internalType: "address"
          }],
          outputs: [{
            name: "",
            type: "uint256",
            internalType: "uint256"
          }],
          stateMutability: "view"
        }, {
          type: "function",
          name: "balanceOfBonded",
          inputs: [{
            name: "account",
            type: "address",
            internalType: "address"
          }],
          outputs: [{
            name: "",
            type: "uint256",
            internalType: "uint256"
          }],
          stateMutability: "view"
        },
        {
          type: "function",
          name: "balanceOfUnbonding",
          inputs: [{
            name: "policyID",
            type: "uint64",
            internalType: "uint64"
          }, {
            name: "account",
            type: "address",
            internalType: "address"
          }],
          outputs: [{
            name: "",
            type: "uint256",
            internalType: "uint256"
          }],
          stateMutability: "view"
        }
      ],
      this.wallet.wallet,
    );
  }


  /**
   * 获取monad余额
   * @return {number}
   */
  async getBalance() {
    try {
      const balance = await retry(async () => await this.wallet.getBalance(), 3, 2000);
      return formatEther(balance);
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取质押金额
   * @param {number} maxAmount - 最大质押金额
   * @returns {number}
   */
  getStakeAmount(maxAmount) {
    return getHumanNumber(maxAmount);
  }


  /**
   * 质押存入
   * @param {boolean} skipIfStaked - 是否跳过已质押
   * @param {string} [amount] - 质押金额
   * @returns {Promise<{success: boolean, hash?: string, name?: string, message?: string, code?: string}>}
   */
  async stake(skipIfStaked = false, amount = 0) {
    let txHash = null;
    try {
      const address = this.wallet.getAddress();

      // 1. 检查是否已质押
      const stakedAmount = await retry(async () => await this.query_contract.balanceOf(address), 3, 2000);
      this.logger.info(`当前质押数量: ${formatEther(stakedAmount)} MON`);

      if (stakedAmount > 0n && skipIfStaked) {
        this.logger.success(`已经质押过 | ${formatEther(stakedAmount)} MON`);
        return {
          success: true,
          code: "ALREADY_STAKE",
          message: `已经质押过: ${formatEther(stakedAmount)}`,
        };
      }

      // 2. 检查余额
      const balance = await this.wallet.getBalance();
      if (amount === 0) {
        const formattedBalance = formatEther(balance);
        this.logger.info(`当前余额: ${formattedBalance} MON`);
        amount = this.getStakeAmount(formattedBalance);
      }

      this.logger.info(`质押金额: ${amount} MON`);
      const amountInWei = parseEther(amount.toString());

      if (balance < amountInWei) {
        const formattedBalance = formatEther(balance);
        const formattedAmount = formatEther(amountInWei);

        this.logger.error(
          `余额不足支付质押费用 | 当前余额: ${formattedBalance} MON | 需要: ${formattedAmount} MON`,
        );

        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: `余额不足支付质押费用`,
          data: {
            balance: formattedBalance,
            required: formattedAmount,
          },
        };
      }

      // 3. 执行质押 - 修改为使用 deposit 函数
      const tx = await this.contract.deposit(amountInWei, address, {
        value: amountInWei,
      });

      txHash = tx.hash;
      try {
        const receipt = await tx.wait();
        if (receipt.status !== 1) {
          throw new Error(`质押失败 | Hash: ${txHash} | Status: ${receipt.status}`);
        }
        this.logger.success(`质押成功 | Hash: ${txHash}`);
        return {
          success: true,
          hash: txHash,
          blockNumber: receipt.blockNumber,
        };
      } catch (error) {
        this.logger.error(`质押失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          hash: txHash,
        };
      }

    } catch (error) {
      this.logger.error(`质押失败: ${error.message}`);
      if (error.message.includes("insufficient balance")) {
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: error.message,
        };
      }

      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
        hash: txHash,
      };
    }
  }

  /**
   * 估算交易 gas
   * @param {Object} params 参数对象
   * @param {string} method - 方法名
   * @param {Array} args - 方法参数
   * @param {boolean} useFixed - 是否使用固定 gas，默认 false
   * @returns {Promise<{gasLimit: bigint, gasPrice: bigint}>}
   */
  async estimateGas({ method, args, useFixed = false }) {
    try {
      // 获取 gas 价格信息
      const feeData = await retry(async () => await this.wallet.provider.getFeeData(), 3, 2000);
      const gasPrice = feeData.gasPrice || feeData.maxFeePerGas || BigInt(0);

      let gasLimit;
      if (useFixed) {
        gasLimit = BigInt(300000);
      } else {
        try {
          const estimate = await retry(async () => await this.contract[method].estimateGas(...args), 3, 2000);
          gasLimit = BigInt(Math.ceil(Number(estimate) * 1.2)); // 增加 20% 缓冲
        } catch (error) {
          console.warn(`Gas 估算失败，使用固定值: ${error.message}`);
          gasLimit = BigInt(300000);
        }
      }

      return {
        gasLimit,
        gasPrice,
        ...(feeData.maxPriorityFeePerGas && {
          maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
          maxFeePerGas: feeData.maxFeePerGas,
        }),
      };
    } catch (error) {
      throw new Error(`Gas 估算失败: ${error.message}`);
    }
  }


  /**
   * 赎回质押存入
   * @returns {Promise<{success: boolean, hash?: string, message?: string}>}
   */
  async redeem() {
    try {
      const address = this.wallet.getAddress();

      // 获取可赎回的份额
      const stakedAmount = await retry(
        async () => await this.query_contract.balanceOf(address),
        3,
        2000
      );

      // 确保有可赎回的份额
      if (stakedAmount <= 0 || stakedAmount.toString() === '0') {
        return {
          success: false,
          code: "NO_STAKED_AMOUNT",
          message: "没有可赎回的份额"
        };
      }

      const tx = await this.contract.redeem(
        stakedAmount,  // shares - 赎回数量
        address,      // receiver - 接收地址
        address       // owner - 所有者地址
      );

      this.logger.info(`赎回金额: ${formatEther(stakedAmount)} shMON | 交易已发送 | Hash: ${tx.hash}`);

      try {
        const receipt = await tx.wait();
        if (receipt.status === 1) {
          this.logger.success(`赎回成功 | Hash: ${tx.hash}`);
          return {
            success: true,
            hash: tx.hash,
            code: "CLAIM_SUCCESS",
            message: `赎回成功`,
          };
        } else {
          throw new Error(`赎回失败 | Status: ${receipt.status}`);
        }
      } catch (error) {
        this.logger.error(`赎回失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          hash: tx.hash,
        };
      }

    } catch (error) {
      this.logger.error(`赎回失败: ${error.message}`);
      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
        hash: null,
      };
    }
  }

  /**
   * 质押捆绑
   * @returns {Promise<{success: boolean, hash?: string, message?: string}>}
   * @param {boolean} skipIfBonded - 是否跳过已质押捆绑
   */
  async bond(skipIfBonded = false) {
    try {
      const address = this.wallet.getAddress();

      // 获取已经质押捆绑金额
      const bondAmount = await retry(async () => await this.query_contract.balanceOfBonded(address), 3, 2000);
      this.logger.info(`当前质押绑定数量: ${formatEther(bondAmount)} shMON`);

      if (bondAmount > 0n && skipIfBonded) {
        this.logger.success(`已经质押绑定过 | ${formatEther(bondAmount)} MON`);
        return {
          success: true,
          code: "ALREADY_BONDED",
          message: `已经质押绑定过: ${formatEther(bondAmount)}`,
        };
      }

      const policyID = 4n;
      const depositAmount = await retry(async () => await this.query_contract.balanceOf(address), 3, 2000);
      this.logger.info(`当前存入数量: ${formatEther(depositAmount)} shMON`);

      if (depositAmount <= 0n) {
        this.logger.error(`shMON 数量不足`);
        return {
          success: false,
          code: "INSUFFICIENT_DEPOSIT",
          message: `shMON 数量不足`,
        };
      }
      const tx = await this.contract.bond(policyID, address, depositAmount, {
        value: 0,
      });

      const receipt = await tx.wait();
      if (receipt.status === 1) {
        this.logger.success(`质押绑定成功 | Hash: ${tx.hash}`);
        return {
          success: true,
          hash: tx.hash,
          code: "BOND_SUCCESS",
          message: `质押绑定成功`,
        };
      } else {
        this.logger.error(`质押绑定失败`);
        return {
          success: false,
          code: "BOND_FAILED",
          message: `质押绑定失败`,
        };
      }

    } catch (error) {
      this.logger.error(`质押绑定失败: ${error.message}`);
      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
      };
    }
  }




  /**
    * 执行领取操作
    * @private
    */
  async #executeClaim({ amount, policyId }) {
    try {
      this.logger.info('开始执行领取操作');

      // 执行领取交易
      const claimTx = await this.contract.claim(policyId, amount);
      this.logger.info(`领取交易已发送: ${claimTx.hash}`);

      // 等待交易确认
      const receipt = await claimTx.wait();

      if (receipt.status === 1) {
        this.logger.success(`领取成功: ${claimTx.hash}`);
        return true;
      } else {
        this.logger.error(`领取交易失败: ${claimTx.hash}`);
        return false;
      }

    } catch (error) {
      this.logger.error('领取操作失败:', error);
      return false;
    }
  }


  /**
   * 等待区块并自动领取
   * @param {Object} params
   * @param {BigInt} params.amount - 解质押数量
   * @param {string} params.policyId - 策略ID
   * @param {number} params.targetBlock - 目标区块
   * @returns {Promise<boolean>}
   */
  async #waitForBlockAndClaim(params) {
    const { amount, policyId, targetBlock } = params;
    const MAX_ATTEMPTS = 50;      // 最大尝试次数
    const CHECK_INTERVAL = 12000; // 12秒检查一次

    try {
      this.logger.info(`开始等待区块: 目标区块 ${targetBlock}`);

      for (let attempt = 0; attempt < MAX_ATTEMPTS; attempt++) {
        try {
          const currentBlock = await this.wallet.provider.getBlockNumber();
          const remainingBlocks = targetBlock - currentBlock;

          // 每5次检查输出一次日志
          if (attempt % 5 === 0) {
            this.logger.info(
              `等待中: 当前区块 ${currentBlock}, ` +
              `目标区块 ${targetBlock}, ` +
              `剩余 ${remainingBlocks} 个区块 ` +
              `(约 ${(remainingBlocks * 12 / 60).toFixed(1)} 分钟)`
            );
          }

          // 检查是否达到目标区块
          if (currentBlock >= targetBlock) {
            // 直接尝试领取
            const claimed = await this.#executeClaim({
              amount,
              policyId
            });

            if (claimed) {
              return true;
            }

            return false;
          }

          await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL));

        } catch (error) {
          this.logger.error('检查区块出错:', error);
        }
      }

      this.logger.error('等待区块超时');
      return false;

    } catch (error) {
      this.logger.error('等待区块过程出错:', error);
      return false;
    }
  }



  /**
   * 解质押
   * @returns {Promise<{success: boolean, hash?: string, message?: string}>}
   */
  async unbond() {
    try {
      const address = this.wallet.getAddress();
      const bondedAmountInWei = await this.query_contract.balanceOfBonded(address);

      if (bondedAmountInWei <= 0n) {
        this.logger.error('没有可解质押的金额');
        return {
          success: false,
          code: "NO_BONDED_AMOUNT",
          message: `没有可解质押的金额`,
        };
      }

      // 固定4, 还有一个5，默认用4
      const policyID = 4n;

      // 获取策略信息
      const policy = await this.contract.getPolicy(policyID);
      const escrowDuration = policy[0]; // 锁定期区块数

      this.logger.info(`锁定期: ${escrowDuration} 区块`);

      // 3. 执行解质押
      this.logger.info(`开始解质押 ${formatEther(bondedAmountInWei)} shMON`);
      const amountInWei = await this.query_contract.balanceOf(address);
      let newMinBalance = bondedAmountInWei + amountInWei;
      const unbondTx = await this.contract.unbond(policyID, bondedAmountInWei, newMinBalance);

      // 4. 等待解质押交易确认
      const unbondReceipt = await unbondTx.wait();
      this.logger.success(`解质押交易确认 | Hash: ${unbondTx.hash}`);

      // 5. 计算可领取的目标区块
      const targetBlock = unbondReceipt.blockNumber + Number(escrowDuration);
      this.logger.info(`需等待至区块: ${targetBlock} (约 ${Number(escrowDuration) * 12 / 60} 分钟)`);

      // 6. 等待区块并自动领取
      const result = await this.#waitForBlockAndClaim({
        amount: bondedAmountInWei,
        policyId: policyID,
        targetBlock
      });

      if (result) {
        return {
          success: true,
          hash: unbondTx.hash,
          message: `解质押并且领取成功`,
        };
      }





    } catch (error) {
      this.logger.error(`解质押失败: ${error.message}`);
      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
      };
    }
  }
}
