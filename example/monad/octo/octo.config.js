const monToTokenConfig = {
    // MON 兑换代币配置
    minAmount: 0.01,    // 最小兑换数量
    maxAmount: 0.15,    // 最大兑换数量
    decimals: 2         // 保留小数位数
};

const tokenSwapConfig = {
    // ERC20代币兑换配置
    minPercentage: 10,  // 最小交换百分比
    maxPercentage: 30,  // 最大交换百分比
    minAmount: 0.005,   // 最小交换金额(MON)
    decimals: 4         // 保留小数位数
};

const tokens = {
    // 代币配置
    MON: {
        name: "<PERSON><PERSON>",
        address: null,
        decimals: 18,
        native: true
    },
    WMON: {
        name: "WMO<PERSON>",
        address: "0x760AfE86e5de5fa0Ee542fc7B7B713e1c5425701",
        decimals: 18,
        native: false
    },
    USDC: {
        name: "USDC",
        address: "0xf817257fed379853cDe0fa4F97AB987181B1E5Ea",
        decimals: 6,
        native: false
    },
    USDT: {
        name: "USDT",
        address: "0x88b8E2161DEDC77EF4ab7585569D2415a1C1055D",
        decimals: 6,
        native: false
    },
    DAK: {
        name: "DAK",
        address: "0x0F0BDEbF0F83cD1EE3974779Bcb7315f9808c714",
        decimals: 18,
        native: false
    },
    TEST1: {
        name: "TEST1",
        address: "0xe42cFeCD310d9be03d3F80D605251d8D0Bc5cDF3",
        decimals: 18,
        native: false
    },
    TEST2: {
        name: "TEST2",
        address: "0x73c03bc8F8f094c61c668AE9833D7Ed6C04FDc21",
        decimals: 18,
        native: false
    }
};

export {
    monToTokenConfig,
    tokenSwapConfig,
    tokens
};
