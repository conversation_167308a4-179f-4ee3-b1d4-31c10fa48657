import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { getHumanRandomFloat, getHumanRandomInt } from "../../../base/tools/common.js";
import { monToTokenConfig, tokens, tokenSwapConfig } from "./octo.config.js";
import {
  ABI,
  DAK_CONTRACT,
  ROUTER_CONTRACT,
  TEST1_CONTRACT,
  TEST2_CONTRACT,
  USDC_CONTRACT,
  USDT_CONTRACT,
  WMON_CONTRACT,
} from "./abi.js";

// 使用配置文件中的代币配置
const AVAILABLE_TOKENS = {
    "mon": tokens.MON,
    "wmon": tokens.WMON,
    "usdc": tokens.USDC,
    "usdt": tokens.USDT,
    "dak": tokens.DAK,
    "test1": tokens.TEST1,
    "test2": tokens.TEST2
}

// ERC20 ABI
const ERC20_ABI = [
    "function balanceOf(address) view returns (uint256)",
    "function decimals() view returns (uint8)",
    "function symbol() view returns (string)",
    "function approve(address spender, uint256 amount) returns (bool)",
    "function allowance(address owner, address spender) view returns (uint256)"
]

// WMON ABI
const WMON_ABI = [
    "function deposit() payable",
    "function withdraw(uint256) nonpayable"
]

class OctoSwap {
    constructor(privateKey, walletIndex, rpc, proxy) {
        if (!privateKey || !walletIndex || !rpc) {
            throw new Error('必须提供私钥、钱包索引和RPC地址');
        }
        this.privateKey = privateKey;
        this.walletIndex = walletIndex;
        this.rpc = rpc;
        this.proxy = proxy;
        this.provider = new ethers.JsonRpcProvider(rpc);
        this.wallet = new ethers.Wallet(privateKey, this.provider);
        this.address = this.wallet.getAddress();
        this.routerContract = new ethers.Contract(ROUTER_CONTRACT, ABI.router, this.wallet);
    }

    /**
     * 生成随机交换金额
     */
    generateRandomAmount(isNative = false) {
        if (isNative) {
            // 对于原生代币，使用MON配置，生成人性化的随机浮点数
            const { minAmount, maxAmount, decimals } = monToTokenConfig
            const randomAmount = getHumanRandomFloat(minAmount, maxAmount)
            return parseFloat(randomAmount.toFixed(decimals))
        } else {
            // 对于ERC20代币，使用百分比配置，生成人性化的随机整数
            const { minPercentage, maxPercentage } = tokenSwapConfig
            return getHumanRandomInt(minPercentage, maxPercentage)
        }
    }

    /**
     * 获取代币余额
     */
    async getTokenBalance(token, walletAddress) {
        try {
            if (token.native) {
              return await this.provider.getBalance(walletAddress);
            } else {
                const tokenContract = new ethers.Contract(token.address, ERC20_ABI, this.provider)
              return await tokenContract.balanceOf(walletAddress);
            }
        } catch (error) {
            logger.error(`[${this.walletIndex}] 获取${token.name}余额失败: ${error.message}`)
            return 0n
        }
    }

    /**
     * 获取Gas参数 - 优化版本
     */
    async getGasParams(transactionType = 'swap') {
        try {
            const feeData = await this.provider.getFeeData()

            // 根据交易类型动态设置gasLimit
            let gasLimit
            switch (transactionType) {
                case 'approve':
                    gasLimit = 80000  // 授权交易通常需要较少gas
                    break
                case 'deposit':
                case 'withdraw':
                    gasLimit = 150000 // WMON存取款
                    break
                case 'swap_eth_token':
                    gasLimit = 200000 // ETH->Token交换
                    break
                case 'swap_token_eth':
                    gasLimit = 220000 // Token->ETH交换
                    break
                case 'swap_token_token':
                    gasLimit = 250000 // Token->Token交换
                    break
                default:
                    gasLimit = 300000 // 默认值
            }

            return {
                gasLimit,
                maxFeePerGas: feeData.maxFeePerGas ? (feeData.maxFeePerGas * 110n) / 100n : undefined,
                maxPriorityFeePerGas: feeData.maxPriorityFeePerGas ? (feeData.maxPriorityFeePerGas * 110n) / 100n : undefined
            }
        } catch (error) {
            logger.error(`[${this.walletIndex}] 获取Gas参数失败: ${error.message}`)
            return { gasLimit: 300000 }
        }
    }

    /**
     * 检查并授权代币 - 优化版本
     */
    async approveTokenIfNeeded(token, amount) {
        if (token.native) return true

        try {
            const walletAddress = await this.wallet.getAddress()
            const tokenContract = new ethers.Contract(token.address, ERC20_ABI, this.wallet)

            const currentAllowance = await tokenContract.allowance(walletAddress, ROUTER_CONTRACT)

            if (currentAllowance < amount) {
                logger.info(`[${this.walletIndex}] 授权 ${token.name} 代币...`)

                const gasParams = await this.getGasParams('approve')
                const tx = await tokenContract.approve(ROUTER_CONTRACT, ethers.MaxUint256, {
                    gasLimit: gasParams.gasLimit
                })

                logger.info(`[${this.walletIndex}] 授权交易已发送: ${tx.hash}`)
                const receipt = await tx.wait()
                logger.info(`[${this.walletIndex}] 授权确认，区块: ${receipt.blockNumber}`)

                // 等待1秒确保授权生效
                await new Promise(resolve => setTimeout(resolve, 1000))
            }

            return true
        } catch (error) {
            logger.error(`[${this.walletIndex}] 授权${token.name}失败: ${error.message}`)
            return false
        }
    }

    /**
     * 获取交换路径的预期输出
     */
    async getAmountsOut(amountIn, path) {
        try {
            const amounts = await this.routerContract.getAmountsOut(amountIn, path)
            return amounts[amounts.length - 1]
        } catch (error) {
            logger.error(`[${this.walletIndex}] 获取交换预期输出失败: ${error.message}`)
            throw error
        }
    }

    /**
     * 执行MON到WMON的转换（存款）- 优化版本
     */
    async depositMonToWmon(amount) {
        try {
            const wmonContract = new ethers.Contract(WMON_CONTRACT, WMON_ABI, this.wallet)
            const gasParams = await this.getGasParams('deposit')

            logger.info(`[${this.walletIndex}] 将 ${ethers.formatEther(amount)} MON 转换为 WMON...`)

            const tx = await wmonContract.deposit({
                value: amount,
                gasLimit: gasParams.gasLimit,
                maxFeePerGas: gasParams.maxFeePerGas,
                maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
            })

            logger.info(`[${this.walletIndex}] 存款交易已发送: ${tx.hash}`)
            const receipt = await tx.wait()
            logger.info(`[${this.walletIndex}] 存款确认，区块: ${receipt.blockNumber}`)

            return {
                success: true,
                hash: tx.hash,
                expectedOut: ethers.formatEther(amount),
                gasUsed: receipt.gasUsed.toString()
            }
        } catch (error) {
            logger.error(`[${this.walletIndex}] MON转WMON失败: ${error.message}`)
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            }
        }
    }

    /**
     * 执行WMON到MON的转换（提款）- 优化版本
     */
    async withdrawWmonToMon(amount) {
        try {
            const wmonContract = new ethers.Contract(WMON_CONTRACT, WMON_ABI, this.wallet)
            const gasParams = await this.getGasParams('withdraw')

            logger.info(`[${this.walletIndex}] 将 ${ethers.formatEther(amount)} WMON 转换为 MON...`)

            const tx = await wmonContract.withdraw(amount, {
                gasLimit: gasParams.gasLimit,
                maxFeePerGas: gasParams.maxFeePerGas,
                maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
            })

            logger.info(`[${this.walletIndex}] 提款交易已发送: ${tx.hash}`)
            const receipt = await tx.wait()
            logger.info(`[${this.walletIndex}] 提款确认，区块: ${receipt.blockNumber}`)

            return {
                success: true,
                hash: tx.hash,
                expectedOut: ethers.formatEther(amount),
                gasUsed: receipt.gasUsed.toString()
            }
        } catch (error) {
            logger.error(`[${this.walletIndex}] WMON转MON失败: ${error.message}`)
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            }
        }
    }

    /**
     * 执行常规代币交换 - 优化版本
     */
    async performRegularSwap(tokenA, tokenB, amountIn) {
        try {
            const walletAddress = await this.wallet.getAddress()
            const currentTime = Math.floor(Date.now() / 1000)
            const deadline = currentTime + 3600 * 6 // 6小时后过期

            // 构建交换路径
            const path = [
                tokenA.native ? WMON_CONTRACT : tokenA.address,
                tokenB.native ? WMON_CONTRACT : tokenB.address
            ]

            logger.info(`[${this.walletIndex}] 交换路径: ${path.join(' -> ')}`)

            // 获取预期输出
            let expectedOut
            try {
                expectedOut = await this.getAmountsOut(amountIn, path)
            } catch (error) {
                logger.error(`[${this.walletIndex}] 直接路径失败: ${error.message}`)
                throw new Error(`无法获取交换报价，可能是流动性不足`)
            }

            // 检查预期输出是否合理
            if (expectedOut <= 0) {
                throw new Error(`预期输出为0，交换无效`)
            }

            logger.info(`[${this.walletIndex}] 预期获得: ${ethers.formatUnits(expectedOut, tokenB.decimals)} ${tokenB.name}`)

            // 授权代币（如果需要）
            if (!tokenA.native) {
                const approved = await this.approveTokenIfNeeded(tokenA, amountIn)
                if (!approved) {
                    throw new Error(`${tokenA.name}授权失败`)
                }
            }

            // 计算最小输出（使用更宽松的滑点）
            const slippageTolerance = 15 // 15% 滑点，更宽松
            const minAmountOut = (expectedOut * BigInt(100 - slippageTolerance)) / 100n

            logger.info(`[${this.walletIndex}] 最小输出: ${ethers.formatUnits(minAmountOut, tokenB.decimals)} ${tokenB.name} (${slippageTolerance}% 滑点)`)

            // 根据交换类型获取优化的Gas参数
            let transactionType
            if (tokenA.native) {
                transactionType = 'swap_eth_token'
            } else if (tokenB.native) {
                transactionType = 'swap_token_eth'
            } else {
                transactionType = 'swap_token_token'
            }

            const gasParams = await this.getGasParams(transactionType)

            logger.info(`[${this.walletIndex}] Gas参数: ${JSON.stringify({
                gasLimit: gasParams.gasLimit,
                transactionType: transactionType,
                maxFeePerGas: gasParams.maxFeePerGas ? gasParams.maxFeePerGas.toString() : 'undefined',
                maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas ? gasParams.maxPriorityFeePerGas.toString() : 'undefined'
            })}`)

            let tx
            try {
                if (tokenA.native) {
                    // ETH -> Token
                    logger.info(`[${this.walletIndex}] 执行 ETH -> Token 交换`)
                    tx = await this.routerContract.swapExactETHForTokens(
                        minAmountOut, // 使用最小输出
                        path,
                        walletAddress,
                        deadline,
                        {
                            value: amountIn,
                            gasLimit: gasParams.gasLimit,
                            maxFeePerGas: gasParams.maxFeePerGas,
                            maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
                        }
                    )
                } else if (tokenB.native) {
                    // Token -> ETH
                    logger.info(`[${this.walletIndex}] 执行 Token -> ETH 交换`)
                    tx = await this.routerContract.swapExactTokensForETH(
                        amountIn,
                        minAmountOut,
                        path,
                        walletAddress,
                        deadline,
                        {
                            gasLimit: gasParams.gasLimit,
                            maxFeePerGas: gasParams.maxFeePerGas,
                            maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
                        }
                    )
                } else {
                    // Token -> Token
                    logger.info(`[${this.walletIndex}] 执行 Token -> Token 交换`)
                    tx = await this.routerContract.swapExactTokensForTokens(
                        amountIn,
                        minAmountOut,
                        path,
                        walletAddress,
                        deadline,
                        {
                            gasLimit: gasParams.gasLimit,
                            maxFeePerGas: gasParams.maxFeePerGas,
                            maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
                        }
                    )
                }
            } catch (estimateError) {
                logger.error(`[${this.walletIndex}] Gas估算失败: ${estimateError.message}`)

                // 尝试使用更宽松的参数
                const veryMinAmountOut = (expectedOut * BigInt(70)) / 100n // 30% 滑点
                const fallbackGasLimit = gasParams.gasLimit + 50000 // 增加50k gas

                logger.info(`[${this.walletIndex}] 尝试更宽松的滑点: ${ethers.formatUnits(veryMinAmountOut, tokenB.decimals)} ${tokenB.name} (30% 滑点)`)

                if (tokenA.native) {
                    tx = await this.routerContract.swapExactETHForTokens(
                        veryMinAmountOut,
                        path,
                        walletAddress,
                        deadline,
                        {
                            value: amountIn,
                            gasLimit: fallbackGasLimit
                        }
                    )
                } else if (tokenB.native) {
                    tx = await this.routerContract.swapExactTokensForETH(
                        amountIn,
                        veryMinAmountOut,
                        path,
                        walletAddress,
                        deadline,
                        { gasLimit: fallbackGasLimit }
                    )
                } else {
                    tx = await this.routerContract.swapExactTokensForTokens(
                        amountIn,
                        veryMinAmountOut,
                        path,
                        walletAddress,
                        deadline,
                        { gasLimit: fallbackGasLimit }
                    )
                }
            }

            logger.info(`[${this.walletIndex}] 交换交易已发送: ${tx.hash}`)
            const receipt = await tx.wait()

            if (receipt.status === 0) {
                throw new Error(`交易被回滚，可能是滑点过大或流动性不足`)
            }

            logger.info(`[${this.walletIndex}] 交换确认，区块: ${receipt.blockNumber}`)

            return {
                success: true,
                hash: tx.hash,
                expectedOut: ethers.formatUnits(expectedOut, tokenB.decimals),
                gasUsed: receipt.gasUsed.toString()
            }
        } catch (error) {
            logger.error(`[${this.walletIndex}] 常规交换失败: ${error.message}`)
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            }
        }
    }

    /**
     * 执行交换
     */
    async performSwap(tokenA, tokenB, amountIn) {
        try {
            logger.info(`[${this.walletIndex}] 开始交换: ${ethers.formatUnits(amountIn, tokenA.decimals)} ${tokenA.name} -> ${tokenB.name}`)

            // MON -> WMON (存款)
            if (tokenA.native && tokenB.name === "WMON") {
                return await this.depositMonToWmon(amountIn)
            }

            // WMON -> MON (提款)
            if (tokenA.name === "WMON" && tokenB.native) {
                return await this.withdrawWmonToMon(amountIn)
            }

            // 常规代币交换
            return await this.performRegularSwap(tokenA, tokenB, amountIn)

        } catch (error) {
            logger.error(`[${this.walletIndex}] 交换失败: ${error.message}`)
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            }
        }
    }

    /**
     * 获取钱包中有余额的代币 - 优化版本
     */
    async getTokensWithBalance() {
        try {
            const walletAddress = await this.wallet.getAddress()
            const tokensWithBalance = []

            for (const [key, token] of Object.entries(AVAILABLE_TOKENS)) {
                try {
                    const balance = await this.getTokenBalance(token, walletAddress)
                    const balanceFormatted = parseFloat(ethers.formatUnits(balance, token.decimals))

                    // 根据代币类型设置不同的最小余额阈值
                    let minBalance
                    if (token.native) {
                        minBalance = 0.025 // 原生代币需要更多余额（包含gas费）
                    } else if (token.decimals === 6) {
                        minBalance = 0.1 // USDC/USDT等6位小数代币
                    } else {
                        minBalance = 0.01 // 其他18位小数代币
                    }

                    if (balanceFormatted > minBalance) {
                        tokensWithBalance.push([key, balanceFormatted, balance])
                        logger.info(`[${this.walletIndex}] ✓ ${token.name || key.toUpperCase()}: ${balanceFormatted.toFixed(6)}`)
                    } else if (balanceFormatted > 0) {
                        logger.info(`[${this.walletIndex}] ✗ ${token.name || key.toUpperCase()}: ${balanceFormatted.toFixed(6)} (低于最小值 ${minBalance})`)
                    }
                } catch (error) {
                    logger.error(`[${this.walletIndex}] 检查${token.name || key.toUpperCase()}余额失败: ${error.message}`)
                }
            }

            return tokensWithBalance
        } catch (error) {
            logger.error(`[${this.walletIndex}] 获取代币余额失败: ${error.message}`)
            return []
        }
    }

    /**
     * 执行任务 - 随机代币交换和随机金额
     */
    async execute() {
        try {
            logger.info(`[${this.walletIndex}] 开始处理OctoSwap任务...`)

            const walletAddress = await this.wallet.getAddress()

            // 获取有余额的代币
            const tokensWithBalance = await this.getTokensWithBalance()

            logger.info(`[${this.walletIndex}] 发现的代币: ${JSON.stringify(
                tokensWithBalance.reduce((acc, [token, amount]) => {
                    acc[token] = amount.toFixed(6)
                    return acc
                }, {})
            )}`)

            if (tokensWithBalance.length === 0) {
                logger.warn(`[${this.walletIndex}] 没有找到足够余额的代币`)
                return {
                    success: false,
                    code: "ERROR",
                    message: "没有可用于交换的代币",
                    data: null
                }
            }

            // 优先选择有足够余额的代币作为源代币
            const sourceTokenData = tokensWithBalance[Math.floor(Math.random() * tokensWithBalance.length)]
            const sourceTokenKey = sourceTokenData[0]
            const sourceTokenBalance = sourceTokenData[1]
            const sourceToken = AVAILABLE_TOKENS[sourceTokenKey]

            // 随机选择目标代币（排除源代币）
            let availableTargetTokens = Object.keys(AVAILABLE_TOKENS).filter(key => key !== sourceTokenKey)
            const targetTokenKey = availableTargetTokens[Math.floor(Math.random() * availableTargetTokens.length)]
            const targetToken = AVAILABLE_TOKENS[targetTokenKey]

            logger.info(`[${this.walletIndex}] 选择的交换对: ${sourceToken.name} -> ${targetToken.name}`)
            logger.info(`[${this.walletIndex}] ${sourceToken.name}余额: ${sourceTokenBalance.toFixed(6)}`)

            let amountToSwap
            let amountFormatted
            let displayInfo

            if (sourceToken.native) {
                // 对于原生代币，使用固定金额配置
                const swapAmount = this.generateRandomAmount(true)
                const sourceBalanceWei = await this.getTokenBalance(sourceToken, walletAddress)
                const gasReserve = ethers.parseEther("0.02") // 预留0.02 MON作为gas费

                // 检查余额是否足够
                const requiredBalance = ethers.parseEther((swapAmount + 0.02).toString())
                if (sourceBalanceWei < requiredBalance) {
                    return {
                        success: false,
                        code: "ERROR",
                        message: `MON余额不足，需要至少${(swapAmount + 0.02).toFixed(4)} MON（包含gas费）`,
                        data: null
                    }
                }

                amountToSwap = ethers.parseEther(swapAmount.toString())
                amountFormatted = swapAmount
                displayInfo = `${swapAmount} MON (固定金额)`
            } else {
                // 对于ERC20代币，使用百分比配置
                const percentage = this.generateRandomAmount(false)
                const sourceBalanceWei = await this.getTokenBalance(sourceToken, walletAddress)
                amountToSwap = (sourceBalanceWei * BigInt(percentage)) / 100n

                if (amountToSwap <= 0) {
                    return {
                        success: false,
                        code: "ERROR",
                        message: `${sourceToken.name}余额不足`,
                        data: null
                    }
                }

                amountFormatted = parseFloat(ethers.formatUnits(amountToSwap, sourceToken.decimals))

                // 检查最小交换金额
                const minAmountThreshold = sourceToken.decimals === 6 ? 0.01 : 0.001
                if (amountFormatted < minAmountThreshold) {
                    return {
                        success: false,
                        code: "ERROR",
                        message: `交换金额太小: ${amountFormatted.toFixed(6)} ${sourceToken.name}`,
                        data: null
                    }
                }

                displayInfo = `${amountFormatted.toFixed(tokenSwapConfig.decimals)} ${sourceToken.name} (${percentage}%余额)`
            }

            logger.info(`[${this.walletIndex}] 准备交换 ${displayInfo} 到 ${targetToken.name}`)

            // 执行交换
            const result = await this.performSwap(sourceToken, targetToken, amountToSwap)

            if (result.success) {
                // 获取交换后的余额
                const newSourceBalance = await this.getTokenBalance(sourceToken, walletAddress)
                const newTargetBalance = await this.getTokenBalance(targetToken, walletAddress)

                // 格式化余额显示
                const newSourceBalanceFormatted = parseFloat(ethers.formatUnits(newSourceBalance, sourceToken.decimals)).toFixed(6)
                const newTargetBalanceFormatted = parseFloat(ethers.formatUnits(newTargetBalance, targetToken.decimals)).toFixed(6)

                logger.success(`[${this.walletIndex}] OctoSwap交换任务完成: ${JSON.stringify({
                    '交易哈希': result.hash,
                    '输入': displayInfo,
                    '输出': `${result.expectedOut} ${targetToken.name}`,
                    '交换后余额': `${sourceToken.name}: ${newSourceBalanceFormatted}, ${targetToken.name}: ${newTargetBalanceFormatted}`,
                    'Gas使用': result.gasUsed
                })}`)

                return {
                    success: true,
                    code: "SUCCESS",
                    message: "OctoSwap交换任务完成",
                    data: {
                        hash: result.hash,
                        tokenIn: sourceToken.name,
                        tokenOut: targetToken.name,
                        amountIn: amountFormatted.toFixed(tokenSwapConfig.decimals),
                        expectedOut: result.expectedOut,
                        gasUsed: result.gasUsed,
                        newBalances: {
                            [sourceToken.name]: newSourceBalanceFormatted,
                            [targetToken.name]: newTargetBalanceFormatted
                        }
                    }
                }
            } else {
                logger.error(`[${this.walletIndex}] OctoSwap任务失败: ${JSON.stringify({
                    '原因': result.message,
                    '尝试交换': displayInfo
                })}`)

                return {
                    success: false,
                    code: "ERROR",
                    message: result.message,
                    data: null
                }
            }

        } catch (error) {
            logger.error(`[${this.walletIndex}] OctoSwap任务处理出错: ${error.message}`)
            return {
                success: false,
                code: "ERROR",
                message: error.message,
                data: null
            }
        }
    }
}

export default OctoSwap;
