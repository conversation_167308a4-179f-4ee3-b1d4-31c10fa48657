import { ethers } from "ethers";
import { getHumanRandomFloat } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";
import { monToTokenConfig, targetTokens, contracts } from "./bubblefi.config.js";

// 使用配置文件中的合约地址和代币配置
const BUBBLEFI_ROUTER = contracts.router;
const WMON_ADDRESS = contracts.wmon;
const TARGET_TOKENS = targetTokens;

// BubbleFi Router ABI - 只包含需要的函数
const BUBBLEFI_ABI = [
    {
        "inputs": [
            {"internalType": "uint256", "name": "_amountIn", "type": "uint256"},
            {"internalType": "address[]", "name": "_path", "type": "address[]"}
        ],
        "name": "getAmountsOut",
        "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "uint256", "name": "_amountOutMin", "type": "uint256"},
            {"internalType": "address[]", "name": "_path", "type": "address[]"},
            {"internalType": "address", "name": "_receiver", "type": "address"},
            {"internalType": "uint256", "name": "_deadline", "type": "uint256"},
            {
                "components": [
                    {"internalType": "bool", "name": "enter", "type": "bool"},
                    {
                        "components": [
                            {"internalType": "uint256", "name": "numerator", "type": "uint256"},
                            {"internalType": "uint256", "name": "denominator", "type": "uint256"}
                        ],
                        "internalType": "struct BubbleV1Types.Fraction",
                        "name": "fractionOfSwapAmount",
                        "type": "tuple"
                    },
                    {"internalType": "address", "name": "raffleNftReceiver", "type": "address"}
                ],
                "internalType": "struct BubbleV1Types.Raffle",
                "name": "_raffle",
                "type": "tuple"
            }
        ],
        "name": "swapExactNativeForTokens",
        "outputs": [
            {"internalType": "uint256[]", "name": "", "type": "uint256[]"},
            {"internalType": "uint256", "name": "", "type": "uint256"}
        ],
        "stateMutability": "payable",
        "type": "function"
    }
]

// ERC20 ABI
const ERC20_ABI = [
    "function balanceOf(address) view returns (uint256)",
    "function decimals() view returns (uint8)",
    "function symbol() view returns (string)"
]

class BubbleFi {
    constructor(privateKey, walletIndex, rpc, proxy) {
        if (!privateKey || !walletIndex || !rpc) {
            throw new Error('必须提供私钥、钱包索引和RPC地址');
        }
        this.walletIndex = walletIndex
        this.provider = new ethers.JsonRpcProvider(rpc);
        this.wallet = new ethers.Wallet(privateKey, this.provider)
        this.routerContract = new ethers.Contract(BUBBLEFI_ROUTER, BUBBLEFI_ABI, this.wallet)
    }

    /**
     * 生成随机交换金额
     */
    generateRandomAmount() {
        const { minAmount, maxAmount, decimals } = monToTokenConfig
        // const randomAmount = Math.random() * (maxAmount - minAmount) + minAmount
        // return parseFloat(randomAmount.toFixed(decimals))
        return getHumanRandomFloat(minAmount, maxAmount)
    }

    /**
     * 获取代币余额
     */
    async getTokenBalance(tokenAddress, walletAddress) {
        try {
            if (!tokenAddress || tokenAddress === ethers.ZeroAddress) {
                // 原生代币余额
                return await this.provider.getBalance(walletAddress)
            } else {
                // ERC20代币余额
                const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, this.provider)
                return await tokenContract.balanceOf(walletAddress)
            }
        } catch (error) {
            logger.error(`获取代币余额失败: ${error.message}`)
            return 0n
        }
    }

    /**
     * 获取交换预期输出
     */
    async getAmountsOut(amountIn, path) {
        try {
            const amounts = await this.routerContract.getAmountsOut(amountIn, path)
            return amounts[amounts.length - 1]
        } catch (error) {
            logger.error(`获取交换预期输出失败: ${error.message}`)
            throw error
        }
    }

    /**
     * 获取Gas参数
     */
    async getGasParams() {
        try {
          const feeData = await this.provider.getFeeData();
          return {
            gasLimit: 300000, // 加 20% 缓冲
            maxFeePerGas: feeData.maxFeePerGas ? (feeData.maxFeePerGas * 110n) / 100n : undefined,
            maxPriorityFeePerGas: feeData.maxPriorityFeePerGas ? (feeData.maxPriorityFeePerGas * 110n) / 100n : undefined,
          };
        } catch (error) {
          logger.error(`获取 Gas 参数失败: ${error.message}`);
          return { gasLimit: 300000 };
        }
      }



    /**
     * 随机选择目标代币
     */
    selectRandomTargetToken() {
        const tokenKeys = Object.keys(TARGET_TOKENS)
        const randomKey = tokenKeys[Math.floor(Math.random() * tokenKeys.length)]
        return TARGET_TOKENS[randomKey]
    }

    /**
     * 执行MON到目标代币的交换
     */
    async swapMonToToken(amountIn, targetToken) {
        try {
            const walletAddress = await this.wallet.getAddress()

            logger.info(`${this.walletIndex} 开始交换: ${ethers.formatEther(amountIn)}MON -> ${targetToken.name}`)

            // 构建交换路径: MON -> WMON -> 目标代币
            const path = [WMON_ADDRESS, targetToken.address]

            logger.info(`${this.walletIndex} 交换路径: MON -> ${path.join(' -> ')}`)

            // 获取预期输出
            let expectedOut
            try {
                expectedOut = await this.getAmountsOut(amountIn, path)
                const expectedOutFormatted = ethers.formatUnits(expectedOut, targetToken.decimals)
                logger.info(`${this.walletIndex} 预期获得: ${expectedOutFormatted}${targetToken.name}`)
            } catch (error) {
                logger.error(`${this.walletIndex} 获取预期输出失败: ${error.message}`)
                throw new Error(`无法获取交换报价: ${error.message}`)
            }

            // 计算最小输出（15%滑点保护）
            const slippageTolerance = 15
            const minAmountOut = (expectedOut * BigInt(100 - slippageTolerance)) / 100n

            const minAmountOutFormatted = ethers.formatUnits(minAmountOut, targetToken.decimals)
            logger.info(`${this.walletIndex} 最小输出: ${minAmountOutFormatted} ${targetToken.name} (${slippageTolerance}% 滑点)`)

            // 设置截止时间
            const currentTime = Math.floor(Date.now() / 1000)
            const deadline = currentTime + 3600 * 6 // 6小时后过期

            // 构建Raffle参数（基于您提供的数据）
            const raffleParams = {
                enter: false, // 不参与抽奖
                fractionOfSwapAmount: {
                    numerator: 1n,
                    denominator: 100n // 1%
                },
                raffleNftReceiver: walletAddress
            }

            // 获取Gas参数
            const gasParams = await this.getGasParams(amountIn, walletAddress, deadline, raffleParams)
            logger.info(`${this.walletIndex} 发送交易...amountIn=${ethers.formatEther(amountIn)}MON, minAmountOut=${minAmountOutFormatted}${targetToken.name}, deadline=${deadline}, gasLimit=${gasParams.gasLimit}`)

            // 执行交换
            const tx = await this.routerContract.swapExactNativeForTokens(
                minAmountOut,
                path,
                walletAddress,
                deadline,
                raffleParams,
                {
                    value: amountIn,
                    gasLimit: gasParams.gasLimit,
                    maxFeePerGas: gasParams.maxFeePerGas,
                    maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
                }
            )

            logger.info(`${this.walletIndex} 交换交易已发送: ${tx.hash}`)
            const receipt = await tx.wait()

            if (receipt.status === 0) {
                throw new Error(`${this.walletIndex} 交易被回滚，可能是滑点过大或流动性不足`)
            }

            logger.info(`${this.walletIndex} 交换确认，区块: ${receipt.blockNumber}`)

            return {
                success: true,
                hash: tx.hash,
                expectedOut: ethers.formatUnits(expectedOut, targetToken.decimals),
                gasUsed: receipt.gasUsed.toString()
            }

        } catch (error) {
            logger.error(`MON->${targetToken.name}交换失败: ${error.message}`)
            return {
                success: false,
                message: error.message
            }
        }
    }

    /**
     * 执行任务 - 随机选择目标代币和随机金额进行交换
     */
    async executeTask() {
        try {
            logger.info(`${this.walletIndex} 开始处理BubbleFi交换任务...`)

            const walletAddress = await this.wallet.getAddress()

            // 检查MON余额
            const monBalance = await this.getTokenBalance(null, walletAddress)
            const monBalanceFormatted = parseFloat(ethers.formatEther(monBalance))

            logger.info(`${this.walletIndex} MON余额: ${monBalanceFormatted.toFixed(6)}`)

            // 生成随机交换金额
            const swapAmount = this.generateRandomAmount()

            // 检查是否有足够余额（随机金额 + gas费）
            const requiredBalance = swapAmount + 0.02 // 随机金额 + 0.02 gas费
            if (monBalanceFormatted < requiredBalance) {
                return {
                    success: false,
                    message: `MON余额不足，需要至少${requiredBalance.toFixed(4)} MON（包含gas费）`
                }
            }

            // 随机选择目标代币
            const targetToken = this.selectRandomTargetToken()
            logger.info(`${this.walletIndex} 随机选择目标代币: ${targetToken.name}`)

            const amountToSwap = ethers.parseEther(swapAmount.toString())
            logger.info(`${this.walletIndex} 准备交换 ${swapAmount} MON 到 ${targetToken.name}`)

            // 获取交换前的目标代币余额
            const tokenBalanceBefore = await this.getTokenBalance(targetToken.address, walletAddress)

            // 执行交换
            const result = await this.swapMonToToken(amountToSwap, targetToken)

            if (result.success) {
                // 获取交换后的余额
                const newMonBalance = await this.getTokenBalance(null, walletAddress)
                const newTokenBalance = await this.getTokenBalance(targetToken.address, walletAddress)

                // 计算实际获得的代币数量
                const tokenReceived = newTokenBalance - tokenBalanceBefore

                // 格式化余额显示
                const newMonBalanceFormatted = parseFloat(ethers.formatEther(newMonBalance)).toFixed(6)
                const newTokenBalanceFormatted = parseFloat(ethers.formatUnits(newTokenBalance, targetToken.decimals)).toFixed(6)
                const tokenReceivedFormatted = parseFloat(ethers.formatUnits(tokenReceived, targetToken.decimals)).toFixed(6)

                logger.success(`${this.walletIndex} BubbleFi交换任务完成,
                    交易哈希: ${result.hash},
                    输入: ${swapAmount}MON,
                    目标代币: ${targetToken.name},
                    实际获得: ${tokenReceivedFormatted} ${targetToken.name},
                    预期获得: ${result.expectedOut} ${targetToken.name},
                    交换后余额: ${newMonBalanceFormatted}MON, ${newTokenBalanceFormatted}${targetToken.name},
                    Gas使用: ${result.gasUsed}`
                )

                return {
                    success: true,
                    data: {
                        hash: result.hash,
                        tokenIn: 'MON',
                        tokenOut: targetToken.name,
                        amountIn: swapAmount.toString(),
                        expectedOut: result.expectedOut,
                        actualOut: tokenReceivedFormatted,
                        gasUsed: result.gasUsed,
                        newBalances: {
                            MON: newMonBalanceFormatted,
                            [targetToken.name]: newTokenBalanceFormatted
                        }
                    }
                }
            } else {
                logger.error(`${this.walletIndex} BubbleFi交换任务失败,
                    原因: ${result.message},
                    目标代币: ${targetToken.name},
                    尝试交换金额: ${swapAmount}MON`
                )

                return {
                    success: false,
                    message: result.message
                }
            }

        } catch (error) {
            logger.error(`钱包 ${this.walletIndex} BubbleFi任务处理出错:`, error.message)
            return {
                success: false,
                message: error.message
            }
        }
    }
}

export default BubbleFi;
