export default {
    // MON 兑换代币配置
    monToTokenConfig: {
        minAmount: 0.01,   // 最小兑换数量
        maxAmount: 0.1,    // 最大兑换数量
        decimals: 2        // 保留小数位数
    },

    // 代币兑换 Mon 配置
    tokenToMonConfig: {
        ETH: {
            minAmount: 0.1,     // 最小兑换数量
            maxAmount: 0.5,     // 最大兑换数量
            decimals: 2         // 保留小数位数
        },
        WETH: {
            minAmount: 0.1,    // 最小兑换数量
            maxAmount: 0.5,    // 最大兑换数量
            decimals: 2        // 保留小数位数
        },
        USDC: {
            minAmount: 0.1,     // 最小兑换数量
            maxAmount: 2,       // 最大兑换数量
            decimals: 1         // 保留小数位数
        },
        USDT: {
            minAmount: 0.1,     // 最小兑换数量
            maxAmount: 2,    // 最大兑换数量
            decimals: 1       // 保留小数位数
        },
        WBTC: {
            minAmount: 0.001,    // 最小兑换数量
            maxAmount: 0.01,    // 最大兑换数量
            decimals: 6         // 保留小数位数
        },
        shMON: {
            minAmount: 0.01,    // 最小兑换数量
            maxAmount: 0.5,     // 最大兑换数量
            decimals: 2         // 保留小数位数
        },
        DAK: {
            minAmount: 0.01,    // 最小兑换数量
            maxAmount: 0.5,     // 最大兑换数量
            decimals: 2         // 保留小数位数
        },
        YAKI: {
            minAmount: 1,       // 最小兑换数量
            maxAmount: 10,      // 最大兑换数量
            decimals: 0         // 保留小数位数
        },
        CHOG: {
            minAmount: 1,       // 最小兑换数量
            maxAmount: 10,      // 最大兑换数量
            decimals: 0         // 保留小数位数
        },
        mamaBTC: {
            minAmount: 0.00001, // 最小兑换数量
            maxAmount: 0.001,   // 最大兑换数量
            decimals: 5         // 保留小数位数
        },
        wWETH: {
            minAmount: 0.01,    // 最小兑换数量
            maxAmount: 0.5,     // 最大兑换数量
            decimals: 2         // 保留小数位数
        },

        // 添加流动性配置
        liquidityConfig: {
            USDC: {
                minAmount: 0.3,     // 最小数量
                maxAmount: 0.8,     // 最大数量
                decimals: 2         // 保留小数位数
            },
        },
    },
    // 代币配置
    tokens: {
        ETH: {
            address: '******************************************',
            decimals: 18
        },
        WETH: {
            address: '******************************************',
            decimals: 18
        },
        USDC: {
            address: '******************************************',
            decimals: 6
        },
        USDT: {
            address: '******************************************',
            decimals: 6
        },
        WBTC: {
            address: '******************************************',
            decimals: 8
        },
        shMON: {
            address: '******************************************',
            decimals: 18
        },
        DAK: {
            address: "******************************************",
            decimals: 18,
        },
        YAKI: {
            address: "******************************************",
            decimals: 18,
        },
        CHOG: {
            address: "******************************************",
            decimals: 18,
        },
        mamaBTC: {
            address: "******************************************",
            decimals: 8,
        },
        wWETH: {
            address: "******************************************",
            decimals: 18,
        },
    }

};
