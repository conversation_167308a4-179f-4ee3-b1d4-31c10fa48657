import CSV from "../../../base/tools/csv.js";
import {
  filterWalletsByIndex,
  parseTOMLConfig,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import AmbientDex from "./ambient.js";
import logger from "../../../base/tools/logger.js";
import { ethers, formatEther, parseEther } from "ethers";
import SecureEncryption from "../../../base/tools/secure-encryption.js";

const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad;
const rpc = monadConfig.rpc;
const explorerUrl = monadConfig.explorerUrl;
const min_delay_seconds = 300; // 5分钟
const max_delay_seconds = 600; // 10分钟

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}


/**
 * 大额交易, 刷流水， swap过去后立即swap回来
 */
async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  let swapSuccessCount = 0;
  let swapFailedCount = 0;
  const secureEncryption = new SecureEncryption();
  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    // 90% 概率执行
    if (Math.random() >= 0.9) {
      logger.info(`[${index}] 随机概率不执行`);
      continue;
    }

    const startTime = Date.now();
    const ambient = new AmbientDex(index, private_key, rpc, proxy);

    try {
      const outTokens = ['USDC', 'USDT', 'WBTC', 'ETH', 'WETH'];
      const outToken = outTokens[Math.floor(Math.random() * outTokens.length)];

      let inTokenBalanceWei = await ambient.getNativeBalance();
      let inTokenBalance = formatEther(inTokenBalanceWei);

      // 随机获取质押总金额的10% ~ 30%, ，然后取整
      const percentage = 10 + Math.floor(Math.random() * 31);
      const swapAmount = Math.floor(inTokenBalance * percentage / 100);
      const swapAmountWei = parseEther(swapAmount.toString());

      if (swapAmountWei > inTokenBalanceWei) {
        logger.info(`[${index}] 质押金额 ${swapAmount} 大于 质押总金额 ${inTokenBalance}, 跳过`);
        continue;
      }
      logger.info(`[${index}] 准备 swap, MON: ${inTokenBalance}, swap ${swapAmount} MON 到 ${outToken}`);
      let { success, message, data } = await ambient.swapMon2Token(outToken, swapAmountWei);
      if (!success) {
        logger.error(`[${index}] 质押失败: ${message}`);
        continue;
      }

      await sleep(getRandomValue(2, 5));


      // 然后swap回来
      let outTokenBalanceDecimal = await ambient.getDecimalBalanceWithToken(outToken);
      let outTokenBalance = ambient.convertTokenToDecimal(outToken, outTokenBalanceDecimal);
      inTokenBalanceWei = await ambient.getNativeBalance();
      inTokenBalance = formatEther(inTokenBalanceWei);

      logger.info(`[${index}] swap后余额 - ${outToken}: ${outTokenBalance},  MON: ${inTokenBalance}`);

      let tx = await ambient.swapToken2Mon(outToken, outTokenBalanceDecimal);
      if (!tx) {
        logger.error(`[${index}] swap ${outToken} 到 MON 失败: ${message2}`);
        continue;
      }

      // 然后再次swap回来
      await sleep(getRandomValue(2, 5));
      outTokenBalanceDecimal = await ambient.getDecimalBalanceWithToken(outToken);
      outTokenBalance = ambient.convertTokenToDecimal(outToken, outTokenBalanceDecimal);
      inTokenBalanceWei = await ambient.getNativeBalance();
      inTokenBalance = formatEther(inTokenBalanceWei);

      logger.info(`[${index}] 再次swap后余额 - ${outToken}: ${outTokenBalance},  MON: ${inTokenBalance}`);

    } catch (error) {
      logger.error(`执行任务失败: ${error.message}`);
      swapFailedCount++;
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印统计信息
  logger.info("------------------------");
  logger.info(`总计处理钱包: ${shuffledWallets.length}`);
  logger.info(`交换成功: ${swapSuccessCount}`);
  logger.info(`交换失败: ${swapFailedCount}`);
}

// 执行任务
// node example/monad/ambient/task.js 1-10
main();
