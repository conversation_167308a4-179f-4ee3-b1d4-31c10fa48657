import CSV from "../../../base/tools/csv.js";
import {
  filterWalletsByIndex,
  parseTOMLConfig,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import AmbientDex from "./ambient.js";
import logger from "../../../base/tools/logger.js";
import { formatEther } from "ethers";
import SecureEncryption from "../../../base/tools/secure-encryption.js";
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad;
const rpc = monadConfig.rpc;
const explorerUrl = monadConfig.explorerUrl;
const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const [indexArg, inToken, outToken, amount] = args;

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  let swapSuccessCount = 0;
  let swapFailedCount = 0;

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const ambient = new AmbientDex(index, private_key, rpc, proxy);
    let balance = await ambient.getNativeBalance();
    try {
      if (inToken === "MON") {
        let outTokenBalance = await ambient.getBalanceWithToken(outToken);
        logger.info(`[${index}] 交易前余额 - ${inToken}: ${formatEther(balance)},  ${outToken}: ${outTokenBalance}`);
        await ambient.swapMon2Token(outToken, ambient.convertDecimalToToken(outToken, amount));
        await sleep(3);

        balance = await ambient.getNativeBalance();
        outTokenBalance = await ambient.getBalanceWithToken(outToken);
        logger.info(`[${index}] 交易后余额 - ${inToken}: ${formatEther(balance)},  ${outToken}: ${outTokenBalance}`);

      } else if (outToken === "MON") {
        let inTokenBalance = await ambient.getBalanceWithToken(inToken);
        logger.info(`[${index}] 交易前余额 - ${outToken}: ${formatEther(balance)},  ${inToken}: ${inTokenBalance}`);
        await ambient.swapToken2Mon(inToken, ambient.convertDecimalToToken(inToken, amount));
        await sleep(3);

        balance = await ambient.getNativeBalance();
        inTokenBalance = await ambient.getBalanceWithToken(inToken);
        logger.info(`[${index}] 交易后余额 - ${outToken}: ${formatEther(balance)},  ${inToken}: ${inTokenBalance}`);
      } else {
        throw new Error("输入和输出代币必须有一个是MON");
      }

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      logger.info(`任务完成，用时: ${duration} 秒`);

      // 如果是最后一个任务，则不延迟
      if (j === shuffledWallets.length - 1) {
        break;
      }

      // 任务间随机延迟
      const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
      await sleep(delay);
    } catch (error) {
      logger.error(`执行任务失败: ${error.message}`);
      swapFailedCount++;
    }
  }
}

// 执行任务
// node example/monad/ambient/task_custom_swap.js 1-10 MON USDT 0.01
main();
