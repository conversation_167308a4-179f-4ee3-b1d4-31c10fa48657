import { ethers, formatEther } from "ethers";
import { getRandomValue, retry, sleep } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";
import config from "./ambient.config.js"; // 导入配置文件
const tokens = config.tokens;

// Ambient 常量
const AMBIENT_CONTRACT = "******************************************";
const ZERO_ADDRESS = "******************************************";
const POOL_IDX = 36000;
const RESERVE_FLAGS = 0;
const TIP = 0;
const MAX_SQRT_PRICE = "21267430153580247136652501917186561137";
const MIN_SQRT_PRICE = "65537";
const explorerUrl = "https://testnet.monadexplorer.com/tx/";


// ABI 定义
const AMBIENT_ABI = [
  {
    inputs: [{
      internalType: "uint16",
      name: "callpath",
      type: "uint16"
    }, {
      internalType: "bytes",
      name: "cmd",
      type: "bytes"
    }],
    name: "userCmd",
    outputs: [{
      internalType: "bytes",
      name: "",
      type: "bytes"
    }],
    stateMutability: "payable",
    type: "function"
  }
];


const ERROR_CODES = {
  INSUFFICIENT_FUNDS: "INSUFFICIENT_FUNDS",
};


export default class AmbientDex {
  /**
   * 创建 AmbientDex 实例
   * @param {number} index - 账户索引
   * @param {string} privateKey - 钱包私钥
   * @param {string} rpcUrl - RPC 节点地址
   * @param {string} proxy - 代理配置
   */
  constructor(index = 0, privateKey, rpcUrl, proxy = "") {
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    if (!rpcUrl || typeof rpcUrl !== "string") {
      throw new Error("必须提供有效的 RPC 节点地址");
    }

    this.index = index;
    this.proxy = proxy || config.PROXY_URL;

    try {
      const provider = new ethers.JsonRpcProvider(rpcUrl);
      this.wallet = new ethers.Wallet(privateKey, provider);
      this.contract = new ethers.Contract(
        AMBIENT_CONTRACT,
        AMBIENT_ABI,
        this.wallet
      );

    } catch (error) {
      logger.error(`[${this.index}] Contract initialization error:`, error);
      throw error;
    }
  }

  /**
   * 获取当前网络的 gas 参数
   */
  async #getGasParams() {
    try {
      const latestBlock = await this.wallet.provider.getBlock("latest");
      const baseFee = latestBlock.baseFeePerGas;
      const maxPriorityFee = await this.wallet.provider.send("eth_maxPriorityFeePerGas", []);
      const maxPriorityFeePerGas = BigInt(maxPriorityFee);
      const maxFeePerGas = baseFee + maxPriorityFeePerGas;

      return {
        maxFeePerGas,
        maxPriorityFeePerGas,
      };
    } catch (error) {
      this.logger.error(`[${this.accountIndex}] 获取 gas 参数失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 随机获取兑换金额
   * @param {number} monWei - MON余额
   * @returns {number}
   */
  async #getRandomMonWeiAmount() {
    const monConfig = config.monToTokenConfig;
    const minAmount = monConfig.minAmount;
    const maxAmount = monConfig.maxAmount;
    const decimals = monConfig.decimals;

    const monWei = await this.getNativeBalance();

    // 如果余额小于最小兑换金额，则返回0
    if (monWei < ethers.parseEther(minAmount.toString())) {
      return 0n;
    }

    // 生成一个随机金额
    for (let i = 0; i < 20; i++) {
      const randomAmount = minAmount + Math.random() * (maxAmount - minAmount);
      const formattedAmount = randomAmount.toFixed(decimals);
      const amountWei = ethers.parseEther(formattedAmount.toString());
      if (monWei > amountWei) {
        return amountWei;
      }
    }

    return 0n;
  }


  /**
   * 获取原生代币余额
   * @returns
   */
  async getNativeBalance() {
    const balance = await this.wallet.provider.getBalance(this.wallet.address);
    return balance;
  }

  /**
   * 将代币的最小单位转换为人类可读的数量
   * @param {string} token - 代币符号
   * @param {bigint} amountInSmallestUnit - 代币最小单位数量
   * @returns {string} 人类可读的代币数量
   */
  convertTokenToDecimal(token, amountInSmallestUnit) {
    const tokenConfig = tokens[token];
    if (!tokenConfig) {
      throw new Error(`未知代币: ${token}`);
    }

    return ethers.formatUnits(amountInSmallestUnit, tokenConfig.decimals);
  }

  /**
    * 将人类可读的代币数量转换为最小单位
    * @param {string} token - 代币符号
    * @param {string|number} amount - 人类可读的代币数量
    * @returns {bigint} 代币最小单位数量
    */
  convertDecimalToToken(token, amount) {
    const tokenConfig = tokens[token];
    if (!tokenConfig) {
      throw new Error(`未知代币: ${token}`);
    }
    return ethers.parseUnits(amount.toString(), tokenConfig.decimals);
  }

  /**
   * 获取指定代币余额
   * @param {string} token 代币符号
   * @returns
   */
  async getBalanceWithToken(token) {
    try {
      const decimalBalance = await this.getDecimalBalanceWithToken(token);
      if (decimalBalance == 0) {
        return 0;
      }
      return this.convertTokenToDecimal(token, decimalBalance);
    } catch (error) {
      logger.error(`[${this.index}] 获取 ${token} 余额失败: ${error.message}`);
      throw error;
    }
  }


  /**
  * 获取指定代币余额
  * @param {string} token 代币符号
  * @returns
  */
  async getDecimalBalanceWithToken(token) {
    try {
      const tokenConfig = tokens[token];
      if (!tokenConfig) {
        throw new Error(`未知代币: ${token}`);
      }

      const tokenContract = new ethers.Contract(
        tokenConfig.address,
        ["function balanceOf(address) view returns (uint256)"],
        this.wallet
      );

      const balance = await retry(async () => {
        return await tokenContract.balanceOf(this.wallet.address);
      });
      return balance
    } catch (error) {
      logger.error(`[${this.index}] 获取 ${token} 余额失败: ${error.message}`);
      throw error;
    }
  }


  // 随机获取一个token
  #getRandomToken() {
    const tokens = Object.keys(config.tokens);
    // 70% 返回
    if (Math.random() < 0.7) {
      return "USDC";
    }
    return tokens[Math.floor(Math.random() * tokens.length)];
  }



  getSwapAmount(token, amount = 0) {

    const tokenConfig = config.tokenToMonConfig[token];
    if (!tokenConfig) {
      throw new Error(`未知代币: ${token}`);
    }

    // 70% 概率返回原有金额
    if (Math.random() < 0.7) {
      return amount;
    }

    const minAmount = tokenConfig.minAmount;
    const maxAmount = tokenConfig.maxAmount;
    const decimals = tokenConfig.decimals;

    for (let i = 0; i < 10; i++) {
      const randomAmount = minAmount + Math.random() * (maxAmount - minAmount);
      const formattedAmount = randomAmount.toFixed(decimals);
      if (formattedAmount < amount) {
        return formattedAmount;
      }
    }

    return amount;
  }

  /**
   * 获取代币的合约地址
   * @param {string} token - 代币符号
   * @returns {string} 代币合约地址
   */
  #getTokenContractAddress(token) {
    const tokenConfig = tokens[token];
    if (!tokenConfig) {
      throw new Error(`未知代币: ${token}, 请检查配置文件`);
    }
    return tokenConfig.address;
  }


  /**
   * 授权代币
   * @param {*} token 代币符号
   * @param {*} amountWei 授权数量
   * @returns
   */
  async approveToken(token, amountWei) {
    try {
      const tokenContract = new ethers.Contract(
        tokens[token].address,
        [
          "function approve(address spender, uint256 amount) public returns (bool)",
          "function allowance(address owner, address spender) public view returns (uint256)"
        ],
        this.wallet
      );

      // 检查当前授权额度
      const currentAllowance = await tokenContract.allowance(
        this.wallet.address,
        AMBIENT_CONTRACT
      );

      if (currentAllowance >= amountWei) {
        return {
          success: true,
          code: 0,
          message: `[${this.index}] ${token} 授权成功`,
        };
      }

      // 准备授权交易
      const nonce = await this.wallet.provider.getTransactionCount(this.wallet.address);
      const gasParams = await retry(async () => {
        return await this.#getGasParams();
      }, 3, 2000);

      const tx = await tokenContract.approve(
        AMBIENT_CONTRACT,
        amountWei,
        {
          nonce,
          type: 2,
          chainId: 10143,
          ...gasParams,
        }
      );

      try {
        const receipt = await tx.wait();
        await sleep(getRandomValue(3, 10));
        if (receipt.status === 1) {
          logger.success(`[${this.index}] ${token} 授权成功! Hash: ${explorerUrl}${tx.hash}`);
          return {
            success: true,
            data: {
              txHash: tx.hash,
            },
            code: "SUCCESS",
            message: `[${this.index}] ${token} 授权成功!`,
          };
        } else {
          return {
            success: false,
            message: `[${this.index}] ${token} 授权失败`,
            code: "APPROVE_FAILED",
          };
        }
      } catch (error) {
        logger.error(`授权 ${token} 失败: ${error.message}`);
        return {
          success: false,
          message: `[${this.index}] ${token} 授权失败`,
          code: "APPROVE_FAILED",
        };
      }

    } catch (error) {
      logger.error(`授权 ${token} 失败: ${error.message}`);
      return {
        success: false,
        message: `[${this.index}] ${token} 授权失败`,
        code: "APPROVE_FAILED",
      };
    }
  }


  /**
   * 将代币换成 MON
   * @param {*} token 代币符号
   * @param {*} amountInSmallestUnit 代币数量
   * @returns
   */
  async swapToken2Mon(token, amountInSmallestUnit = 0) {
    try {
      if (!token || amountInSmallestUnit == 0) {
        throw new Error(`[${this.index}] 请提供需要的参数`);
      }

      const tokenBalance = await retry(async () => {
        return await this.getBalanceWithToken(token);
      });
      if (tokenBalance === 0) {
        throw new Error(`[${this.index}] ${token} 余额不足`);
      }

      // 授权代币
      const approveResult = await this.approveToken(token, amountInSmallestUnit);
      if (approveResult.success == false) {
        return approveResult;
      }

      // 生成并执行交换交易
      const tokenAddress = this.#getTokenContractAddress(token);
      if (!tokenAddress) {
        throw new Error(`${token} 合约地址不存在, 请检查配置文件`);
      }

      const tx = await retry(async () => await this.#ambientSwap({
        baseToken: ZERO_ADDRESS,
        quoteToken: tokenAddress,
        isNative: false,
        qty: amountInSmallestUnit,
      }), 3, 5000);

      if (tx.success == false) {
        return tx;
      }
      logger.success(`[${this.index}] 将 ${token} 换成 MON 成功. TX: ${explorerUrl}${tx.hash}`);
      return tx;
    } catch (error) {
      logger.error(`[${this.index}] 将 ${token} 换成 MON 失败: ${error.message}`);
      throw error;
    }
  }


  /**
   * 将 MON 换成其他代币
   */
  async swapMon2Token(token, amountWei = 0n) {
    try {

      if (amountWei == 0n || !token) {
        throw new Error(`[${this.index}] 请提供需要的参数`);
      }

      const amount = formatEther(amountWei);
      const tokenAddress = this.#getTokenContractAddress(token);
      if (!tokenAddress) {
        throw new Error(`${token} 合约地址不存在, 请检查配置文件`);
      }

      const tx = await retry(async () => await this.#ambientSwap({
        baseToken: ZERO_ADDRESS,
        quoteToken: tokenAddress,
        isNative: true, // 原生代币 -> token
        poolIdx: POOL_IDX,
        qty: amountWei,
        tip: TIP,
      }), 5, 2000);

      logger.success(`[${this.index}] 将 ${amount} MON 换成 ${token} 成功. TX: ${explorerUrl}${tx.hash}`);

      return {
        'success': true,
        'code': 0,
        'message': `[${this.index}] 将 ${amount} MON 换成 ${token} 成功`,
        'data': {
          'txHash': tx.hash,
        },
      };

    } catch (error) {
      this.logger.error(`将 MON 换成 ${token} 失败: ${error.message}`);
      throw error;
    }
  }


  async #taskSwapMon2Token() {
    const amountWei = await this.#getRandomMonWeiAmount();
    if (amountWei == 0n) {
      return {
        success: false,
        code: ERROR_CODES.INSUFFICIENT_FUNDS,
        message: `[${this.index}] 没有足够的MON`,
      };
    }

    const token = this.#getRandomToken();

    const balance = await this.getBalanceWithToken(token);
    logger.info(`[${this.index}] swap前 MON 余额 ${formatEther(await this.getNativeBalance())}, ${token} 余额 ${balance}, `);
    const result = await this.swapMon2Token(token, amountWei);
    if (result == null) {
      logger.warn(`[${this.index}] 交易失败`);
      return result;
    }

    await sleep(5);
    const balance2 = await this.getBalanceWithToken(token);
    await sleep(3);
    const monBalance = await this.getNativeBalance();
    logger.info(`[${this.index}] swap后 MON 余额 ${formatEther(monBalance)}, ${token} 余额 ${balance2}`);
    return result;
  }

  async #taskSwapToken2Mon() {
    // 随机获取一个有余额的代币
    const tokenInfo = await this.#getRandomTokenWithBalance();
    if (tokenInfo == null) {
      logger.warn(`[${this.index}] 没有可用的代币`);
      return {
        success: false,
        code: 1,
        message: `[${this.index}] 没有可用的代币`,
      };
    }

    const token = tokenInfo[0];
    const amount = tokenInfo[1];

    logger.info(`[${this.index}] swap前 ${token} 余额 ${amount}, mon 余额 ${formatEther(await this.getNativeBalance())}`);
    const result = await this.swapToken2Mon(token, this.convertDecimalToToken(token, amount));
    if (result.success == false) {
      logger.warn(`[${this.index}] 交易失败`);
      return result;
    }
    logger.info(`[${this.index}] swap后 ${token} 余额 ${await this.getBalanceWithToken(token)}, mon 余额 ${formatEther(await this.getNativeBalance())}`);

    return result;
  }


  /**
   * 执行代币兑换任务
   * @param {number} minTimes 最小交易次数
   * @param {number} maxTimes 最大交易次数
   */
  async taskSwap(minTimes = 1, maxTimes = 3) {

    logger.info(`[${this.index}] 开始执行 ${minTimes} - ${maxTimes} 次兑换任务`);
    const targetSuccessCount = Math.floor(Math.random() * (maxTimes - minTimes + 1)) + minTimes;
    logger.info(`[${this.index}] 目标执行 ${targetSuccessCount} 次兑换任务`); // 使用正确的变量名

    const MAX_ATTEMPTS = 20;
    let successCount = 0;
    let attempts = 0;

    while (successCount < targetSuccessCount && attempts < MAX_ATTEMPTS) {
      try {
        attempts++;
        const random = Math.random();

        const result = random < 0.7
          ? await this.#taskSwapMon2Token()
          : await this.#taskSwapToken2Mon();

        if (result.success == true) {
          successCount++;
          logger.info(`[${this.index}] 交易成功 ${successCount}/${targetSuccessCount}`);

          if (successCount >= targetSuccessCount) {
            break;
          }

          const randomValue = getRandomValue(5, 15);
          await sleep(randomValue);
        } else {
          if (result.code == ERROR_CODES.INSUFFICIENT_FUNDS) {
            logger.warn(`[${this.index}] 没有足够的MON`);
            break;
          }
          await sleep(5);
        }

      } catch (error) {
        logger.error(`[${this.index}] 交易失败: ${error.message}`);
        await sleep(5 * 1000);
      }

      const remainingAttempts = MAX_ATTEMPTS - attempts;
      if (remainingAttempts < 10) {
        logger.warn(`[${this.index}] 剩余尝试次数: ${remainingAttempts}`);
      }
    }

    if (successCount >= targetSuccessCount) {
      logger.info(`[${this.index}] 🎉 成功完成目标次数！(${successCount}/${targetSuccessCount}, 总尝试: ${attempts}次)`);
    } else {
      logger.warn(`[${this.index}] ⚠️ 成功 ${successCount}/${targetSuccessCount} 次`);
    }

    return {
      targetCount: targetSuccessCount,
      successCount: successCount,
      attempts: attempts
    };
  }


  /**
   * 随机获取一个有余额的代币
   * @returns {Promise<Array<[string, number]>>} 返回代币符号和余额的数组
   */
  async #getRandomTokenWithBalance() {
    // 随机打乱
    const shuffledTokens = Object.entries(tokens).sort(() => Math.random() - 0.5);
    try {
      // 检查其他代币
      for (const [token, tokenInfo] of shuffledTokens) {
        try {
          const tokenContract = new ethers.Contract(
            tokenInfo.address,
            ["function balanceOf(address) view returns (uint256)"],
            this.wallet
          );

          const decimalBalance = await retry(async () => {
            return await tokenContract.balanceOf(this.wallet.address);
          }, 3, 2000);

          if (decimalBalance > 0n) {
            // 定义一个极小值阈值
            const MINIMUM_BALANCE = 0.00000000001; // 1e-10
            const balance = this.convertTokenToDecimal(token, decimalBalance);
            if (balance > MINIMUM_BALANCE) {
              return [token, balance];
            }
          }

        } catch (error) {
          logger.error(`[${this.index}] 获取 ${token} 余额失败: ${error.message}`);
          continue;
        }
      }

      return null;

    } catch (error) {
      logger.error(`[${this.index}] 获取代币余额失败: ${error.message}`);
      throw error;
    }
  }


  /**
   * 获取所有非零余额代币列表，包括原生代币
   * @returns {Promise<Array<[string, number]>>} 返回代币符号和余额的数组
   */
  async #getTokensWithBalance() {
    const tokensWithBalance = [];

    try {
      // 检查原生代币余额
      const nativeBalance = await this.wallet.provider.getBalance(this.wallet.address);
      if (nativeBalance > 0n) {
        const nativeAmount = parseFloat(formatEther(nativeBalance));
        tokensWithBalance.push(["MON", nativeAmount]);
      }

      // 检查其他代币
      for (const [token, tokenInfo] of Object.entries(tokens)) {
        try {
          const tokenContract = new ethers.Contract(
            tokenInfo.address,
            ["function balanceOf(address) view returns (uint256)"],
            this.wallet
          );

          const balance = await retry(async () => {
            return await tokenContract.balanceOf(this.wallet.address);
          }, 3, 2000);

          if (balance > 0n) {
            const decimals = tokenInfo.decimals;
            const amount = parseFloat(ethers.formatUnits(balance, decimals));

            // 跳过低余额的 ETH 和 WETH
            if (
              (token.toLowerCase() === "eth" || token.toLowerCase() === "weth") &&
              amount < 0.001
            ) {
              continue;
            }

            tokensWithBalance.push([token, amount]);
          }

        } catch (error) {
          logger.error(`[${this.index}] 获取 ${token} 余额失败: ${error.message}`);
          continue;
        }
      }

      return tokensWithBalance;

    } catch (error) {
      logger.error(`[${this.index}] 获取代币余额失败: ${error.message}`);
      throw error;
    }
  }


  getSwapAmount(token, amount = 0) {
    const tokenConfig = config.tokenToMonConfig[token];
    if (!tokenConfig) {
      throw new Error(`未知代币: ${token}`);
    }

    const minAmount = tokenConfig.minAmount;
    const maxAmount = tokenConfig.maxAmount;
    const decimals = tokenConfig.decimals;

    for (let i = 0; i < 10; i++) {
      const randomAmount = minAmount + Math.random() * (maxAmount - minAmount);
      const formattedAmount = randomAmount.toFixed(decimals);
      if (formattedAmount < amount) {
        return formattedAmount;
      }
    }

    return 0n;
  }


  async #ambientSwap({
    baseToken,
    quoteToken,
    isNative = false,
    poolIdx = POOL_IDX,
    qty,
    tip = TIP,
  }) {
    try {

      // 准备 swap 参数
      const swapParams = {
        base: baseToken,                    // 基础代币地址
        quote: quoteToken,                  // 目标代币地址
        poolIdx: BigInt(poolIdx),           // 池子索引
        isBuy: isNative,                    // 是否为买入操作，当使用原生代币时为true
        inBaseQty: isNative,                // 是否以基础代币为单位, 与 isBuy 保持一致
        qty: qty,                           // 交易数量，需要是 BigInt 类型
        tip: BigInt(tip),                   // 小费，默认为 0，需要转为 BigInt
        limitPrice: isNative ? BigInt(MAX_SQRT_PRICE) : BigInt(MIN_SQRT_PRICE), // 限价
        minOut: BigInt(0),                  // 最小获得数量，滑点保护, 目前为 0
        reserveFlags: BigInt(RESERVE_FLAGS) // 保留标志位，通常为 0
      };


      const swapData = ethers.AbiCoder.defaultAbiCoder().encode(
        [
          "address",   // base
          "address",   // quote
          "uint256",   // poolIdx
          "bool",      // isBuy
          "bool",      // inBaseQty
          "uint128",   // qty
          "uint16",    // tip
          "uint128",   // limitPrice
          "uint128",   // minOut
          "uint8"      // reserveFlags
        ],
        [
          swapParams.base,
          swapParams.quote,
          swapParams.poolIdx,
          swapParams.isBuy,
          swapParams.inBaseQty,
          swapParams.qty,
          swapParams.tip,
          swapParams.limitPrice,
          swapParams.minOut,
          swapParams.reserveFlags
        ]
      );

      // 估算 gas
      const userCmdFunction = this.contract.getFunction("userCmd");
      const gasEstimate = await userCmdFunction.estimateGas(
        BigInt(1),
        swapData,
        {
          value: isNative ? qty.toString() : "0"
        }
      );

      // 获取当前 gas 价格
      const gasPrice = await this.wallet.provider.getFeeData();
      const maxFeePerGas = gasPrice.maxFeePerGas || gasPrice.gasPrice;

      // 计算总成本（gas费用 + 交易金额）
      const gasCost = gasEstimate * maxFeePerGas;
      const totalCost = isNative ? (gasCost + BigInt(qty)) : gasCost;

      // 检查钱包余额
      const monBalance = await this.getNativeBalance();
      if (monBalance <= totalCost) {
        logger.error(`[${this.index}] 余额不足，需要 ${ethers.formatEther(totalCost)} MON，当前余额 ${ethers.formatEther(monBalance)} MON`);
        return {
          success: false,
          code: ERROR_CODES.INSUFFICIENT_FUNDS,
          message: `[${this.index}] 余额不足，需要 ${ethers.formatEther(totalCost)} MON，当前余额 ${ethers.formatEther(monBalance)} MON`,
        };
      }

      // 添加余额缓冲检查
      const BUFFER_MULTIPLIER = BigInt(12) / BigInt(10); // 1.2倍缓冲
      if (monBalance <= totalCost * BUFFER_MULTIPLIER) {
        logger.warn(`[${this.index}] 余额偏低，建议充值。当前余额: ${ethers.formatEther(monBalance)} MON`);
      }

      const tx = await userCmdFunction(
        BigInt(1),              // callpath 转换为 BigInt
        swapData,
        {
          gasLimit: gasEstimate * BigInt(11) / BigInt(10),  // 使用 BigInt 计算
          value: isNative ? qty.toString() : "0"
        }
      );

      return tx;
    } catch (error) {
      logger.error(`[${this.index}] Ambient swap error:`, error);
      throw error;
    }
  }

}



