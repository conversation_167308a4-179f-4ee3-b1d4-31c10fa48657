import { filterWalletsByIndex, getRand<PERSON>Value, parseTOMLConfig, sleep } from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import Aicraft from "./aicraft/aicraft.js";
import Ambient from "./ambient/ambient.js";
import Aprio from "./aprio/aprio.js";
import Azaar from "./azaar/azaar.js";
import Caddy from "./caddy/caddy.js";
import BeanExchange from "./beanexchange/bean.js";
import { CommunityMember } from "./communitymember/communityMember.js";
import NadDomain from "./domain/nad_domain.js";
import Kintsu from "./kintsu/kintsu.js";
import KuruSwap from "./kuru/kuru.js";
import Mace2 from "./mace/mace2.js";
import Magma from "./magma/magma.js";
import { MintNFT } from "./mintnft/mintnft.js";
import Narwhal from "./narwhal/narwhal.js";
import SHMonad from "./shmonad/shmonad.js";
// import MonagayAnimalsTask from "./monagaynanimals/task.js";
import { ethers } from "ethers";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import BubbleFi from "./bubblefi/bubblefi.js";
import OctoSwap from "./octo/octoswap.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);
const proxy = config.PROXY_URL;
const monad_config = config.monad;
const rpc = monad_config.rpc;

const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;
const task_interval_min_seconds = config.TASK_INTERVAL_MIN_SECONDS || 10;
const task_interval_max_seconds = config.TASK_INTERVAL_MAX_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function createProvider(rpc, retries = 3, delay = 1000) {
  for (let i = 0; i < retries; i++) {
    try {
      const provider = new ethers.JsonRpcProvider(rpc);
      // 测试连接
      await provider.getNetwork();
      return provider;
    } catch (error) {
      logger.warn(`RPC连接失败，第 ${i + 1} 次重试...`);
      if (i === retries - 1) throw error;
      await sleep(delay);
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0]; // 获取钱包索引参数
  // const indexArg = "1"; // 获取钱包索引参数

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  // 定义可用的任务列表
  const tasks = [
    {
      name: "Mint NAD Domain",
      action: async (wallet) => {
        const domain = new NadDomain(wallet.private_key, wallet.proxy, rpc);
        const random = Math.random();
        await domain.register(random < 0.5);
      },
    },
    {
      name: "Community Member NFT Mint",
      action: async (wallet) => {
        const communityMember = new CommunityMember(rpc);
        await communityMember.mint(wallet.private_key, wallet.index);
      },
    },
    {
      name: "KURU Swap",
      action: async (wallet) => {
        const kuru = new KuruSwap(wallet.private_key, wallet.proxy, rpc);
        await kuru.executeSwapRandom();
      },
    },
    {
      name: "Ario Stake Task",
      action: async (wallet) => {
        const aprio = new Aprio(wallet.private_key, rpc);

        // 1. 请求取消质押
        const unstakeResult = await aprio.requestUnstake();
        if (unstakeResult.code === "UNSTAKE_SUCCESS") {
          logger.info(`[${wallet.index}] 成功在 Ario 上解质押`);
          await sleep(getRandomValue(5, 10));
        }

        // 2. 领取质押奖励
        const claimResult = await aprio.claimAllRequests();
        if (claimResult.code === "CLAIM_SUCCESS") {
          logger.info(`[${wallet.index}] 成功领取 Ario 质押奖励`);
          await sleep(getRandomValue(5, 10));
        }

        // 3. 检查质押数量
        const skipIfStaked = Math.random() < 0.5;
        const stakeResult = await aprio.stake(skipIfStaked);
        if (stakeResult.code === "STAKE_SUCCESS") {
          logger.info(`[${wallet.index}] 成功在 Ario 上质押`);
        } else {
          logger.error(`[${wallet.index}] 在 Ario 上质押失败, ${stakeResult.message}`);
        }

        // 4. 解质押
        if (Math.random() < 0.5) {
          // 等待 8-25 秒
          if (stakeResult.code === "STAKE_SUCCESS") {
            await sleep(getRandomValue(8, 25));
          }

          const unstakeResult = await aprio.requestUnstake();
          if (unstakeResult.code === "UNSTAKE_SUCCESS") {
            logger.info(`[${wallet.index}] 成功在 Ario 上解质押`);
            await sleep(getRandomValue(8, 25));
          } else {
            logger.error(`[${wallet.index}] 在 Ario 上解质押失败, ${unstakeResult.message}`);
          }

          const claimResult = await aprio.claimAllRequests();
          if (claimResult.code === "CLAIM_SUCCESS") {
            logger.info(`[${wallet.index}] 成功领取 Ario 质押奖励`);
          } else {
            logger.error(`[${wallet.index}] 领取 Ario 质押奖励失败, ${claimResult.message}`);
          }
        }
      },
    },
    {
      name: "shmonad Stake Task",
      action: async (wallet) => {
        const shmonad = new SHMonad(wallet.private_key, wallet.proxy, rpc);

        // 1. 解除绑定
        await shmonad.unbond();

        sleep(getRandomValue(5, 10));

        // 2. 解除质押
        await shmonad.redeem();

        sleep(getRandomValue(3, 6));

        // 3. 质押
        const stakeResult = await shmonad.stake();
        if (stakeResult.success) {
          stakeSuccessCount++;
          sleep(getRandomValue(5, 10));
        } else {
          stakeFailedCount++;
        }

        // 4. 绑定
        const bondResult = await shmonad.bond();
        if (bondResult.success) {
          bondSuccessCount++;
          sleep(getRandomValue(5, 10));
        } else {
          bondFailedCount++;
        }

        // 70% 概率解除质押
        const skipIfStaked = Math.random() < 0.7;
        if (skipIfStaked) {
          await shmonad.unbond();
          sleep(getRandomValue(5, 10));
          await shmonad.redeem();
        }
      }
    },
    {
      name: "Kintsu Stake And Unstake",
      action: async (wallet) => {
        const kintsu = new Kintsu(wallet.private_key, wallet.proxy, rpc, wallet.index);
        const stakeResult = await kintsu.stakeMon();
        if (stakeResult) {
          logger.info(`[${wallet.index}] 成功在 Kintsu 上质押`);
        } else {
          logger.error(`[${wallet.index}] 在 Kintsu 上质押失败`);
        }

        // 80% 概率解质押
        if (Math.random() < 0.8) {
          // 等待 8-25 秒
          await sleep(getRandomValue(8, 25));
          const unstakeResult = await kintsu.requestUnstake();
          if (unstakeResult.status === 1) {
            logger.info(`[${wallet.index}] 成功在 Kintsu 上解质押`);
          } else {
            logger.error(`[${wallet.index}] 在 Kintsu 上解质押失败`);
          }
        }
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT Proof of Sparkballs",
      action: async (wallet) => {
        const amount = 1;
        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const amountHex = amount.toString(16).padStart(64, "0");
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000${amountHex}00000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "Proof of Sparkballs",
          amount: amount,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index, amount);
      },
    },
    {
      // https://the-pipeline.testnet.nfts2.me/
      name: "Mint NFT The Pipeline",
      action: async (wallet) => {
        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0xb510391f000000000000000000000000039c4338a14c2d46c2b13d319161f4e22b9980ee00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000044449a52f8000000000000000000000000${cleanAddress}000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0.00000000666",
          name: "The Pipeline",
          amount: 1,
          contractType: "ERC721",
          tokenAddress: "******************************************",
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      name: "Bean Exchange Task",
      action: async (wallet) => {
        const beanExchange = new BeanExchange(rpc);
        const tokens = ["USDC", "BEAN", "JAI"];
        const getRandomToken = () => tokens[Math.floor(Math.random() * tokens.length)];
        const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

        // 生成随机任务列表
        const tasks = [];

        // 添加2-4次SWAP任务
        const swapCount = getRandomInt(1, 3);
        for (let i = 0; i < swapCount; i++) {
          // 随机选择是MON换代币还是代币换MON
          if (Math.random() < 0.5) {
            tasks.push({
              name: "monToToken",
              token: getRandomToken(),
              action: (token) => beanExchange.swapMONToToken(wallet.private_key, token),
            });
          } else {
            tasks.push({
              name: "tokenToMon",
              token: getRandomToken(),
              action: (token) => beanExchange.swapTokenToMON(wallet.private_key, token),
            });
          }
        }

        // 添加1-2次LP任务
        const lpCount = getRandomInt(1, 2);
        for (let i = 0; i < lpCount; i++) {
          tasks.push({
            name: "addLiquidity",
            token: getRandomToken(),
            action: (token) => beanExchange.addLiquidityWithMON(wallet.private_key, token),
          });
        }

        // 打乱任务顺序
        const shuffledTasks = tasks.sort(() => Math.random() - 0.5);

        // 执行所有任务
        for (const task of shuffledTasks) {
          logger.info(`执行Bean Exchange任务: ${task.name} ${task.token}`);
          await task.action(task.token);

          // 任务之间添加延迟
          if (task !== shuffledTasks[shuffledTasks.length - 1]) {
            const delay = getRandomValue(task_interval_min_seconds, task_interval_max_seconds);
            logger.info(`等待 ${delay} 秒后执行下一个Bean Exchange操作...`);
            await sleep(delay);
          }
        }
      },
    },
    {
      // https://lilchogstars.com/
      name: "Mint NFT OPEN EDITION CHOGSTAR",
      action: async (wallet) => {
        const inputData = "0xa0712d680000000000000000000000000000000000000000000000000000000000000001";
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "OPEN EDITION CHOGSTAR",
          amount: 1,
          contractType: "ERC721",
          inputData: inputData
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      name: "Magma Stake And Unstake Task",
      action: async (wallet) => {

        let private_key = wallet.private_key;
        const isEncrypted = await secureEncryption.isEncrypted(private_key);
        if (isEncrypted) {
          private_key = await secureEncryption.decrypt(private_key);
          wallet.private_key = private_key;
        }

        if (!private_key || private_key.trim() === "") {
          logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
          return;
        }

        const magma = new Magma(private_key, wallet.proxy, rpc);

        // 1. 请求取消质押
        const unstakeResult = await magma.requestUnstake();
        if (unstakeResult.success) {
          logger.info(`[${wallet.index}] ${unstakeResult.message}`);
          if (unstakeResult.code === "ALREADY_STAKE") {
            const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
            logger.info(`[${wallet.index}] 等待 ${delay} 秒后继续`);
            await sleep(delay);
          }
        } else {
          logger.error(`[${wallet.index}] 在 Magma 上解质押失败, ${unstakeResult.message}`);
        }

        // 2. 质押
        const stakeResult = await magma.stakeMon();
        if (stakeResult.success) {
          logger.info(`[${wallet.index}] ${stakeResult.message}`);
        } else {
          logger.error(`[${wallet.index}] 在 Magma 上质押失败, ${stakeResult.message}`);
        }

        // 3. 解质押
        const isUnstake = Math.random() < 0.5;
        if (isUnstake) {
          // 等待 8-25 秒
          if (stakeResult.success && stakeResult.code === "STAKE_SUCCESS") {
            const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
            logger.info(`[${wallet.index}] 等待 ${delay} 秒后解质押`);
            await sleep(delay);
          }
          const unstakeResult = await magma.requestUnstake(true);
          if (unstakeResult.success) {
            logger.info(`[${wallet.index}] ${unstakeResult.message}`);
          } else {
            logger.error(`[${wallet.index}] 在 Magma 上解质押失败, ${unstakeResult.message}`);
          }
        }
      }
    },
    {
      name: "Ambient Swap Task",
      action: async (wallet) => {
        const ambient = new Ambient(wallet.index, wallet.private_key, rpc, wallet.proxy);
        await ambient.taskSwap();
      },
    },
    // {
    //   name: "Mace Swap Task",
    //   action: async (wallet) => {
    //     const mace = new Mace(rpc);

    //     // 随机选择1-3个代币进行swap
    //     const swapCount = getRandomValue(1, 3);
    //     logger.info(`[${wallet.index}] 将执行 ${swapCount} 次Swap`);

    //     for (let i = 0; i < swapCount; i++) {
    //       // 随机选择一个代币
    //       const token = mace.getRandomToken();
    //       logger.info(`[${wallet.index}] 执行第 ${i + 1} 次Swap: ${token.name}`);

    //       const result = await mace.executeSwap(wallet.private_key, token.address);
    //       if (result) {
    //         logger.success(`[${wallet.index}] Swap ${token.name} 成功`);
    //       } else {
    //         logger.error(`[${wallet.index}] Swap ${token.name} 失败`);
    //         return;
    //       }

    //       // 如果不是最后一次swap，等待30-70秒
    //       if (i < swapCount - 1) {
    //         const delay = getRandomValue(30, 70);
    //         logger.info(`[${wallet.index}] 等待 ${delay} 秒后继续下一次Swap...`);
    //         await sleep(delay);
    //       }
    //     }
    //   },
    // },
    {
      name: "Narwhal Games",
      action: async (wallet) => {
        const narwhal = new Narwhal(rpc);

        // 1. 检查并执行mint
        const balance = await narwhal.getMockUSDTBalance(wallet.address);
        const balanceInToken = Number(ethers.formatEther(balance));

        if (balanceInToken < 1000) {
          logger.info(`[${wallet.index}] 当前MockUSDT余额: ${balanceInToken.toFixed(2)}, 尝试mint`);
          const mintResult = await narwhal.mint(wallet.private_key);
          if (!mintResult.success) {
            logger.error(`[${wallet.index}] Mint失败: ${mintResult.message}`);
            return;
          }
        }

        // 2. 随机选择1-3个游戏
        const gameTypes = ['dice', 'slots', 'rock', 'coinFlip'];
        const numGames = getRandomValue(1, 3);
        const selectedGames = [...gameTypes]
          .sort(() => Math.random() - 0.5)
          .slice(0, numGames);

        logger.info(`[${wallet.index}] 将玩 ${selectedGames.length} 个游戏: ${selectedGames.join(', ')}`);

        // 3. 执行每个游戏
        for (const gameType of selectedGames) {
          const playTimes = getRandomValue(2, 4);

          for (let i = 1; i <= playTimes; i++) {
            if (i > 1) {
              const delay = getRandomValue(10, 20);
              logger.info(`[${wallet.index}] 等待 ${delay} 秒后继续...`);
              await sleep(delay);
            }

            const result = await narwhal[gameType](wallet.private_key);
            if (!result.success) {
              logger.error(`[${wallet.index}] ${gameType}游戏失败: ${result.message}`);
              return;
            }
          }

          if (gameType !== selectedGames[selectedGames.length - 1]) {
            const delay = getRandomValue(20, 40);
            logger.info(`[${wallet.index}] 等待 ${delay} 秒后开始下一个游戏...`);
            await sleep(delay);
          }
        }
      },
    },
    // {
    //   name: "Monagay Animals Game",
    //   action: async (wallet) => {
    //     const monagayAnimalsTask = new MonagayAnimalsTask(rpc);
    //     await monagayAnimalsTask.handleWallet(wallet);
    //   },
    // },
    {
      name: "Mace Swap Task",
      action: async (wallet) => {
        const mace = new Mace2(rpc);
        await mace.task(wallet);
      },
    },
    {
      name: "aicraft.fun",
      action: async (wallet) => {
        const aicraft = new Aicraft(wallet.private_key, wallet.index, rpc, proxy);
        await aicraft.task();
      },
    },
    // {
    //   // https://testnet.freee.xyz/prime/monadt/AuroraHare
    //   name: "Mint NFT Aurora Hare",
    //   action: async (wallet) => {
    //     const inputData = "0xefef39a10000000000000000000000000000000000000000000000000000000000000001";
    //     const nftConfig = {
    //       rpc: rpc,
    //       contractAddress: "******************************************",
    //       mintPrice: "0.005",
    //       name: "Aurora Hare",
    //       amount: 1,
    //       contractType: "ERC721",
    //       inputData: inputData
    //     };
    //     const nft = new MintNFT(nftConfig);
    //     await nft.mint(wallet.private_key, wallet.index);
    //   },
    // },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT OGM",
      action: async (wallet) => {
        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "OGM",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT Mint Me",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "Mint Me",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT Orgone Omniflow",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "Orgone Omniflow",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT JMNA",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "JMNA",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT lleonard",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "lleonard",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT MONAD GIRL",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "MONAD GIRL",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT MON6",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "MON6",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT PEPE KING",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "PEPE KING",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT The Bitcoin DOG on Monad",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "The Bitcoin DOG on Monad",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT MON FROG",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "MON FROG",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT VHS",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "VHS",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT MonadPunk",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "MonadPunk",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT art",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "art",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT PUPUEE",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "PUPUEE",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://magiceden.io/mint-terminal/monad-testnet/******************************************
      name: "Mint NFT Trump",
      action: async (wallet) => {

        const cleanAddress = wallet.address.slice(2).toLowerCase();
        const inputData = `0x9b4f3af5000000000000000000000000${cleanAddress}0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000`;
        const nftConfig = {
          rpc: rpc,
          contractAddress: "******************************************",
          mintPrice: "0",
          name: "Trump",
          amount: 1,
          contractType: "ERC1155",
          tokenId: 0,
          inputData: inputData,
        };
        const nft = new MintNFT(nftConfig);
        await nft.mint(wallet.private_key, wallet.index);
      },
    },
    {
      // https://app-monad-testnet.azaar.com/mint?chainId=10143
      name: "Azaar",
      action: async (wallet) => {
        const azaar = new Azaar(wallet.index, wallet.private_key, rpc);
        await azaar.mintAll();
      },
    },
    {
      // https://alpha.caddy.finance/
      name: "Caddy",
      action: async (wallet) => {
        const caddy = new Caddy(wallet.private_key, wallet.index, rpc, proxy);
        await caddy.execute();
      },
    },
    {
      // https://alpha.caddy.finance/
      name: "Bubblefi",
      action: async (wallet) => {
        const bubblefi = new BubbleFi(wallet.private_key, wallet.index, rpc, proxy);
        await bubblefi.executeTask();
      },
    },
    {
      // https://octo.exchange/
      name: "OctoSwap",
      action: async (wallet) => {
        const octoSwap = new OctoSwap(wallet.private_key, wallet.index, rpc, proxy);
        await octoSwap.execute();
      },
    },
  ];

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }
    const index = wallet.index;
    const address = wallet.address;

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    try {
      const provider = await createProvider(rpc);
      const _wallet = new ethers.Wallet(private_key, provider);

      // 判断余额是否小于0.05
      const balance = await provider.getBalance(_wallet.address);
      const balanceInToken = Number(ethers.formatEther(balance));
      logger.info(`钱包序号 ${index} 的余额: ${balanceInToken.toFixed(2)}`);
      if (balanceInToken < 0.05) {
        logger.warn(`钱包序号 ${index} 的余额小于0.05，跳过处理`);
        continue;
      }

      const startTime = Date.now();
      logger.info(`开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: ${index} ${address}`);

      const shuffledTasks = [...tasks].sort(() => Math.random() - 0.5);

      // 生成一个随机数，表示要执行的任务数量
      const minTasks = Math.max(1, Math.floor(tasks.length / 4)); // 确保最少执行1个任务
      const maxTasksToExecute = getRandomValue(minTasks, Math.floor(tasks.length / 3)); // 随机选择1到总任务数之间的数量

      logger.info(`将执行 ${maxTasksToExecute} 个任务`);

      // 随机选择要执行的任务
      const selectedTasks = shuffledTasks
        .map((task, index) => ({ task, index }))
        .sort(() => Math.random() - 0.5)
        .slice(0, maxTasksToExecute);

      // 执行选中的任务
      for (const { task } of selectedTasks) {
        try {
          logger.info(`执行任务: ${task.name}`);
          await task.action(wallet);
        } catch (error) {
          logger.error(`执行任务 ${task.name} 失败:`, error);
        }
        const delay = getRandomValue(task_interval_min_seconds, task_interval_max_seconds);
        logger.info(`钱包 ${index} 等待 ${delay} 秒后继续下一个任务`);
        await sleep(delay);
      }

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      logger.info(`任务完成: ${index} ${address}, 耗时 ${duration} 秒`);

      // 如果是最后一个任务，则不延迟
      if (j === shuffledWallets.length - 1) {
        break;
      }

      // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
      const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
      logger.info(`等待 ${delay} 秒后继续下一个钱包`);
      await sleep(delay);
    } catch (error) {
      logger.error(`处理钱包 ${index} 时发生错误:`, error);
      // 如果是网络错误，等待较长时间后继续
      if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
        const recoveryDelay = 30; // 30秒恢复时间
        logger.info(`网络错误，等待 ${recoveryDelay} 秒后继续...`);
        await sleep(recoveryDelay * 1000);
      }
      continue; // 跳过当前钱包，继续处理下一个
    }
  }
}

// 执行任务
// node example/monad/task.js 1-10
main();
