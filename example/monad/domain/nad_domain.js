import { ethers, parseEther, formatEther } from "ethers";
import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";
import fakeUa from "fake-useragent";

import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";
import { getRandomDomain } from "../../../base/tools/domain.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { parseTOMLConfig } from "../../../base/tools/common.js";
import { el } from "@faker-js/faker";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad || {};
const rpc = monadConfig.rpc || "https://testnet-rpc.monad.xyz/";
const defaultProxy = config.PROXY_URL || "";

export default class NadDomain {
  /**
   * 创建 NAD 域名管理实例
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpc - RPC 节点地址
   */
  constructor(privateKey, proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.proxy = proxy || defaultProxy;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.logger = logger;

    // 合约初始化
    this.domain_contract = new ethers.Contract(
      "******************************************",
      [
        {
          inputs: [
            {
              components: [
                { internalType: "string", name: "name", type: "string" },
                { internalType: "address", name: "nameOwner", type: "address" },
                { internalType: "bool", name: "setAsPrimaryName", type: "bool" },
                { internalType: "address", name: "referrer", type: "address" },
                { internalType: "bytes32", name: "discountKey", type: "bytes32" },
                { internalType: "bytes", name: "discountClaimProof", type: "bytes" },
                { internalType: "uint256", name: "nonce", type: "uint256" },
                { internalType: "uint256", name: "deadline", type: "uint256" },
              ],
              internalType: "struct NNSRegistrarController.RegisterData",
              name: "params",
              type: "tuple",
            },
            { internalType: "bytes", name: "signature", type: "bytes" },
          ],
          name: "registerWithSignature",
          outputs: [],
          stateMutability: "payable",
          type: "function",
        },
      ],
      this.wallet.wallet,
    );

    this.check_contract = new ethers.Contract(
      "******************************************",
      [
        {
          inputs: [{ internalType: "address", name: "addr", type: "address" }],
          name: "getPrimaryNameForAddress",
          outputs: [{ internalType: "string", name: "", type: "string" }],
          stateMutability: "view",
          type: "function",
        },
        {
          inputs: [{ internalType: "string", name: "name", type: "string" }],
          name: "isNameAvailable",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          stateMutability: "view",
          type: "function",
        },
        {
          inputs: [
            {
              internalType: "address",
              name: "addr",
              type: "address",
            },
          ],
          name: "getNamesOfAddress",
          outputs: [
            {
              internalType: "string[]",
              name: "",
              type: "string[]",
            },
          ],
          stateMutability: "view",
          type: "function",
        },
      ],
      this.wallet.wallet,
    );
  }

  /**
   * 检查域名是否可以注册
   * @param {string} name - 要检查的域名名称（不包含 .nad 后缀）
   * @returns {Promise<boolean>} - true: 可以注册, false: 已被注册
   */
  async isNameAvailable(name) {
    try {
      // 1. 基本验证
      if (!name) throw new Error("域名不能为空");
      if (name.length < 2) throw new Error("域名长度必须大于等于2");

      // 2. 字符验证（只允许小写字母、数字和连字符）
      const validNameRegex = /^[a-z0-9-]+$/;
      if (!validNameRegex.test(name)) {
        throw new Error("域名只能包含小写字母、数字和连字符");
      }

      // 3. 调用合约检查可用性
      const available = await this.check_contract.isNameAvailable(name);
      return available;
    } catch (error) {
      this.logger.error(`检查域名可用性失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 计算域名注册费用 域名长度 3位0.1 4位0.05 5位0.02
   * @param {string} name - 域名名称
   * @returns {bigint} - 注册费用
   */
  calculateRegistrationFee(name) {
    if (!name || typeof name !== "string") {
      throw new Error("无效的域名");
    }

    const length = name.length;
    let fee;
    switch (length) {
      case 3:
        fee = "0.1"; // 3位域名 0.1 MON
        break;
      case 4:
        fee = "0.05"; // 4位域名 0.05 MON
        break;
      default:
        fee = "0.02"; // 5位及以上 0.02 MON
        break;
    }

    return parseEther(fee);
  }

  /**
   * 获取钱包地址的域名
   * @returns {Promise<string[]>} - 域名列表
   */
  async getNamesOfAddress() {
    try {
      const names = await this.check_contract.getNamesOfAddress(this.wallet.getAddress());
      return Array.from(names);
    } catch (error) {
      this.logger.error(`获取域名失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取可用的域名
   * @param {string} [baseName] - 基础域名名称，如果不提供则随机生成
   * @param {number} [maxAttempts=10] - 最大尝试次数
   * @returns {Promise<{name: string, isAvailable: boolean}>} - 可用的域名
   */
  async getAvailableName(baseName, maxAttempts = 10) {
    try {
      let name = baseName || getRandomDomain();
      let isAvailable = await this.isNameAvailable(name);
      this.logger.info(`准备注册域名: ${name}.nad`);
      if (isAvailable) {
        this.logger.success(`域名可用|${name}.nad`);
        return { name, isAvailable: true };
      }

      this.logger.info(`域名已经被注册|${name}.nad`);

      // 尝试添加随机数
      for (let i = 0; i < maxAttempts; i++) {
        // 保留原始名称，添加随机数
        const newName = `${name}${i}`;
        isAvailable = await this.isNameAvailable(newName);

        if (isAvailable) {
          this.logger.success(`域名可用|${newName}.nad`);
          return { name: newName, isAvailable: true };
        }

        this.logger.info(`域名已经被注册|${newName}.nad`);
      }

      return { name, isAvailable: false };
    } catch (error) {
      this.logger.error(`获取可用域名失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取注册签名
   * @param {string} domainName - 域名名称
   * @returns {Promise<{success: boolean, signature?: string, nonce?: string, deadline?: string, message?: string}>}
   */
  async getSignature(domainName) {
    try {
      if (!domainName) {
        throw new Error("域名不能为空");
      }

      const name = domainName;
      const nameOwner = this.wallet.getAddress();
      const setAsPrimaryName = true;
      const referrer = ethers.ZeroAddress;
      const discountKey = ethers.ZeroHash;
      const discountClaimProof = ethers.ZeroHash;

      const queryParams = new URLSearchParams({
        name,
        nameOwner,
        setAsPrimaryName,
        referrer,
        discountKey,
        discountClaimProof,
        nonce: 0,
        deadline: 0,
        chainId: 10143,
      }).toString();

      let proxyAgent = null;
      if (this.proxy) {
        proxyAgent = new HttpsProxyAgent(this.proxy);
      }

      const config = {
        headers: {
          "Content-Type": "application/json",
          origin: "https://app.nad.domains",
          referer: "https://app.nad.domains/",
          "User-Agent": fakeUa(),
        },
        timeout: 30000,
      };

      // 只有在代理存在时才添加代理
      if (proxyAgent) {
        config.httpsAgent = proxyAgent;
      }

      const response = await axios.get(
        `https://api.nad.domains/register/signature?${queryParams}`,
        config,
      );

      // 验证返回数据
      const { code, success, message, signature, nonce, deadline } = response.data;

      if (!success || code !== 0) {
        this.logger.error(`获取签名失败: ${message}`);
        return { success: false, message };
      }

      this.logger.success(`签名获取成功: ${signature.substring(0, 20)}...`);

      return {
        success: true,
        signature,
        nonce,
        deadline,
      };
    } catch (error) {
      this.logger.error(`签名获取失败: ${error.message}`);
      return { success: false, message: error.message };
    }
  }

  /**
   * 注册域名
   * @param {string} [domainName] - 域名名称，如果不提供则自动获取可用域名
   * @returns {Promise<{success: boolean, hash?: string, name?: string, message?: string, code?: string}>}
   */
  async register(skipIfRegistered = false, domainName) {
    let txHash = null;

    try {
      const address = this.wallet.getAddress();

      // 检查是否已有主域名
      if (!skipIfRegistered) {
        try {
          const has_primary = await this.check_contract.getPrimaryNameForAddress(address);
          if (has_primary) {
            this.logger.success(`已经注册过域名 | ${has_primary}.nad`);
            return {
              success: true,
              code: "ALREADY_REGISTERED",
              message: `已经注册过域名: ${has_primary}.nad`,
            };
          }
        } catch (error) {
          // 如果查询失败，继续注册流程
          this.logger.warn(`查询主域名失败: ${error.message}`);
        }
      } else {
        this.logger.info(`跳过已注册域名检查`);
        const names = await this.getNamesOfAddress();
        if (names.length >= 3) {
          this.logger.info(`已经注册过3个域名，跳过注册`);
          return {
            success: true,
            code: "ALREADY_REGISTERED",
          };
        }
      }

      // 获取可用的域名
      let finalName = domainName;
      if (!finalName) {
        const { name, isAvailable } = await this.getAvailableName();
        if (!isAvailable) {
          return {
            success: false,
            code: "NO_AVAILABLE_NAME",
            message: "无法找到可用的域名",
          };
        }
        finalName = name;
      } else {
        // 验证指定域名是否可用
        const isAvailable = await this.isNameAvailable(finalName);
        if (!isAvailable) {
          return {
            success: false,
            code: "NAME_UNAVAILABLE",
            message: `域名 ${finalName}.nad 已被注册`,
          };
        }
      }

      // 判断余额是否足够
      const balance = await this.wallet.getBalance();
      const registrationFee = this.calculateRegistrationFee(finalName);

      if (balance < registrationFee) {
        const formattedBalance = formatEther(balance);
        const formattedFee = formatEther(registrationFee);
        const missing = formatEther(registrationFee - balance);

        this.logger.error(
          `余额不足支付注册费用 | 当前余额: ${formattedBalance} MON | 需要: ${formattedFee} MON`,
        );

        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: `余额不足支付注册费用`,
          data: {
            balance: formattedBalance,
            required: formattedFee,
            missing: missing,
          },
        };
      }

      // 获取签名
      const signatureResult = await this.getSignature(finalName);
      if (!signatureResult.success || !signatureResult.signature) {
        return {
          success: false,
          code: "SIGNATURE_FAILED",
          message: signatureResult.message || "获取注册域名签名失败",
        };
      }

      const { signature, nonce, deadline } = signatureResult;

      // 注册域名
      this.logger.info(`开始注册域名 ${finalName}.nad | 费用: ${formatEther(registrationFee)} MON`);

      const args = {
        name: finalName,
        nameOwner: address,
        setAsPrimaryName: true,
        referrer: ethers.ZeroAddress,
        discountKey: ethers.ZeroHash,
        discountClaimProof: ethers.ZeroHash,
        nonce: nonce,
        deadline: deadline,
      };

      const tx = await this.domain_contract.registerWithSignature(args, signature, {
        value: registrationFee,
      });

      txHash = tx.hash;
      this.logger.info(`交易已发送 | Hash: ${txHash}`);

      const receipt = await tx.wait();
      if (receipt.status !== 1) {
        throw new Error(`交易失败 | Hash: ${txHash} | Status: ${receipt.status}`);
      }

      this.logger.success(`域名注册成功 | ${finalName}.nad | Hash: ${txHash}`);

      return {
        success: true,
        hash: txHash,
        name: finalName,
        blockNumber: receipt.blockNumber,
      };
    } catch (error) {
      // 处理特定错误
      if (error.message.includes("insufficient balance")) {
        this.logger.error(`余额不足: ${error.message}`);
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: `余额不足`,
        };
      }

      this.logger.error(`交易失败: ${error.message}`);
      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
        hash: txHash,
      };
    }
  }

  /**
   * 批量注册域名
   * @param {Array<string>} names - 要注册的域名列表
   * @param {Object} options - 选项
   * @param {boolean} options.stopOnError - 遇到错误时是否停止
   * @param {number} options.delay - 注册间隔时间(毫秒)
   * @returns {Promise<Array<Object>>} - 注册结果列表
   */
  async batchRegister(names, options = {}) {
    const { stopOnError = false, delay = 2000 } = options;
    const results = [];

    for (const name of names) {
      try {
        // 添加延迟
        if (results.length > 0 && delay > 0) {
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        // 注册域名
        const result = await this.register(name);
        results.push({ ...result, name });

        // 如果注册失败且设置了停止标志，则中断
        if (!result.success && stopOnError) {
          this.logger.warn(`批量注册中断，因为 ${name}.nad 注册失败`);
          break;
        }
      } catch (error) {
        const result = {
          success: false,
          name,
          code: "UNEXPECTED_ERROR",
          message: error.message || "未知错误",
        };

        results.push(result);

        if (stopOnError) {
          this.logger.warn(`域名注册中断，因为 ${name}.nad 注册失败`);
          break;
        }
      }
    }

    // 统计结果
    const successful = results.filter((r) => r.success).length;
    const failed = results.length - successful;

    this.logger.info(`域名注册完成 | 成功: ${successful} | 失败: ${failed}`);

    return results;
  }
}
