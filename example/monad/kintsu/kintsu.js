import { ethers, formatEther, parseEther } from "ethers";

import { Wallet } from "../../../base/evm/wallet.js";
import { getRandomValue, parseTOMLConfig, retry, sleep } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";
import { resolveFromModule } from "../../../base/tools/path.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad || {};
const rpc = monadConfig.rpc || "https://testnet-rpc.monad.xyz/";
const explorerUrl = monadConfig.explorer_url || "https://testnet.monadexplorer.com/tx/";

const STAKE_ADDRESS = "******************************************";
const STAKE_ABI = [
  {
    type: "function",
    name: "stake",
    inputs: [],
    outputs: [{ name: "newShares", type: "uint128", internalType: "uint128" }],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "requestUnlock",
    inputs: [{ name: "shares", type: "uint128", internalType: "uint128" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "balanceOf",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
];

export default class Kintsu {
  /**
   * 创建 Kintsu 实例
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpc - RPC 节点地址
   * @param {number} accountIndex - 账户索引
   */
  constructor(privateKey, proxy = "", rpcUrl = rpc, accountIndex = 0) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.accountIndex = accountIndex;
    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.logger = logger;
    this.config = config;

    // 创建合约实例
    this.contract = new ethers.Contract(STAKE_ADDRESS, STAKE_ABI, this.wallet.wallet);
  }

  /**
   * 获取当前网络的 gas 参数
   * @returns {Promise<{maxFeePerGas: bigint, maxPriorityFeePerGas: bigint}>}
   */
  async getGasParams() {
    try {
      // 获取最新区块
      const latestBlock = await this.wallet.provider.getBlock("latest");
      const baseFee = latestBlock.baseFeePerGas;

      // 获取最大优先费用
      const maxPriorityFee = await this.wallet.provider.send("eth_maxPriorityFeePerGas", []);
      const maxPriorityFeePerGas = BigInt(maxPriorityFee);

      // 计算最大费用 (基础费用 + 优先费用)
      const maxFeePerGas = baseFee + maxPriorityFeePerGas;

      return {
        maxFeePerGas,
        maxPriorityFeePerGas,
      };
    } catch (error) {
      this.logger.error(`[${this.accountIndex}] 获取 gas 参数失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 估算交易 gas
   * @param {Object} transaction - 交易对象
   * @returns {Promise<bigint>} - 估算的 gas 限制
   */
  async estimateGas(transaction) {
    try {
      const estimated = await this.wallet.provider.estimateGas(transaction);
      return BigInt(Math.ceil(Number(estimated) * 1.0));
    } catch (error) {
      this.logger.warn(`[${this.accountIndex}] 估算 gas 失败: ${error.message}. 使用默认 gas 限制`);
      throw error;
    }
  }

  /**
   * 获取质押金额
   * @param {number} maxAmount - 最大质押金额
   * @returns {number}
   */
  getStakeAmount(maxAmount = 0.01) {
    const amounts = [
      ...Array(9)
        .fill()
        .map((_, i) => +(0.01 * (i + 1)).toFixed(2)),
      0.1,
    ];

    // 获取小于等于输入金额的值
    const filteredAmounts = amounts.filter((a) => a <= maxAmount);

    // 如果没有找到合适的金额，返回最小值
    if (filteredAmounts.length === 0) {
      return amounts[0].toString();
    }

    // 随机返回一个符合条件的金额
    const selectedAmount = filteredAmounts[Math.floor(Math.random() * filteredAmounts.length)];

    // 返回字符串格式，方便后续转换为 BigInt
    return selectedAmount.toString();
  }

  /**
   * 质押 MON 代币
   * @returns {Promise<boolean>} - 质押是否成功
   */
  async stakeMon() {
    for (let i = 0; i < 3; i++) {
      try {
        // 获取随机质押金额
        const balance = await this.wallet.getBalance();
        const randomAmount = this.getStakeAmount(ethers.formatEther(balance) / 2);
        this.logger.info(`[${this.accountIndex}] 在 Kintsu 上质押 ${randomAmount} MON`);

        // 将金额转换为 wei
        const amountWei = parseEther(randomAmount);

        // 获取 gas 参数
        const gasParams = await retry(async () => await this.getGasParams(), 3, 2000);

        // 创建交易对象用于估算 gas
        const transaction = {
          from: this.wallet.getAddress(),
          to: STAKE_ADDRESS,
          value: amountWei,
          data: this.contract.interface.encodeFunctionData("stake", []),
          chainId: 10143,
          type: 2,
        };

        // 估算 gas
        const estimatedGas = await retry(async () => await this.estimateGas(transaction), 3, 2000);
        this.logger.info(`[${this.accountIndex}] 估算 gas: ${estimatedGas}`);

        // 添加剩余交易参数
        transaction.nonce = await this.wallet.provider.getTransactionCount(
          this.wallet.getAddress(),
          "latest",
        );
        transaction.gasLimit = estimatedGas;
        transaction.maxFeePerGas = gasParams.maxFeePerGas;
        transaction.maxPriorityFeePerGas = gasParams.maxPriorityFeePerGas;

        // 签名并发送交易
        const tx = await this.wallet.wallet.sendTransaction(transaction);

        // 等待交易确认
        this.logger.info(`[${this.accountIndex}] 等待交易确认...`);

        try {
          const receipt = await tx.wait();
          if (receipt.status === 1) {
            this.logger.success(
              `[${this.accountIndex}] 成功在 Kintsu 上质押 ${randomAmount} MON. TX: ${explorerUrl}${tx.hash}`,
            );
            return {
              success: true,
              code: "SUCCESS",
              message: `[${this.accountIndex}] 成功在 Kintsu 上质押 ${randomAmount} MON. TX: ${explorerUrl}${tx.hash}`,
              data: {
                txHash: tx.hash,
              },
            };
          } else {
            this.logger.error(`[${this.accountIndex}] 交易失败. 状态: ${receipt.status}`);
            return {
              success: false,
              code: "TRANSACTION_FAILED",
            };
          }
        } catch (error) {
          this.logger.error(`[${this.accountIndex}] 交易失败: ${error.message}`);
          return {
            success: false,
            code: "TRANSACTION_FAILED",
          };
        }


      } catch (error) {
        const errorMessage = error.message;

        // 处理最小质押要求错误
        if (errorMessage.includes("Minimum stake")) {
          this.logger.error(`[${this.accountIndex}] 错误: 未满足最小质押要求. 尝试使用更高金额.`);
          continue;
        }

        const randomPause = getRandomValue(3, 5);
        this.logger.error(
          `[${this.accountIndex}] | 在 Kintsu 上质押 MON 时出错: ${error.message}. 暂停 ${randomPause} 秒`,
        );
        sleep(randomPause);
      }
    }

    return false;
  }

  /**
   * 获取代币余额
   * @param {string} tokenSymbol - 代币符号
   * @returns {Promise<string>} - 代币余额
   */
  async getTokenBalance(tokenSymbol = "native") {
    try {
      if (tokenSymbol === "native") {
        const balanceWei = await this.wallet.provider.getBalance(this.wallet.getAddress());
        return formatEther(balanceWei);
      }

      // 如果需要，可以在这里添加其他代币的余额查询

      return "0";
    } catch (error) {
      this.logger.error(`[${this.accountIndex}] 获取代币余额失败: ${error.message}`);
      return "0";
    }
  }

  /**
   * 获取已质押代币余额
   * @returns {Promise<bigint|null>} - 已质押代币余额
   */
  async getStakedTokenBalance() {
    try {
      // 创建合约实例
      const balance = await this.contract.balanceOf(this.wallet.getAddress());

      this.logger.info(`[${this.accountIndex}] 已质押代币余额: ${formatEther(balance)} 代币`);

      return balance;
    } catch (error) {
      this.logger.error(`[${this.accountIndex}] 获取已质押代币余额失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 请求解除质押
   * @returns {Promise<{status: number, hash?: string, error?: string, amount?: number}>}
   */
  async requestUnstake() {
    for (let i = 0; i < (this.config.settings?.attempts || 3); i++) {
      try {
        this.logger.info(`[${this.accountIndex}] 请求从 Kintsu 解除质押 MON`);

        // 获取已质押余额
        const amountWei = await this.getStakedTokenBalance();
        const amountEther = formatEther(amountWei);

        if (amountWei === 0n) {
          this.logger.warn(`[${this.accountIndex}] 未找到可解除质押的代币`);
          return {
            status: 0,
            error: "未找到可解除质押的代币",
          };
        }

        // 获取 gas 参数
        const gasParams = await retry(async () => await this.getGasParams(), 3, 2000);

        // 创建交易对象用于 requestUnlock
        const transaction = {
          from: this.wallet.getAddress(),
          to: STAKE_ADDRESS,
          data: this.contract.interface.encodeFunctionData("requestUnlock", [amountWei]),
          chainId: 10143,
          type: 2,
        };

        // 估算 gas
        const estimatedGas = await retry(async () => await this.estimateGas(transaction), 3, 2000);
        this.logger.info(`[${this.accountIndex}] 估算 gas: ${estimatedGas}`);

        // 添加剩余交易参数
        transaction.nonce = await retry(async () => await this.wallet.provider.getTransactionCount(
          this.wallet.getAddress(),
          "latest",
        ), 3, 2000);
        transaction.gasLimit = estimatedGas;
        transaction.maxFeePerGas = gasParams.maxFeePerGas;
        transaction.maxPriorityFeePerGas = gasParams.maxPriorityFeePerGas;

        // 签名并发送交易
        const tx = await this.wallet.wallet.sendTransaction(transaction);

        // 等待交易确认
        this.logger.info(`[${this.accountIndex}] 等待解除质押请求确认...`);
        const receipt = await retry(async () => await tx.wait(), 3, 5000);

        if (receipt.status === 1) {
          this.logger.success(
            `[${this.accountIndex}] 成功请求从 Kintsu 解除质押 ${amountEther} MON. TX: ${explorerUrl}${tx.hash}`,
          );

          return {
            status: 1,
            hash: tx.hash,
            amount: parseFloat(amountEther),
          };
        } else {
          this.logger.error(`[${this.accountIndex}] 交易失败. 状态: ${receipt.status}`);

          return {
            status: 0,
            hash: tx.hash,
            error: "交易失败",
          };
        }
      } catch (error) {
        // 随机暂停
        const randomPause = Math.floor(
          Math.random() *
          ((this.config.settings?.pause_between_attempts?.[1] || 30) -
            (this.config.settings?.pause_between_attempts?.[0] || 10)) +
          (this.config.settings?.pause_between_attempts?.[0] || 10),
        );

        this.logger.error(
          `[${this.accountIndex}] | 在 Kintsu 上请求解除质押时出错: ${error.message}. 暂停 ${randomPause} 秒`,
        );

        await new Promise((resolve) => setTimeout(resolve, randomPause * 1000));
      }
    }

    return {
      status: 0,
      error: "超过最大重试次数",
    };
  }
}
