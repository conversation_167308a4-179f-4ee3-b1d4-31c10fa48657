import { resolveFromModule } from "../../../base/tools/path.js";
import {
  parseTOMLConfig,
  filterWalletsByIndex,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import Mace2 from "./mace2.js";
import CSV from "../../../base/tools/csv.js";
import logger from "../../../base/tools/logger.js";

class MaceTask {
  constructor(rpc) {
    this.rpc = rpc;
    this.mace = new Mace2(rpc);
  }

  async handleWallet(wallet) {
    const { private_key, index, address } = wallet;

    try {
      const tokenArr = this.mace.getRandomTokens();
      logger.info(`钱包 ${index} 将执行 ${tokenArr.length} 次Swap`);

      for (let i = 0; i < tokenArr.length; i++) {
        // 随机选择一个代币
        const token = tokenArr[i];
        logger.info(`钱包 ${index} 执行第 ${i + 1} 次Swap: ${token.name}`);

        const result = await this.mace.executeSwap(private_key, token);
        if (result) {
          logger.success(`钱包 ${index} Swap ${token.name} 成功`);
          await this.mace.approveToken(private_key, token);
        } else {
          logger.error(`钱包 ${index} Swap ${token.name} 失败`);
          return {
            success: false,
            message: `Swap ${token.name} 失败`
          };
        }

        // 如果不是最后一次swap，等待30-70秒
        if (i < tokenArr.length - 1) {
          const delay = getRandomValue(30, 70);
          logger.info(`等待 ${delay} 秒后继续下一次Swap...`);
          await sleep(delay);
        }
      }

      return {
        success: true,
        message: "所有Swap完成"
      };

    } catch (error) {
      logger.error(`钱包 ${index} 处理出错:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  async start(indexArg) {
    logger.info("开始Mace Swap任务...");

    try {
      // 加载钱包
      const walletList = await CSV.read(getCSVFilePath());
      const filteredWallets = filterWalletsByIndex(indexArg, walletList);
      const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
      const totalWallets = shuffledWallets.length;

      logger.info(`成功加载 ${totalWallets} 个钱包`);

      // 统计变量
      let successCount = 0;
      let failedCount = 0;
      let failedIndexes = [];

      // 遍历处理每个钱包
      for (const [currentIndex, wallet] of shuffledWallets.entries()) {
        const { index, address } = wallet;

        if (!wallet.private_key?.trim()) {
          logger.warn(`钱包 ${index} 的私钥为空，跳过处理`);
          failedIndexes.push(index);
          failedCount++;
          continue;
        }

        logger.info(
          `开始处理第 ${currentIndex + 1}/${totalWallets} 个钱包，编号: ${index}，地址: ${address}`,
        );

        const result = await this.handleWallet(wallet);

        if (result.success) {
          successCount++;
        } else {
          failedCount++;
          failedIndexes.push(index);
        }

        // 如果不是最后一个钱包，添加随机延迟
        if (currentIndex < totalWallets - 1) {
          const delaySeconds = getRandomValue(10, 20);
          logger.info(`等待 ${delaySeconds} 秒后继续下一个钱包...`);
          await sleep(delaySeconds);
        }
      }

      // 打印统计信息
      logger.info("------------------------");
      logger.info(`总计处理钱包: ${totalWallets}`);
      logger.info(`成功: ${successCount}`);
      logger.info(`失败: ${failedCount}`);
      if (failedIndexes.length > 0) {
        logger.info(`失败钱包编号: ${failedIndexes.join(", ")}`);
      }

    } catch (error) {
      logger.error("任务执行出错:", error);
    }
  }
}

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  // 加载配置
  const configPath = resolveFromModule(import.meta.url, "../config.toml");
  const config = parseTOMLConfig(configPath);
  const monadConfig = config.monad;
  const rpc = monadConfig.rpc;

  const task = new MaceTask(rpc);

  // 如果没有参数，测试第一个钱包
  if (!indexArg) {
    const walletList = await CSV.read(getCSVFilePath());
    if (walletList.length > 0) {
      logger.info("测试模式：使用第一个钱包");
      await task.handleWallet(walletList[0]);
    }
  } else {
    // 正常模式：处理指定范围的钱包
    await task.start(indexArg);
  }
}

// 运行主函数
main().catch(error => {
  logger.error("未处理的错误:", error);
  process.exit(1);
});

export default main;
