import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import axios from "axios";
import { getHumanRandomFloat } from "../../../base/tools/common.js";

class Mace {
  constructor(rpc) {
    this.rpc = rpc;
    this.provider = new ethers.JsonRpcProvider(this.rpc);

    // 合约地址和配置
    this.MACE_ROUTER = "******************************************";
    this.MACE_API = "https://swaps-api.test.mace.ag/get-best-routes";

    // 合约ABI
    this.MACE_ABI = [
      {
        type: "function",
        name: "executeSwap",
        inputs: [
          { name: "inputToken", type: "address" },
          { name: "amountIn", type: "uint256" },
          { name: "amountOutMin", type: "uint256" },
          {
            name: "path",
            type: "tuple[]",
            components: [
              { name: "adapter", type: "address" },
              {
                name: "subPath",
                type: "tuple[]",
                components: [
                  { name: "dexPair", type: "address" },
                  { name: "outputToken", type: "address" },
                  { name: "extraCallData", type: "bytes" }
                ]
              }
            ]
          },
          { name: "recipient", type: "address" }
        ],
        outputs: [],
        stateMutability: "payable"
      }
    ];

    // 支持的代币列表
    this.TOKENS = {
      CHOG: {
        address: "0x7E9953A11E606187be268C3A6Ba5f36635149C81",
        name: "CHOG"
      },
      KURU: {
        address: "0x11A6B19Ae3d383A6BddC46598D66198940aE2273",
        name: "KURU"
      },
      KB: {
        address: "0x34D1ae6076Aee4072F54e1156D2e507DD564a355",
        name: "KB"
      },
      ELPAB: {
        address: "0x835c39208636525695FC8220f58c0914fC71EE3F",
        name: "ELPAB"
      },
      WIF: {
        address: "******************************************",
        name: "WIF"
      },
      DAK: {
        address: "******************************************",
        name: "DAK"
      },
      IDN: {
        address: "******************************************",
        name: "IDN"
      }
    };

    // 配置
    this.config = {
      swapAmount: {
        min: 0.001,
        max: 0.01
      }
    };
  }

  /**
   * 获取最佳交易路径
   */
  async getBestRoutes(walletAddress, amountIn, outputToken) {
    try {
      const response = await axios.post(this.MACE_API, {
        from: walletAddress,
        in: [{
          token: "native",
          amount: amountIn.toString()
        }],
        out: [{
          token: outputToken,
          minAmount: "0",
          slippageToleranceBps: 10000
        }]
      });

      return response.data.routes[0];
    } catch (error) {
      logger.error(`获取交易路径失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取随机代币
   */
  getRandomToken() {
    const tokens = Object.values(this.TOKENS);
    return tokens[Math.floor(Math.random() * tokens.length)];
  }

  /**
   * 执行swap
   */
  async executeSwap(private_key, outputToken) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.MACE_ROUTER, this.MACE_ABI, wallet);

      // 随机生成交易金额
      const randomAmount = getHumanRandomFloat(this.config.swapAmount.min, this.config.swapAmount.max);
      const amountInWei = ethers.parseEther(randomAmount.toFixed(4));

      // 检查钱包余额
      const balance = await this.provider.getBalance(wallet.address);
      if (balance < amountInWei) {
        logger.info(`余额不足，无法Swap`, {
          当前余额: ethers.formatEther(balance),
          需要数量: ethers.formatEther(amountInWei)
        });
        return false;
      }

      // 获取最佳路径
      const bestRoute = await this.getBestRoutes(wallet.address, amountInWei, outputToken);

      // 构造交易参数
      const path = bestRoute.routes.map(route => ({
        adapter: route.adapter,
        subPath: route.adapterHops.map(hop => ({
          dexPair: hop.exchange,
          outputToken: hop.expectedOut.token,
          extraCallData: hop.extraCallData
        }))
      }));

      logger.info(`准备Swap ${ethers.formatEther(amountInWei)} MON`);

      // 执行交易
      const tx = await contract.executeSwap(
        "******************************************", // native token
        amountInWei,
        0, // amountOutMin
        path,
        wallet.address,
        { value: amountInWei }
      );

      logger.info(`Swap交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();
      logger.success(`Swap交易已确认: ${tx.hash}`);

      return true;
    } catch (error) {
      logger.error("Swap失败:", error);
      return false;
    }
  }
}

export default Mace;
