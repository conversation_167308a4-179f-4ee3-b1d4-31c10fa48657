import axios from "axios";
import { ethers } from "ethers";
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { getHumanRandomFloat, getRandomValue, sleep } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const tokenList = JSON.parse(fs.readFileSync(path.join(__dirname, './asserts.json')));

class Mace2 {
  constructor(rpc) {
    this.rpc = rpc;
    this.provider = new ethers.JsonRpcProvider(this.rpc);

    // 合约地址和配置
    this.MACE_ROUTER = "******************************************";
    this.MACE_API = "https://swaps-api.test.mace.ag/get-best-routes";

    // 合约ABI
    this.ERC20_ABI = [
      {
        "constant": true,
        "inputs": [],
        "name": "decimals",
        "outputs": [{ "name": "", "type": "uint8" }],
        "type": "function"
      },
      {
        "constant": true,
        "inputs": [{ "name": "_owner", "type": "address" }],
        "name": "balanceOf",
        "outputs": [{ "name": "balance", "type": "uint256" }],
        "type": "function"
      },
      {
        "constant": true,
        "inputs": [
          { "name": "_owner", "type": "address" },
          { "name": "_spender", "type": "address" }
        ],
        "name": "allowance",
        "outputs": [{ "name": "", "type": "uint256" }],
        "type": "function"
      },
      {
        "constant": false,
        "inputs": [
          { "name": "_spender", "type": "address" },
          { "name": "_value", "type": "uint256" }
        ],
        "name": "approve",
        "outputs": [{ "name": "", "type": "bool" }],
        "type": "function"
      }
    ]
    // 合约ABI
    this.MACE_ABI = [
      {
        type: "function",
        name: "executeSwap",
        inputs: [
          { name: "inputToken", type: "address" },
          { name: "amountIn", type: "uint256" },
          { name: "amountOutMin", type: "uint256" },
          {
            name: "path",
            type: "tuple[]",
            components: [
              { name: "adapter", type: "address" },
              {
                name: "subPath",
                type: "tuple[]",
                components: [
                  { name: "dexPair", type: "address" },
                  { name: "outputToken", type: "address" },
                  { name: "extraCallData", type: "bytes" }
                ]
              }
            ]
          },
          { name: "recipient", type: "address" }
        ],
        outputs: [],
        stateMutability: "payable"
      }
    ];


    // 配置
    this.config = {
      swapAmount: {
        min: 0.001,
        max: 0.01
      }
    };
  }
  /**
   * 获取随机代币列表
   * @returns {Array} 随机选择的代币
   */
  getRandomTokens() {
    const shuffledTokens = tokenList.sort(() => Math.random() - 0.5);
    const tokenCount = Math.floor(Math.random() * 3) + 4; // 4到6个代币
    return shuffledTokens.slice(0, tokenCount);
  }

  /**
   * 获取最佳交易路径
   */
  async getBestRoutes(walletAddress, amountIn, outputToken) {
    try {
      const response = await axios.post(this.MACE_API, {
        from: walletAddress,
        in: [{
          token: "native",
          amount: amountIn.toString()
        }],
        out: [{
          token: outputToken,
          minAmount: "0",
          slippageToleranceBps: 10000
        }]
      });

      return response.data.routes[0];
    } catch (error) {
      logger.error(`获取交易路径失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取代币余额
   */
  async getTokenBalance(walletAddress, token) {
    try {
      const contract = new ethers.Contract(token.token, this.ERC20_ABI, this.provider);
      const balance = await contract.balanceOf(walletAddress);
      return ethers.formatUnits(balance, token.decimals);
    } catch (error) {
      throw new Error(`获取代币余额失败: ${error.message}`);
    }
  }

  /**
   * 授权代币
   */
  async approveToken(private_key, token) {
    try {
      const walletSigner = new ethers.Wallet(private_key, this.provider);
      const tokenContract = new ethers.Contract(token.token, this.ERC20_ABI, walletSigner);
      var balance = await this.getTokenBalance(walletSigner.address, token);
      balance = Number(balance).toFixed(2);
      const balanceWei = ethers.parseUnits(balance, token.decimals);
      const approveTx = await tokenContract.approve(this.MACE_ROUTER, balanceWei);
      await approveTx.wait();

      logger.info(`授权成功: ${token.name} 已授权 ${balance} 给 ${this.MACE_ROUTER}`);
    } catch (error) {
      throw new Error(`授权失败: ${error.message}`);
    }
  }

  /**
   * 执行swap
   */
  async executeSwap(private_key, outputToken) {
    try {
      const wallet = new ethers.Wallet(private_key, this.provider);
      const contract = new ethers.Contract(this.MACE_ROUTER, this.MACE_ABI, wallet);

      // 随机生成交易金额
      const randomAmount = getHumanRandomFloat(this.config.swapAmount.min, this.config.swapAmount.max);
      // const amountInWei = ethers.parseEther(randomAmount.toFixed(4));
      const amountInWei = ethers.parseUnits(randomAmount.toFixed(4).toString(), outputToken.decimals);

      // 检查钱包余额
      const balance = await this.provider.getBalance(wallet.address);
      if (balance < amountInWei) {
        logger.info(`余额不足，无法Swap`, {
          当前余额: ethers.formatEther(balance),
          需要数量: ethers.formatEther(amountInWei)
        });
        return false;
      }

      // 获取最佳路径
      const bestRoute = await this.getBestRoutes(wallet.address, amountInWei, outputToken.token);

      // 构造交易参数
      const path = bestRoute.routes.map(route => ({
        adapter: route.adapter,
        subPath: route.adapterHops.map(hop => ({
          dexPair: hop.exchange,
          outputToken: hop.expectedOut.token,
          extraCallData: hop.extraCallData
        }))
      }));

      logger.info(`准备Swap ${randomAmount.toFixed(4)} MON`);

      // 执行交易
      const tx = await contract.executeSwap(
        "******************************************", // native token
        amountInWei,
        0, // amountOutMin
        path,
        wallet.address,
        { value: amountInWei }
      );

      logger.info(`Swap交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();
      logger.success(`Swap交易已确认: ${tx.hash}`);

      return true;
    } catch (error) {
      logger.error("Swap失败:", error);
      return false;
    }
  }

  async task(wallet) {
    const { private_key, index, address } = wallet;
    try {
      const tokenArr = this.getRandomTokens();
      logger.info(`钱包 ${index} 将执行 ${tokenArr.length} 次Swap`);

      for (let i = 0; i < tokenArr.length; i++) {
        // 随机选择一个代币
        const token = tokenArr[i];
        logger.info(`钱包 ${index} 执行第 ${i + 1} 次Swap: ${token.name}`);

        const result = await this.executeSwap(private_key, token);
        if (result) {
          logger.success(`钱包 ${index} Swap ${token.name} 成功`);
          await this.approveToken(private_key, token);
        } else {
          logger.error(`钱包 ${index} Swap ${token.name} 失败`);
          return {
            success: false,
            message: `Swap ${token.name} 失败`
          };
        }

        // 如果不是最后一次swap，等待30-70秒
        if (i < tokenArr.length - 1) {
          const delay = getRandomValue(10, 25);
          logger.info(`等待 ${delay} 秒后继续下一次Swap...`);
          await sleep(delay);
        }
      }

      return {
        success: true,
        message: "所有Swap完成"
      };

    } catch (error) {
      logger.error(`钱包 ${index} 处理出错:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

}

export default Mace2;
