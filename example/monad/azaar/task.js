import { resolveFromModule } from "../../../base/tools/path.js";
import {
  parseTOMLConfig,
  filterWalletsByIndex,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import Azaar from "./azaar.js";
import CSV from "../../../base/tools/csv.js";
import logger from "../../../base/tools/logger.js";


function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  // 加载配置
  // 加载配置
  const configPath = resolveFromModule(import.meta.url, "../config.toml");
  const config = parseTOMLConfig(configPath);
  const monadConfig = config.monad;
  const rpc = monadConfig.rpc;
  const minDelaySeconds = config.MIN_DELAY_SECONDS || 5;
  const maxDelaySeconds = config.MAX_DELAY_SECONDS || 15;


  // 加载钱包
  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
  const totalWallets = shuffledWallets.length;

  // 统计变量
  let successCount = 0;
  let failedCount = 0;
  let failedIndexes = [];

  for (let [currentIndex, wallet] of shuffledWallets.entries()) {
    const { private_key, index, address } = wallet;
    const progress = ((currentIndex + 1) / totalWallets * 100).toFixed(2);

    if (!private_key?.trim()) {
      logger.warn(`钱包 ${index} 的私钥为空，跳过处理`);
      failedIndexes.push(index);
      failedCount++;
      continue;
    }

    logger.info(
      `[${progress}%] 开始处理第 ${currentIndex + 1}/${totalWallets} 个钱包，编号: ${index}，地址: ${address}`,
    );

    try {
      const azaar = new Azaar(index, private_key, rpc);
      if (await azaar.mintAll()) {
        successCount++;
        logger.success(`钱包 ${index} 处理成功`);
      } else {
        failedCount++;
        failedIndexes.push(index);
        logger.error(`钱包 ${index} 处理失败`);
      }

      // 添加随机延迟
      if (currentIndex < totalWallets - 1) {
        const delaySeconds = getRandomValue(minDelaySeconds, maxDelaySeconds);
        logger.info(`等待 ${delaySeconds} 秒后处理下一个钱包...`);
        await sleep(delaySeconds);
      }
    } catch (err) {
      logger.error(`钱包 ${index} 处理失败:`);
      logger.error(`- 错误信息: ${err.message}`);
      logger.error(`- 错误堆栈: ${err.stack}`);
      failedCount++;
      failedIndexes.push(index);
    }
  }

  // 输出统计结果
  const summaryMsg =
    failedCount > 0
      ? `共处理 ${totalWallets} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个，失败钱包编号: ${failedIndexes.join(
        ", ",
      )}`
      : `共处理 ${totalWallets} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个`;

  logger.success(summaryMsg);
}

// 运行主函数
main().catch(error => {
  logger.error("未处理的错误:", error);
  process.exit(1);
});

// export default main;
