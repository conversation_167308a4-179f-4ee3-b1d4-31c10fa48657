import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";

// https://app-monad-testnet-v2.azaar.com/mint?chainId=10143
class Azaar {
  constructor(walletIndex, privateKey, rpc) {
    this.walletIndex = walletIndex;
    this.privateKey = privateKey;
    this.rpc = rpc;
    this.wallet = new Wallet(privateKey, rpc);
    this.address = this.wallet.getAddress();
    // 所有代币的合约地址
    this.contracts = {
      az2DAI: "******************************************",
      az2USDC: "******************************************",
      az2WBTC: "******************************************",
      az2PEPE: "******************************************",
      az2USDT: "******************************************",
      az2WNEAR: "******************************************",
    };

    // 每个代币的 mint 数量
    this.mintAmounts = {
      az2DAI: "1812f9cf7920e2b66973e2000000000", // 2
      az2USDC: "1a784379d99db42000000", // 2
      az2WBTC: "6c6b935b8bbd400000", // 0.00002
      az2PEPE: "1e17b84357691b6403d0da8000000000", // 40
      az2USDT: "1a784379d99db42000000", // 2
      az2WNEAR: "b7abc627050305adf14a3d9e40000000000", // 1
    };

    this.abi = [
      {
        inputs: [
          { internalType: "address", name: "account", type: "address" },
          { internalType: "uint256", name: "amount", type: "uint256" },
        ],
        name: "mint",
        outputs: [],
        stateMutability: "nonpayable",
        type: "function",
      },
      {
        inputs: [{ internalType: "address", name: "account", type: "address" }],
        name: "balanceOf",
        outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
        stateMutability: "view",
        type: "function",
      },
      {
        inputs: [],
        name: "decimals",
        outputs: [{ internalType: "uint8", name: "", type: "uint8" }],
        stateMutability: "view",
        type: "function",
      },
    ];

    // 缓存查询到的 decimals
    this.decimalsCache = {};
  }

  async getDecimals(type) {
    if (this.decimalsCache[type]) {
      return this.decimalsCache[type];
    }

    try {
      const provider = new ethers.JsonRpcProvider(this.rpc);
      const contract = new ethers.Contract(this.contracts[type], this.abi, provider);
      const decimals = await contract.decimals();
      this.decimalsCache[type] = Number(decimals);
      return this.decimalsCache[type];
    } catch (error) {
      logger.error(`获取${type}精度失败:`, error);
      throw error;
    }
  }

  generateMintData(address, type) {
    const functionSelector = "0x40c10f19";

    const paddedAddress = address.slice(2).padStart(64, "0");
    const amount = this.mintAmounts[type].padStart(64, "0");
    return `${functionSelector}${paddedAddress}${amount}`;
  }

  async getBalance(address, type) {
    try {
      const provider = new ethers.JsonRpcProvider(this.rpc);
      const contract = new ethers.Contract(this.contracts[type], this.abi, provider);
      const balance = await contract.balanceOf(address);
      return balance;
    } catch (error) {
      logger.error(`获取${type}余额失败:`, error);
      throw error;
    }
  }

  async formatBalance(balance, type) {
    const decimals = await this.getDecimals(type);
    return ethers.formatUnits(balance, decimals);
  }

  async mintToken(tokenName) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(this.privateKey, provider);

    try {
      // 检查当前余额
      const oldBalance = await this.getBalance(wallet.address, tokenName);
      logger.info(`钱包 [${this.walletIndex}] ${this.address} 当前 ${tokenName} 余额: ${await this.formatBalance(oldBalance, tokenName)}`);

      // if (oldBalance > 0) {
      //   logger.info(`钱包 [${this.walletIndex}] ${this.address} 已经 mint 过 ${tokenName}`);
      //   return {
      //     success: true,
      //     code: "ALREADY_MINTED",
      //     message: `已经 mint 过 ${tokenName}`,
      //     data: { balance: await this.formatBalance(oldBalance, tokenName) },
      //   };
      // }

      // 生成调用数据
      const data = this.generateMintData(wallet.address, tokenName);

      // 发送交易
      const tx = await wallet.sendTransaction({
        to: this.contracts[tokenName],
        data: data,
        // gasLimit: 300000,
      });

      logger.info(`钱包 [${this.walletIndex}] ${this.address} ${tokenName} mint 交易已发送，等待确认...`);
      logger.info(`钱包 [${this.walletIndex}] ${this.address} 交易哈希: ${tx.hash}`);

      try {
        const receipt = await tx.wait();
        logger.success(`钱包 [${this.walletIndex}] ${this.address} ${tokenName} mint 成功!`);
        // 查询新余额
        const newBalance = await this.getBalance(wallet.address, tokenName);
        const formattedBalance = await this.formatBalance(newBalance, tokenName);
        logger.info(`钱包 [${this.walletIndex}] ${this.address} 最新 ${tokenName} 余额: ${formattedBalance}`);

        return {
          success: true,
          code: "SUCCESS",
          message: `${tokenName} mint 成功`,
          data: {
            txHash: receipt.hash,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed.toString(),
            balance: formattedBalance,
            decimals: await this.getDecimals(tokenName),
          },
        };
      } catch (error) {
        logger.error(`${tokenName} mint 失败:`, error);
        return {
          success: false,
          code: "MINT_FAILED",
          message: error.message,
        };
      }

    } catch (error) {
      if (error.message.includes("Minting allowed once per day")) {
        logger.info(`钱包 [${this.walletIndex}] ${this.address} 今日已经mint过${tokenName}`);
        return {
          success: true,
          code: "ALREADY_MINTED_TODAY",
          message: `今日已经mint过 ${tokenName}`
        };
      } else {
        logger.error(`${tokenName} mint 失败:`, error);
        return {
          success: false,
          code: "MINT_FAILED",
          message: error.message,
        };
      }
    }
  }

  async swapToken(tokenName) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(this.privateKey, provider);

    // todo 实现swap
  }


  async mintAll() {
    try {

      // 依次 mint 选中的代币
      const selectedTokens = Object.keys(this.contracts);
      let hasSuccess = false;
      for (const token of selectedTokens) {
        const result = await this.mintToken(token);
        if (result.success) {
          hasSuccess = true;
        }
        // 只有在真正 mint 成功时才等待
        if (result.success && result.code === 'SUCCESS' && selectedTokens.indexOf(token) !== selectedTokens.length - 1) {
          const waitTime = Math.floor(Math.random() * 10000) + 5000; // 5000-15000ms
          logger.info(`Mint 成功，等待 ${waitTime / 1000} 秒后继续下一个代币的 mint...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      return hasSuccess;
    } catch (error) {
      logger.error('随机 mint 代币失败:', error);
      return false;
    }
  }
}

export default Azaar;
