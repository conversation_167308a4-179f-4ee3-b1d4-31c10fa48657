import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";

class MintNFT {
  constructor({
    rpc,
    contractAddress,
    mintPrice,
    inputData,
    name = "Monad NFT",
    amount = 1,
    tokenId = null,
    tokenAddress = null,
    contractType = 'ERC721'
  }) {
    this.rpc = rpc;
    this.contractAddress = contractAddress;
    this.tokenAddress = tokenAddress || contractAddress;
    this.mintPrice = ethers.parseEther(mintPrice);
    this.inputData = inputData;
    this.tokenId = tokenId;
    this.name = name;
    this.contractType = contractType;
    this.amount = amount;

    if (!['ERC721', 'ERC1155'].includes(this.contractType)) {
      throw new Error('不支持的合约类型，只支持 ERC721 或 ERC1155');
    }

    if (this.contractType === 'ERC1155' && this.tokenId === null) {
      throw new Error('ERC1155 合约需要指定 tokenId');
    }
  }

  async getBalance(address) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const abi = this.contractType === 'ERC1155' ? ERC1155_ABI : ERC721_ABI;
    const contract = new ethers.Contract(this.tokenAddress, abi, provider);

    if (this.contractType === 'ERC1155') {
      return await contract.balanceOf(address, this.tokenId);
    } else {
      return await contract.balanceOf(address);
    }
  }

  async mint(privateKey, walletIndex) {
    const provider = new ethers.JsonRpcProvider(this.rpc);
    const wallet = new ethers.Wallet(privateKey, provider);

    const balance = await this.getBalance(wallet.address);
    if (balance > 0) {
      logger.info(`钱包 ${walletIndex} 已经mint过 ${this.name}，跳过处理`);
      return {
        success: false,
        code: "ALREADY_MINTED",
        message: `钱包 ${walletIndex} 已经mint过 ${this.name}，跳过处理`,
        data: { balance: balance.toString() }
      };
    }

    try {
      const walletBalance = await provider.getBalance(wallet.address);

      const totalPrice = this.mintPrice * BigInt(this.amount);
      // const estimatedGas = this.mintPrice / 2n;
      // const requiredAmount = totalPrice + estimatedGas;
      const requiredAmount = 2n * this.mintPrice
      if (walletBalance < requiredAmount) {
        const formattedBalance = ethers.formatEther(walletBalance);
        const formattedRequired = ethers.formatEther(requiredAmount);
        logger.error(
          `钱包 ${walletIndex} 余额不足，无法mint ${this.name}, 当前余额: ${formattedBalance}MON, 需要: ${formattedRequired}MON`
        );
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: "余额不足，无法支付mint费用",
          data: {
            balance: formattedBalance,
            required: formattedRequired,
            missing: ethers.formatEther(requiredAmount - walletBalance)
          }
        };
      }

      const txHash = await buildAndSendTransaction(
        this.rpc,
        privateKey,
        this.contractAddress,
        this.inputData,
        totalPrice.toString()
      );

      logger.info(`钱包 ${walletIndex} 正在mint ${this.name}，等待确认`);
      const receipt = await txHash.wait();

      logger.success(`钱包 ${walletIndex} mint ${this.name} 成功，交易哈希: ${receipt.hash}`);
      return {
        success: true,
        code: "SUCCESS",
        message: `钱包 ${walletIndex} mint ${this.name} 成功`,
        data: {
          name: this.name,
          txHash: receipt.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          value: ethers.formatEther(totalPrice),
          amount: this.amount,
          contractType: this.contractType
        }
      };

    } catch (error) {
      if (error.message.includes("insufficient funds")) {
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: `余额不足，无法支付 ${this.name} 的gas费`,
          data: { error: error.message }
        };
      } else if (error.message.includes("nonce")) {
        return {
          success: false,
          code: "NONCE_ERROR",
          message: "Nonce错误，请等待之前的交易确认",
          data: { error: error.message }
        };
      } else if (error.message.includes("execution reverted")) {
        return {
          success: false,
          code: "MINT_UNAVAILABLE",
          message: `Mint失败: ${this.name} 可能未开放或不在白名单中`,
          data: { error: error.message }
        };
      }

      logger.error(`Mint ${this.name} 失败: ${error.message}`);
      return {
        success: false,
        code: "UNKNOWN_ERROR",
        message: `Mint ${this.name} 失败: ${error.message}`,
        data: { error: error.message }
      };
    }
  }
}

const INTERFACE_ABI = [
  {
    "inputs": [
      {
        "internalType": "bytes4",
        "name": "interfaceId",
        "type": "bytes4"
      }
    ],
    "name": "supportsInterface",
    "outputs": [
      {
        "internalType": "bool",
        "name": "",
        "type": "bool"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

const ERC721_ABI = [
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "owner",
        "type": "address"
      }
    ],
    "name": "balanceOf",
    "outputs": [
      {
        "internalType": "uint256",
        "name": "balance",
        "type": "uint256"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

const ERC1155_ABI = [
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "account",
        "type": "address"
      },
      {
        "internalType": "uint256",
        "name": "id",
        "type": "uint256"
      }
    ],
    "name": "balanceOf",
    "outputs": [
      {
        "internalType": "uint256",
        "name": "",
        "type": "uint256"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

export { MintNFT };
