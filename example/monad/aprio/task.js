import CSV from "../../../base/tools/csv.js";
import {
  filterWalletsByIndex,
  parseTOMLConfig,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import Aprio from "./aprio.js";
import logger from "../../../base/tools/logger.js";
import SecureEncryption from "../../../base/tools/secure-encryption.js";
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad;
const rpc = monadConfig.rpc;

const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  // const indexArg = "1";

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  let stakeSuccessCount = 0;
  let stakeFailedCount = 0;
  let unstakeSuccessCount = 0;
  let unstakeFailedCount = 0;
  let claimSuccessCount = 0;
  let claimFailedCount = 0;

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const aprio = new Aprio(wallet.private_key, rpc);

    // 1. 请求取消质押
    const unstakeResult = await aprio.requestUnstake();
    if (unstakeResult.code === "UNSTAKE_SUCCESS") {
      logger.info(`[${wallet.index}] 成功在 Ario 上解质押`);
      await sleep(getRandomValue(5, 10));
    }

    // 2. 领取质押奖励
    const claimResult = await aprio.claimAllRequests();
    if (claimResult.code === "CLAIM_SUCCESS") {
      logger.info(`[${wallet.index}] 成功领取 Ario 质押奖励`);
      await sleep(getRandomValue(5, 10));
    }

    // 3. 检查质押数量
    const skipIfStaked = Math.random() < 0.5;
    const stakeResult = await aprio.stake(skipIfStaked);
    if (stakeResult.code === "STAKE_SUCCESS") {
      logger.info(`[${wallet.index}] 成功在 Ario 上质押`);
    } else {
      logger.error(`[${wallet.index}] 在 Ario 上质押失败, ${stakeResult.message}`);
    }

    // 4. 解质押
    if (Math.random() < 0.5) {
      // 等待 8-25 秒
      if (stakeResult.code === "STAKE_SUCCESS") {
        await sleep(getRandomValue(8, 25));
      }

      const unstakeResult = await aprio.requestUnstake();
      if (unstakeResult.code === "UNSTAKE_SUCCESS") {
        logger.info(`[${wallet.index}] 成功在 Ario 上解质押`);
        await sleep(getRandomValue(8, 25));
      } else {
        logger.error(`[${wallet.index}] 在 Ario 上解质押失败, ${unstakeResult.message}`);
      }

      const claimResult = await aprio.claimAllRequests();
      if (claimResult.code === "CLAIM_SUCCESS") {
        logger.info(`[${wallet.index}] 成功领取 Ario 质押奖励`);
      } else {
        logger.error(`[${wallet.index}] 领取 Ario 质押奖励失败, ${claimResult.message}`);
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印统计信息
  logger.info("------------------------");
  logger.info(`总计处理钱包: ${shuffledWallets.length}`);
  logger.info(`质押成功: ${stakeSuccessCount}`);
  logger.info(`质押失败: ${stakeFailedCount}`);
  logger.info(`解质押成功: ${unstakeSuccessCount}`);
  logger.info(`解质押失败: ${unstakeFailedCount}`);
  logger.info(`领取成功: ${claimSuccessCount}`);
  logger.info(`领取失败: ${claimFailedCount}`);
}

// 执行任务
// node example/monad/aprio/task.js 1-10
main();
