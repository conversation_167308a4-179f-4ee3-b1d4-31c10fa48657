import axios from "axios";
import { ethers, formatEther, parseEther } from "ethers";
import { HttpsProxyAgent } from "https-proxy-agent";

import { Wallet } from "../../../base/evm/wallet.js";
import { parseTOMLConfig, retry } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { getHumanNumber } from "../../../base/tools/common.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad || {};
const rpc = monadConfig.rpc || "https://testnet-rpc.monad.xyz/";

// https://stake.apr.io/
export default class Aprio {
  /**
   * 创建 Aprio 实例
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpc - RPC 节点地址
   */
  constructor(privateKey, proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.logger = logger;

    this.CONTRACT_ADDRESS = "******************************************";

    this.contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      [
        {
          type: "function",
          name: "deposit",
          inputs: [
            { name: "assets", type: "uint256", internalType: "uint256" },
            { name: "receiver", type: "address", internalType: "address" },
          ],
          outputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
          stateMutability: "payable",
        },
        {
          type: "function",
          name: "claimRewards",
          inputs: [],
          outputs: [],
          stateMutability: "nonpayable",
        },
        {
          type: "function",
          name: "requestRedeem",
          inputs: [
            { name: "shares", type: "uint256", internalType: "uint256" },
            { name: "controller", type: "address", internalType: "address" },
            { name: "owner", type: "address", internalType: "address" },
          ],
          outputs: [{ name: "requestId", type: "uint256", internalType: "uint256" }],
          stateMutability: "nonpayable",
        },
        {
          type: "function",
          name: "redeem",
          inputs: [
            { name: "requestId", type: "uint256", internalType: "uint256" },
            { name: "receiver", type: "address", internalType: "address" },
          ],
          outputs: [],
          stateMutability: "nonpayable",
        },
        {
          type: "function",
          name: "claimableRedeemRequest",
          inputs: [
            { name: "requestId", type: "uint256", internalType: "uint256" },
            { name: "controller", type: "address", internalType: "address" },
          ],
          outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
          stateMutability: "view",
        },
        {
          type: "function",
          name: "redeemRequests",
          inputs: [{ name: "", type: "uint256", internalType: "uint256" }],
          outputs: [
            { name: "shares", type: "uint256", internalType: "uint256" },
            { name: "controller", type: "address", internalType: "address" },
            { name: "owner", type: "address", internalType: "address" },
            { name: "timestamp", type: "uint256", internalType: "uint256" },
          ],
          stateMutability: "view",
        },
      ],
      this.wallet.wallet,
    );

    this.query_contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      [
        {
          type: "function",
          name: "balanceOf",
          inputs: [{ name: "account", type: "address", internalType: "address" }],
          outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
          stateMutability: "view",
        },
        {
          type: "function",
          name: "withdrawalWaitTime",
          inputs: [],
          outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
          stateMutability: "view",
        },
      ],
      this.wallet.wallet,
    );
  }

  /**
   * 获取解除质押所需参数
   * id: requestId - claimableRedeemRequest 所需的 requestId
   * assets: 请求数量 - 请求数量
   * shares: 请求份额 - 请求份额
   * claimed: 是否已领取 - 是否已领取
   * isClaimable: 是否可领取 - 是否可领取
   * requestedAt: 请求时间 - 请求时间
   * @returns {Promise<{success: boolean, requests?: Array<{id: string, assets: string, shares: string, claimed: boolean, isClaimable: boolean, requestedAt: string}>, message?: string}>}
   */
  async getWithdrawalRequests() {
    try {
      const address = this.wallet.getAddress();

      let proxy = null;
      if (this.proxy) {
        proxy = new HttpsProxyAgent(this.proxy);
      }

      const response = await fetch("https://stake-api.apr.io/withdrawal_requests?address=" + address, {
        "headers": {
          "accept": "*/*",
          "accept-language": "en,zh;q=0.9,zh-CN;q=0.8",
          "priority": "u=1, i",
          "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": "\"macOS\"",
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-site",
          "Referer": "https://stake.apr.io/",
          "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        "body": null,
        "method": "GET",
        "httpsAgent": proxy,
      });

      // 检查响应状态
      if (!response.ok) {
        return {
          success: false,
          code: "GET_WITHDRAWAL_REQUESTS_FAILED",
          message: "获取提款请求失败",
        };
      }

      // 解析响应数据
      const data = await response.json();
      let result = {
        success: true,
        code: "GET_WITHDRAWAL_REQUESTS_SUCCESS",
        requests: data.map((req) => ({
          id: req.id,
          assets: req.assets,
          shares: req.shares,
          claimed: req.claimed,
          isClaimable: req.is_claimable,
          requestedAt: req.requested_at,
        })),
      }
      return result;
    } catch (error) {
      this.logger.error(`获取提款请求失败: ${error.message}`);
      return {
        success: false,
        code: "GET_WITHDRAWAL_REQUESTS_FAILED",
        message: error.message,
      };
    }
  }

  /**
   * 获取可领取的总金额
   * @returns {Promise<{success: boolean, amount?: string, message?: string}>}
   */
  async getClaimableAmount() {
    try {
      const { success, requests } = await this.getWithdrawalRequests();
      if (!success) throw new Error("Failed to get requests");

      const claimableAmount = requests
        .filter((req) => req.isClaimable && !req.claimed)
        .reduce((sum, req) => sum + BigInt(req.assets), BigInt(0));

      return {
        success: true,
        amount: claimableAmount.toString(),
      };
    } catch (error) {
      this.logger.error(`获取可领取金额失败: ${error.message}`);
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * 获取monad余额
   * @return {number}
   */
  async getBalance() {
    try {
      const balance = await retry(async () => await this.wallet.getBalance(), 3, 2000);
      return formatEther(balance);
    } catch (error) {
      return 0;
    }
  }

  /**
   * 领取所有请求
   * @returns {Promise<{success: boolean, hash?: string, message?: string, code?: string}>}
   */
  async claimAllRequests() {
    try {
      // 获取所有请求
      const { success, requests } = await this.getWithdrawalRequests();
      if (!success) throw new Error("Failed to get requests");

      // 过滤出所有可领取的请求ID
      const claimableRequestIds = requests
        .filter((req) => req.isClaimable && !req.claimed)
        .map((req) => BigInt(req.id)); // 转换为 BigInt

      const claimableAmount = requests
        .filter((req) => req.isClaimable && !req.claimed)
        .reduce((sum, req) => sum + BigInt(req.assets), BigInt(0));

      if (claimableRequestIds.length === 0 || claimableAmount === 0n) {
        return {
          success: true,
          code: "NO_CLAIMABLE_REQUESTS",
          message: "No claimable requests found",
        };
      }

      this.logger.info(`准备领取的请求 IDs: ${claimableRequestIds[0]}`);
      this.logger.info(`准备领取的请求金额: ${formatEther(claimableAmount)} MON`);

      // 调用合约领取
      const tx = await this.contract.redeem(claimableRequestIds[0], this.wallet.getAddress());

      try {
        const receipt = await tx.wait();
        this.logger.info(`领取请求交易已发送 | Hash: ${tx.hash}`);
        if (receipt.status !== 1) {
          throw new Error(`领取请求失败 | Status: ${receipt.status}`);
        }
        return {
          success: true,
          hash: receipt.hash,
          code: "CLAIM_SUCCESS",
          claimedId: claimableRequestIds[0],
        };
      } catch (error) {
        this.logger.error(`领取请求失败: ${error.message}`);
        return {
          success: false,
          code: "CLAIM_FAILED",
          message: error.message,
        };
      }

    } catch (error) {
      this.logger.error(`领取请求失败: ${error.message}`);
      return {
        success: false,
        code: "CLAIM_FAILED",
        message: error.message,
      };
    }
  }

  /**
   * 查询是否已质押
   * @returns {Promise<{success: boolean, balance?: string, message?: string}>}
   */
  async checkStaked() {
    try {
      const address = this.wallet.getAddress();
      const balance = await retry(async () => await this.query_contract.balanceOf(address), 3, 2000);

      if (balance > 0n) {
        const formattedBalance = formatEther(balance);
        this.logger.info(`当前质押数量: ${formattedBalance} MON`);
        return {
          'success': true,
          'balance': formattedBalance,
          'message': `已质押: ${formattedBalance} MON`,
        };
      }

      return {
        success: false,
        balance: "0",
        message: "未质押",
      };
    } catch (error) {
      this.logger.error(`查询质押状态失败: ${error.message}`);
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * 获取质押金额
   * @param {number} maxAmount - 最大质押金额
   * @returns {number}
   */
  getStakeAmount(maxAmount) {
    return getHumanNumber(maxAmount);
  }


  /**
   * 质押大额
   * @param {string} amount - 质押金额
   * @returns {Promise<{success: boolean, hash?: string, name?: string, message?: string, code?: string}>}
   */
  async stakeLarge(amount) {
    let txHash = null;
    try {
      const address = this.wallet.getAddress();

      // 1. 检查是否已质押
      const stakedAmount = await retry(async () => await this.query_contract.balanceOf(address), 3, 2000);
      const formattedStakedAmount = formatEther(stakedAmount);
      this.logger.info(`当前质押数量: ${formattedStakedAmount} MON`);

      // 质押3个以上，跳过质押
      if (formattedStakedAmount > 3) {
        this.logger.success(`已经质押超过3个MON，跳过质押 | 已质押 ${formattedStakedAmount} MON`);
        return {
          success: true,
          code: "ALREADY_STAKE",
          message: `已经质押过: ${formattedStakedAmount}`,
        };
      }

      // 2. 检查余额
      const balance = await this.wallet.getBalance();
      if (amount === 0) {
        const formattedBalance = formatEther(balance);
        this.logger.info(`当前余额: ${formattedBalance} MON`);
        amount = this.getStakeAmount(formattedBalance / 2);
      }

      this.logger.info(`质押金额: ${amount} MON`);
      const amountInWei = parseEther(amount.toString());

      if (balance < amountInWei) {
        const formattedBalance = formatEther(balance);
        const formattedAmount = formatEther(amountInWei);

        this.logger.error(
          `余额不足支付质押费用 | 当前余额: ${formattedBalance} MON | 需要: ${formattedAmount} MON`,
        );

        return {
          'success': false,
          'code': "INSUFFICIENT_BALANCE",
          'message': `余额不足支付质押费用`,
          'data': {
            'balance': formattedBalance,
            'required': formattedAmount,
          },
        };
      }

      // 3. 执行质押 - 修改为使用 deposit 函数
      const tx = await this.contract.deposit(amountInWei, address, {
        value: amountInWei,
      });

      txHash = tx.hash;
      try {
        const receipt = await tx.wait();
        if (receipt.status !== 1) {
          throw new Error(`质押失败 | Hash: ${txHash} | Status: ${receipt.status}`);
        }

        this.logger.success(`质押成功 | Hash: ${txHash}`);

        return {
          success: true,
          hash: txHash,
          blockNumber: receipt.blockNumber,
        };
      } catch (error) {
        this.logger.error(`质押失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          hash: txHash,
        };
      }

    } catch (error) {
      this.logger.error(`质押失败: ${error.message}`);
      if (error.message.includes("insufficient balance")) {
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: error.message,
        };
      }

      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
        hash: txHash,
      };
    }
  }

  /**
   * 质押
   * @param {boolean} skipIfStaked - 是否跳过已质押
   * @param {string} [amount] - 质押金额
   * @returns {Promise<{success: boolean, hash?: string, name?: string, message?: string, code?: string}>}
   */
  async stake(skipIfStaked = false, amount = 0) {
    let txHash = null;
    try {
      const address = this.wallet.getAddress();

      // 1. 检查是否已质押
      const stakedAmount = await retry(async () => await this.query_contract.balanceOf(address), 3, 2000);
      this.logger.info(`当前质押数量: ${formatEther(stakedAmount)} MON`);

      if (stakedAmount > 1n && skipIfStaked) {
        this.logger.success(`已经质押过 | ${formatEther(stakedAmount)} MON`);
        return {
          success: true,
          code: "ALREADY_STAKE",
          message: `已经质押过: ${formatEther(stakedAmount)}`,
        };
      }

      // 2. 检查余额
      const balance = await this.wallet.getBalance();
      if (amount === 0) {
        const formattedBalance = formatEther(balance);
        this.logger.info(`当前余额: ${formattedBalance} MON`);
        amount = this.getStakeAmount(formattedBalance);
      }

      this.logger.info(`质押金额: ${amount} MON`);
      const amountInWei = parseEther(amount.toString());

      if (balance < amountInWei) {
        const formattedBalance = formatEther(balance);
        const formattedAmount = formatEther(amountInWei);

        this.logger.error(
          `余额不足支付质押费用 | 当前余额: ${formattedBalance} MON | 需要: ${formattedAmount} MON`,
        );

        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: `余额不足支付质押费用`,
          data: {
            balance: formattedBalance,
            required: formattedAmount,
          },
        };
      }

      // 3. 执行质押 - 修改为使用 deposit 函数
      const tx = await this.contract.deposit(amountInWei, address, {
        value: amountInWei,
      });

      txHash = tx.hash;
      try {
        const receipt = await tx.wait();
        if (receipt.status !== 1) {
          throw new Error(`质押失败 | Hash: ${txHash} | Status: ${receipt.status}`);
        }
        this.logger.success(`质押成功 | Hash: ${txHash}`);
        return {
          success: true,
          hash: txHash,
          code: "STAKE_SUCCESS",
          blockNumber: receipt.blockNumber,
        };
      } catch (error) {
        this.logger.error(`质押失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          hash: txHash,
        };
      }

    } catch (error) {
      this.logger.error(`质押失败: ${error.message}`);
      if (error.message.includes("insufficient balance")) {
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: error.message,
        };
      }

      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
        hash: txHash,
      };
    }
  }

  /**
   * 估算交易 gas
   * @param {Object} params 参数对象
   * @param {string} method - 方法名
   * @param {Array} args - 方法参数
   * @param {boolean} useFixed - 是否使用固定 gas，默认 false
   * @returns {Promise<{gasLimit: bigint, gasPrice: bigint}>}
   */
  async estimateGas({ method, args, useFixed = false }) {
    try {
      // 获取 gas 价格信息
      const feeData = await retry(async () => await this.wallet.provider.getFeeData(), 3, 2000);
      const gasPrice = feeData.gasPrice || feeData.maxFeePerGas || BigInt(0);

      let gasLimit;
      if (useFixed) {
        gasLimit = BigInt(300000);
      } else {
        try {
          const estimate = await retry(async () => await this.contract[method].estimateGas(...args), 3, 2000);
          gasLimit = BigInt(Math.ceil(Number(estimate) * 1.2)); // 增加 20% 缓冲
        } catch (error) {
          console.warn(`Gas 估算失败，使用固定值: ${error.message}`);
          gasLimit = BigInt(300000);
        }
      }

      return {
        gasLimit,
        gasPrice,
        ...(feeData.maxPriorityFeePerGas && {
          maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
          maxFeePerGas: feeData.maxFeePerGas,
        }),
      };
    } catch (error) {
      throw new Error(`Gas 估算失败: ${error.message}`);
    }
  }

  /**
   * 取消质押
   * @param {string} [amount] - 取消质押数量，如果为0则取消全部质押
   * @param {boolean} skipIfStaked - 是否跳过已质押, 默认 false
   * @returns {Promise<{success: boolean, hash?: string, message?: string}>}
   */
  async requestUnstake(amount = 0, skipIfStaked = false) {
    try {
      const address = this.wallet.getAddress();

      // 0. 1 MON 以上的质押不取消，为了博取aprio空投
      const stakedAmount = await retry(async () => await this.query_contract.balanceOf(address), 3, 2000);
      const formattedStakedAmount = formatEther(stakedAmount);
      if (formattedStakedAmount > 1 && skipIfStaked) {
        this.logger.success(`已经质押过大额，跳过取消质押 | 已质押 ${formattedStakedAmount} MON`);
        return {
          success: true,
          code: "ALREADY_STAKE",
          message: `已经质押过大额: ${formattedStakedAmount}`,
        };
      }

      // 1. 检查质押状态
      const stakedInfo = await this.checkStaked();
      this.logger.info(`当前质押状态: ${JSON.stringify(stakedInfo)}`);

      // 2. 确定取消质押数量
      let unstakeAmount;
      if (amount === 0) {
        unstakeAmount = stakedInfo.balance;
      } else {
        unstakeAmount = amount.toString();
      }

      const amountInWei = ethers.parseEther(unstakeAmount); // 1 ETH = 1e18 wei
      if (amountInWei <= 0n) {
        return {
          success: true,
          code: "ZERO_AMOUNT",
          message: "取消质押数量为0",
        };
      }

      // 获取 gas 预估和余额
      const [gasEstimate, balance] = await Promise.all([
        retry(async () => await this.estimateGas({
          method: "requestRedeem",
          args: [amountInWei, address, address],
        }), 3, 2000),
        retry(async () => await this.wallet.provider.getBalance(address), 3, 2000),
      ]);

      // 获取 gas 价格
      const gasPrice = await this.wallet.provider.getFeeData();

      // 计算 gas 成本
      const gasCost =
        gasEstimate?.gasLimit && gasPrice?.gasPrice
          ? BigInt(gasEstimate.gasLimit) * BigInt(gasPrice.gasPrice)
          : BigInt(0);
      const balanceBigInt = BigInt(balance);

      // 检查余额是否足够支付 gas
      if (balanceBigInt < gasCost) {
        return {
          success: false,
          code: "INSUFFICIENT_BALANCE",
          message: `余额不足支付 gas 费用`,
          data: {
            balance: formatEther(balanceBigInt),
            required: ethers.formatEther(gasCost.toString()),
          },
        };
      }

      // 发送交易
      const tx = await this.contract.requestRedeem(amountInWei, address, address);

      let txHash = tx.hash;
      this.logger.info(`请求取消质押交易已发送 | Hash: ${txHash}`);

      try {
        const receipt = await tx.wait();
        if (receipt.status !== 1) {
          throw new Error(`请求取消质押失败 | Status: ${receipt.status}`);
        }

        this.logger.success(`请求取消质押成功 | Hash: ${txHash}`);
        return {
          success: true,
          hash: txHash,
          code: "UNSTAKE_SUCCESS",
          message: `请求取消质押成功`,
          amount: formatEther(amountInWei),
        };
      } catch (error) {
        this.logger.error(`请求取消质押失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          hash: txHash,
        };
      }

    } catch (error) {
      this.logger.error(`请求取消质押失败: ${error.message}`);
      return {
        success: false,
        code: "UNSTAKE_FAILED",
        message: error.message,
        hash: null,
      };
    }
  }

  /**
   * 领取奖励
   * @returns {Promise<{success: boolean, hash?: string, message?: string}>}
   */
  async claimRewards() {
    try {
      const tx = await this.contract.claimRewards();
      this.logger.info(`领取奖励交易已发送 | Hash: ${tx.hash}`);

      try {
        const receipt = await tx.wait();
        if (receipt.status === 1) {
          this.logger.success(`领取奖励成功 | Hash: ${tx.hash}`);
          return {
            success: true,
            hash: tx.hash,
            code: "CLAIM_SUCCESS",
            message: `领取奖励成功`,
          };
        } else {
          throw new Error(`领取奖励失败 | Status: ${receipt.status}`);
        }
      } catch (error) {
        this.logger.error(`领取奖励失败: ${error.message}`);
        return {
          success: false,
          code: "TRANSACTION_FAILED",
          message: error.message,
          hash: tx.hash,
        };
      }

    } catch (error) {
      this.logger.error(`领取奖励失败: ${error.message}`);
      return {
        success: false,
        code: "TRANSACTION_FAILED",
        message: error.message,
        hash: tx.hash,
      };
    }
  }
}
