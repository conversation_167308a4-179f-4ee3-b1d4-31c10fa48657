import CSV from "../../../base/tools/csv.js";
import {
  filterWalletsByIndex,
  parseTOMLConfig,
  getRandomValue,
  sleep,
} from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import Aprio from "./aprio.js";
import logger from "../../../base/tools/logger.js";
import SecureEncryption from "../../../base/tools/secure-encryption.js";

const configPath = resolveFromModule(import.meta.url, "../config.toml");
const config = parseTOMLConfig(configPath);
const monadConfig = config.monad;
const rpc = monadConfig.rpc;

const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  let stakeSuccessCount = 0;
  let stakeFailedCount = 0;
  let unstakeSuccessCount = 0;
  let unstakeFailedCount = 0;
  let claimSuccessCount = 0;
  let claimFailedCount = 0;

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const aprio = new Aprio(private_key, proxy, rpc);
    const balance = await aprio.getBalance();
    logger.info(`钱包余额: ${balance}`);
    if (balance < 3) {
      logger.warn(`钱包余额小于3，跳过质押`);
      continue;
    }

    // 随机获取质押总金额的30% ~ 50%, ，然后取整
    const percentage = 30 + Math.floor(Math.random() * 21);
    const stakeAmount = Math.floor(balance * percentage / 100);
    logger.info(`质押金额: ${stakeAmount}`);
    let stakeResult = await aprio.stakeLarge(stakeAmount);
    if (stakeResult.success) {
      stakeSuccessCount++;
    } else {
      stakeFailedCount++;
    }

    // 95% 概率解质押
    if (Math.random() < 0.95) {
      // 等待 8-25 秒
      await sleep(getRandomValue(8, 25));
      let unstakeResult = await aprio.requestUnstake(0, true);
      if (unstakeResult.success) {
        unstakeSuccessCount++;
      } else {
        unstakeFailedCount++;
      }

      await sleep(getRandomValue(15, 30));
      let claimResult = await aprio.claimAllRequests();
      if (claimResult.success) {
        claimSuccessCount++;
      } else {
        claimFailedCount++;
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印统计信息
  logger.info("------------------------");
  logger.info(`总计处理钱包: ${shuffledWallets.length}`);
  logger.info(`质押成功: ${stakeSuccessCount}`);
  logger.info(`质押失败: ${stakeFailedCount}`);
  logger.info(`解质押成功: ${unstakeSuccessCount}`);
  logger.info(`解质押失败: ${unstakeFailedCount}`);
  logger.info(`领取成功: ${claimSuccessCount}`);
  logger.info(`领取失败: ${claimFailedCount}`);
}

// 执行任务
// node example/monad/aprio/task.js 1-10
main();
