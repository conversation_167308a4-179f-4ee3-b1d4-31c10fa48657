import { ethers, parseEther, formatEther } from "ethers";
import axios from "axios";

import { HttpsProxyAgent } from "https-proxy-agent";
import { buildAndSendTransaction } from "../../../base/evm/utils.js";
import logger from "../../../base/tools/logger.js";
import { Wallet } from "../../../base/evm/wallet.js";
import { getHeaders } from "../../../base/tools/fake_useragent.js";
import CSV from "../../../base/tools/csv.js";
import { retry, randomSleep, sleep, getRandomValue } from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";

// 项目地址：https://aicraft.fun/projects/fizen

// 默认邀请码
const defaultInviteCodes = ["M8KE60KZT9", "G9Q0U81LLS", "M0XUU0MNKX"];

class Aicraft {
    constructor(privateKey, walletIndex, rpc, proxy) {
        this.proxy = proxy;
        this.rpc = rpc;
        this.wallet = new Wallet(privateKey, rpc);
        this.address = this.wallet.getAddress();
        this.walletIndex = walletIndex;
        this.privateKey = privateKey;
        this.token = null;
        this.projectId = "b6c652951946383ca8230380c0f63dbf";
        this.csvPath = resolveFromModule(import.meta.url, './aicraft_users.csv');

        // 在构造函数末尾初始化用户数据
        this.#initUserData();
    }

    async #initUserData() {
        try {
            // 检查文件是否存在
            const exists = await CSV.exists(this.csvPath);

            // 如果文件不存在，创建新文件
            if (!exists) {
                await CSV.write(this.csvPath, [], {
                    header: true,
                    columns: ['index', 'walletId', 'address', 'inviteCode', 'inviteByCode']
                });
                logger.info('初始化aicraft_users.csv文件成功');
            }

            // 如果文件存在，读取并格式化数据
            const records = await CSV.read(this.csvPath);
            const userDataMap = records.reduce((acc, record) => {
                if (record.address) {
                    acc[record.address] = record;
                }
                return acc;
            }, {});

            logger.debug(`读取到 ${Object.keys(userDataMap).length} 条用户数据`);
            this.userDatas = userDataMap;
        } catch (error) {
            logger.error(`初始化用户数据失败: ${error.message}`);
            this.userDatas = {};
        }
    }

    #createRequestConfig(additionalHeaders = {}) {
        let headers = getHeaders("https://aicraft.fun", this.walletIndex);
        if (this.token) {
            headers["authorization"] = `Bearer ${this.token}`;
        }
        // 合并额外的 headers
        headers = { ...headers, ...additionalHeaders };

        let proxyAgent = null;
        if (this.proxy) {
            proxyAgent = new HttpsProxyAgent(this.proxy);
        }

        const config = {
            headers: headers,
            timeout: 30000,
        };

        if (proxyAgent) {
            config.httpsAgent = proxyAgent;
        }
        return config;
    }

    async #_request(method, url, additionalHeaders = {}, data = null) {
        const config = this.#createRequestConfig(additionalHeaders);
        try {
            const response = await axios({
                method,
                url,
                ...config,
                ...(data ? { data: JSON.stringify(data) } : {})
            });
            return response;
        } catch (error) {
            // 处理 HTTP 错误
            if (error.response) {
                // 服务器返回了错误状态码
                const errMsg = `请求失败: HTTP ${error.response.status} - ${error.response.data?.message || error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            } else if (error.request) {
                // 请求已发送但没有收到响应
                const errMsg = `请求超时或无响应: ${error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            } else {
                // 请求配置出错
                const errMsg = `请求配置错误: ${error.message}`;
                logger.error(errMsg);
                throw new Error(errMsg);
            }
        }
    }

    async #sendEvent(eventName, props, address = null) {
        try {
            const url = `https://pulse.walletconnect.org/e?projectId=${this.projectId}&st=appkit&sv=react-wagmi-1.6.9`;
            const uuid = crypto.randomUUID();

            // 构建事件数据
            const eventData = {
                eventId: uuid,
                url: `https://aicraft.fun/projects/fizen`,
                domain: "aicraft.fun",
                timestamp: Date.now(),
                props: {
                    type: "track",
                    event: eventName,
                    properties: props,
                }
            };

            // 如果有地址，添加到属性中
            if (address) {
                eventData.props.address = address;
            }

            const headers = {
                "accept": "*/*",
                "content-type": "text/plain;charset=UTF-8"
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('POST', url, headers, eventData);
                    if (![200, 201, 202].includes(result.status)) {
                        throw new Error(`发送事件失败, status=${result.status}`);
                    }
                    return result;
                }
            );

            logger.info(`钱包 [${this.walletIndex}] ${this.address} 发送事件 ${eventName} 成功`);
            return response;

        } catch (error) {
            logger.error(`钱包 [${this.walletIndex}] ${this.address} 发送事件 ${eventName} 失败: ${error.message}`);
            throw error;
        }
    }

    async #postEvent() {
        try {
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始发送埋点数据`);

            const address = this.wallet.getAddress();
            const events = [
                {
                    name: "INITIALIZE",
                    props: {
                        networks: [10143],
                        defaultNetwork: {
                            id: 10143,
                            name: "Monad Testnet",
                            nativeCurrency: {
                                name: "Testnet MON Token",
                                symbol: "MON",
                                decimals: 18
                            },
                            rpcUrls: {
                                default: {
                                    http: ["https://testnet-rpc.monad.xyz"]
                                }
                            },
                            blockExplorers: {
                                default: {
                                    name: "Monad Testnet explorer",
                                    url: "https://testnet.monadexplorer.com"
                                }
                            },
                            contracts: {
                                multicall3: {
                                    address: "0xcA11bde05977b3631167028862bE2a173976CA11",
                                    blockCreated: 251449
                                }
                            },
                            testnet: true
                        },
                        projectId: this.projectId,
                        metadata: {
                            name: "AICraft",
                            description: "AICraft.fun",
                            url: "https://aicraft.fun",
                            icons: ["https://avatars.githubusercontent.com/u/179229932"]
                        },
                        featuredWalletIds: ["4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0", "8a0ee50d1f22f6651afcae7eb4253e52a3310b90af5daef78a8c4929a9bb99d4", "17a4ec96ceb34ade8e5215220df2051614aeebb832cc80ef19ddd7f33d5ba862"],
                        features: {
                            swaps: false,
                            onramp: false,
                            connectMethodsOrder: ["wallet"],
                            legalCheckbox: false
                        },
                        themeMode: "dark",
                        themeVariables: {
                            "--w3m-z-index": 9999
                        },
                        sdkVersion: "react-wagmi-1.6.9",
                        siweConfig: {
                            options: {}
                        }
                    }
                },
                {
                    name: "MODAL_OPEN",
                    props: { connected: false }
                },
                {
                    name: "SELECT_WALLET",
                    props: {
                        name: "OKX Wallet",
                        platform: "browser"
                    }
                },
                {
                    name: "MODAL_CLOSE",
                    props: { connected: true },
                    needAddress: true
                },
                {
                    name: "CONNECT_SUCCESS",
                    props: {
                        method: "browser",
                        name: "OKX Wallet"
                    },
                    needAddress: true
                }
            ];

            // 依次发送每个事件
            for (const event of events) {
                await this.#sendEvent(
                    event.name,
                    event.props,
                    event.needAddress ? address : null
                );

                // 随机延迟 1-3 秒
                // await randomSleep(1, 3);
            }
            return true;
        } catch (error) {
            logger.error(`钱包 [${this.walletIndex}] ${this.address} 发送埋点事件失败: ${error.message}`);
            throw error;
        }
    }

    async #getSignData() {
        try {
            const url = `https://api.aicraft.fun/auths/wallets/sign-in/message?address=${this.address}&type=ETHEREUM_BASED`;

            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始获取签名数据`);
            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('GET', url, headers);

                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取签名数据失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 检查响应状态
                    if (![200, 201, 202].includes(result.data.statusCode)) {
                        throw new Error(`钱包 [${this.walletIndex}] ${this.address} 获取签名数据失败: 状态码 ${result.data?.statusCode || '未知'}`);
                    }

                    // 检查数据格式
                    if (!result.data.data?.message) {
                        throw new Error(`钱包 [${this.walletIndex}] ${this.address} 获取签名数据失败: 返回数据格式错误`);
                    }

                    return result;
                }
            );

            const message = response.data.data.message;
            logger.success(`钱包 [${this.walletIndex}] ${this.address} 获取签名数据成功: ${message}`);
            return message;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取签名数据失败: ${error.message}`;
            logger.error(errMsg);
            throw new Error(errMsg);
        }
    }

    async #signIn(message) {
        try {
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始登录`);
            const address = this.wallet.getAddress();
            const signature = await this.wallet.signMessage(message);
            const url = "https://api.aicraft.fun/auths/wallets/sign-in";

            const data = {
                address: address,
                signature: signature,
                message: message,
                type: "ETHEREUM_BASED"
            };

            // 如果有本地用户数据，添加邀请码
            const userData = await this.#getLocalUserData(address);
            if (userData?.inviteByCode) {
                data.refCode = userData.inviteByCode;
            }

            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('POST', url, headers, data);

                    // 检查响应状态
                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 登录失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    if (![200, 201, 202].includes(result.data.statusCode)) {
                        throw new Error(`钱包 [${this.walletIndex}] ${this.address} 登录失败: 状态码 ${result.data?.statusCode || '未知'}`);
                    }

                    // 检查返回的 token
                    if (!result.data.data?.token) {
                        throw new Error(`钱包 [${this.walletIndex}] ${this.address} 登录失败: 返回数据格式错误`);
                    }

                    return result;
                }
            );

            // 保存 token
            this.token = response.data.data.token;
            logger.success(`钱包 [${this.walletIndex}] ${this.address} 登录成功: ${this.token}`);
            return true;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 登录失败: ${error.message}`;
            logger.error(errMsg);
            throw new Error(errMsg);
        }
    }

    async #getRemoteUserData() {
        try {
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始获取用户信息`);
            const url = 'https://api.aicraft.fun/users/me?includePresalePurchasedAmount=true';
            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('GET', url, headers);

                    // 检查响应状态
                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取用户信息失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    if (![200, 201, 202].includes(result.data.statusCode)) {
                        throw new Error(`获取用户信息失败: 状态码 ${result.data?.statusCode || '未知'}`);
                    }
                    return result;
                }
            );

            const userData = response.data.data;
            logger.success(`钱包 [${this.walletIndex}] ${this.address} 获取用户信息成功: ${JSON.stringify(userData)}`);
            return userData;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取用户信息失败: ${error.message}`;
            logger.error(errMsg);
            throw new Error(errMsg);
        }
    }

    async #getLocalUserData(address) {
        try {
            // 直接从内存中的 userDatas 获取数据
            if (this.userDatas && address) {
                return this.userDatas[address] || null;
            }
            return null;
        } catch (error) {
            logger.error(`获取本地用户数据失败: ${error.message}`);
            return null;
        }
    }

    async #referral(refCode) {
        try {
            const url = 'https://api.aicraft.fun/users/referral';
            const data = { "refCode": refCode };
            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('POST', url, headers, data);

                    // 检查响应状态
                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 填写邀请码失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 201 表示首次填写成功，200 表示已经填写过
                    if (![200, 201].includes(result.data.statusCode)) {
                        throw new Error(`填写邀请码失败: 状态码 ${result.data.statusCode}`);
                    }

                    return result;
                }
            );

            if (response && response.data && response.data.statusCode in [200, 201]) {
                logger.success(`钱包 [${this.walletIndex}] ${this.address} 填写邀请码 ${refCode} 成功`);
            }
            return true;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 填写邀请码 ${refCode} 失败: ${error.message}`;
            logger.error(errMsg);
            return false;
        }
    }

    async #getCandidate() {
        try {
            const url = 'https://api.aicraft.fun/candidates?projectID=678376133438e102d6ff5c6e';
            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('GET', url, headers);

                    // 检查响应状态
                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取项目失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 检查状态码
                    if (![200, 201].includes(result.data.statusCode)) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取项目失败: 状态码 ${result.data.statusCode}`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 检查候选人数据
                    const candidates = result.data.data;
                    if (!Array.isArray(candidates) || candidates.length === 0) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取项目失败: 项目信息为空`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }
                    return result;
                }
            );

            // 获取所有候选人并按 feedCount 降序排序
            const candidates = response.data.data
                .sort((a, b) => b.feedCount - a.feedCount)
                .slice(0, 12); // 取前12个

            // 从前12个中随机选择一个
            const randomIndex = Math.floor(Math.random() * candidates.length);
            const selectedCandidate = candidates[randomIndex];

            logger.success(`钱包 [${this.walletIndex}] ${this.address} 选择项目: ${selectedCandidate.name}(票数:${selectedCandidate.feedCount}) 进行投票`);
            return selectedCandidate;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 获取项目信息失败: ${error.message}`;
            logger.error(errMsg);
            throw new Error(errMsg);
        }
    }

    async #createFeedOrder(candidateId, walletId, refCode) {
        try {
            const url = 'https://api.aicraft.fun/feeds/orders';
            const data = {
                candidateID: candidateId,
                walletID: walletId,
                feedAmount: 1,
                chainID: "10143",
                refCode: refCode,
            };

            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('POST', url, headers, data);

                    // 检查响应状态
                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 创建投票失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 检查状态码
                    if (![200, 201].includes(result.data.statusCode)) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 创建投票失败: 状态码 ${result.data.statusCode}`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 检查订单数据
                    const orderData = result.data.data;
                    if (!orderData.order || !orderData.payment) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 创建投票失败: 返回数据格式错误`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }
                    return result;
                }
            );

            const orderData = response.data.data;
            logger.success(`钱包 [${this.walletIndex}] ${this.address} 创建投票成功`);
            return orderData;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 创建投票失败: ${error.message}`;
            logger.error(errMsg);
            throw new Error(errMsg);
        }
    }

    async #sendTransaction(payment) {
        try {
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始发送交易`);
            const { contractAddress, abi, params } = payment;

            // 检查余额
            try {
                const balance = await this.wallet.getBalance();
                const minBalance = parseEther("0.001"); // 设置最小余额阈值为 0.001 MON

                if (balance < minBalance) {
                    const message = `余额不足，当前余额: ${formatEther(balance)} MON，最小要求: ${formatEther(minBalance)} MON`;
                    logger.error(`钱包 [${this.walletIndex}] ${this.address} ${message}`);
                    return {
                        success: false,
                        data: null,
                        message: message
                    };
                }

                logger.info(`钱包 [${this.walletIndex}] ${this.address} 当前余额: ${formatEther(balance)} MON`);
            } catch (error) {
                return {
                    success: false,
                    data: null,
                    message: `检查余额失败: ${error.message}`
                };
            }

            // 修改签名方式
            const message = params.userHashedMessage;
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 签名内容: ${message}`);

            const messageBytes = ethers.getBytes(message);
            const signature = await this.wallet.signMessage(messageBytes);
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 签名结果: ${signature}`);

            const abiCoder = ethers.AbiCoder.defaultAbiCoder();
            const encodedData = abiCoder.encode(
                ['string', 'uint256', 'string', 'string', 'bytes', 'bytes'],
                [
                    params.candidateID,
                    params.feedAmount,
                    params.requestID,
                    params.requestData,
                    signature,
                    params.integritySignature
                ]
            );
            const functionSelector = '0xd48eff0c';
            const inputData = functionSelector + encodedData.slice(2);

            // 直接调用合约方法
            const tx = await retry(
                async () => {
                    try {
                        const result = await buildAndSendTransaction(
                            this.rpc,
                            this.privateKey,
                            contractAddress,
                            inputData
                        );

                        if (!result || !result.hash) {
                            throw new Error('发送交易失败: 返回数据格式错误');
                        }

                        return result;
                    } catch (error) {
                        // 处理特定的错误类型
                        if (error.message.includes('insufficient funds') || error.message.includes('insufficient balance')) {
                            logger.error(`钱包 [${this.walletIndex}] ${this.address} 余额不足，无法发送交易`);
                            return null;
                        } else if (error.message.includes('nonce too low')) {
                            logger.error(`钱包 [${this.walletIndex}] ${this.address} nonce 错误，请重试`);
                            throw new Error('nonce 错误，请重试');
                        } else if (error.message.includes('replacement fee too low')) {
                            throw new Error('gas 费用太低，无法替换交易');
                        }
                        // 其他错误直接抛出
                        throw error;
                    }
                }
            );

            // 如果交易发送失败，直接返回
            if (!tx) {
                return {
                    success: false,
                    data: null,
                    message: '交易发送失败'
                };
            }

            logger.info(`钱包 [${this.walletIndex}] ${this.address} 交易已发送，等待确认: ${tx.hash}`);

            // 等待交易确认
            const receipt = await tx.wait();

            if (!receipt || typeof receipt.status === 'undefined') {
                return {
                    success: false,
                    data: null,
                    message: '交易确认失败: 返回数据格式错误'
                };
            }

            if (receipt.status === 1) {
                logger.success(`钱包 [${this.walletIndex}] ${this.address} 交易确认成功: ${receipt.hash}`);
                return {
                    success: true,
                    data: receipt.hash,
                    message: '交易确认成功'
                };
            }

            return {
                success: false,
                data: receipt,
                message: '交易执行失败'
            };

        } catch (error) {
            const errMsg = `发送交易失败: ${error.message}`;
            logger.error(`钱包 [${this.walletIndex}] ${this.address} ${errMsg}`);
            return {
                success: false,
                data: null,
                message: errMsg
            };
        }
    }

    async #confirmOrder(orderId, txHash, refCode) {
        try {
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始投票`);
            const url = `https://api.aicraft.fun/feeds/orders/${orderId}/confirm`;
            const data = {
                transactionHash: txHash,
                refCode: refCode,
            };

            const headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
            };

            // 使用 retry 函数包装请求
            const response = await retry(
                async () => {
                    const result = await this.#_request('POST', url, headers, data);

                    // 检查响应状态
                    if (!result.data) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 确认订单失败: 无响应数据`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    // 检查状态码
                    if (![200, 201].includes(result.data.statusCode)) {
                        const errMsg = `钱包 [${this.walletIndex}] ${this.address} 确认订单失败: 状态码 ${result.data.statusCode}`;
                        logger.error(errMsg);
                        throw new Error(errMsg);
                    }

                    return result;
                }
            );

            logger.success(`钱包 [${this.walletIndex}] ${this.address} 投票成功: ${orderId}`);
            return response.data.data;

        } catch (error) {
            const errMsg = `钱包 [${this.walletIndex}] ${this.address} 投票失败: ${error.message}`;
            logger.error(errMsg);
            throw new Error(errMsg);
        }
    }

    async #login() {
        // 发送埋点事件
        await this.#postEvent();

        // 获取签名数据
        const message = await this.#getSignData();
        // 登录
        await this.#signIn(message);

        return true;
    }

    async #invite(remoteUserData) {
        try {
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始填写邀请码`);
            // 如果没有邀请人，尝试使用邀请码
            if (!remoteUserData.invitedBy) {
                // 从 userDatas 中收集所有有效的邀请码
                let inviteCodes = Object.values(this.userDatas)
                    .map(user => user.inviteCode)
                    .filter(code => code && code.trim() !== '');

                // 如果没有有效的邀请码，使用默认邀请码
                if (inviteCodes.length === 0) {
                    inviteCodes = defaultInviteCodes;
                }

                // 随机选择一个邀请码
                const randomIndex = Math.floor(Math.random() * inviteCodes.length);
                const selectedCode = inviteCodes[randomIndex];

                // 使用邀请码
                const success = await this.#referral(selectedCode);
                if (success) {
                    logger.success(`钱包 [${this.walletIndex}] ${this.address} 填写邀请码成功: ${selectedCode}`);
                    remoteUserData.invitedBy = { refCode: selectedCode };
                } else {
                    logger.error(`钱包 [${this.walletIndex}] ${this.address} 填写邀请码失败`);
                    return false;
                }
            }

            // 准备要保存的用户数据
            const walletId = remoteUserData.wallets[0]._id;
            const inviteCode = remoteUserData.refCode;
            const inviteByCode = remoteUserData.invitedBy.refCode;

            // 无论是否有邀请人，都保存用户数据
            await this.#saveUserData(walletId, inviteCode, inviteByCode);
            return true;

        } catch (error) {
            logger.error(`处理邀请失败: ${error.message}`);
            return false;
        }
    }

    async #saveUserData(walletId, inviteCode, inviteByCode) {
        try {
            // 检查用户是否已存在
            const existingUser = await this.#getLocalUserData(this.address);
            if (existingUser) {
                return;
            }
            const localUserData = {
                index: this.walletIndex,
                walletId: walletId,
                address: this.address,
                inviteCode: inviteCode,
                inviteByCode: inviteByCode
            };

            // 将对象转换为数组
            const csvData = [localUserData];  // 包装成数组

            // 更新 CSV 文件
            await CSV.append(this.csvPath, csvData);

            // 更新内存中的数据
            this.userDatas[this.address] = localUserData;
            logger.success(`钱包 [${this.walletIndex}] ${this.address} 保存用户数据成功`);
        } catch (error) {
            logger.error(`保存用户数据失败: ${error.message}`);
        }
    }

    async #vote(leftTimes) {
        try {
            const userData = await this.#getLocalUserData(this.address);
            const refCode = userData.inviteByCode;

            const balance = parseFloat(formatEther(await this.wallet.getBalance()));

            // 根据余额决定投票次数范围
            // let minTimes, maxTimes;
            // if (balance < 1) {
            //     minTimes = 1;
            //     maxTimes = 3;
            // } else if (balance >= 1 && balance < 3) {
            //     minTimes = 3;
            //     maxTimes = 5;
            // } else if (balance >= 3 && balance < 5) {
            //     minTimes = 5;
            //     maxTimes = 10;
            // } else {
            //     minTimes = 15;
            //     maxTimes = 20;
            // }
            let minTimes = 1, maxTimes = 3;

            // 随机生成投票次数，但不超过剩余次数
            const randomTimes = Math.floor(Math.random() * (maxTimes - minTimes + 1)) + minTimes;
            const times = Math.min(randomTimes, leftTimes);

            logger.info(`钱包 [${this.walletIndex}] ${this.address} 当前余额 ${balance.toFixed(4)} MON`);
            logger.info(`钱包 [${this.walletIndex}] ${this.address} 今日剩余投票次数 ${leftTimes} 次, 本次将投票 ${times} 次`);

            for (let i = 0; i < times; i++) {
                logger.info(`钱包 [${this.walletIndex}] ${this.address} 开始第 ${i + 1}/${times} 次投票`);
                try {
                    const candidate = await this.#getCandidate();
                    if (!candidate) {
                        logger.error(`钱包 [${this.walletIndex}] ${this.address} 获取投票项目失败`);
                        return false;
                    }

                    // 创建订单
                    const { order, payment } = await this.#createFeedOrder(candidate._id, userData.walletId, refCode);
                    // 发送交易
                    const result = await this.#sendTransaction(payment);
                    if (!result.success) {
                        logger.error(result.message);
                        return false;
                    }
                    const txHash = result.data;
                    await randomSleep(1, 3);

                    // 确认订单
                    await this.#confirmOrder(order._id, txHash, refCode);
                    logger.success(`钱包 [${this.walletIndex}] ${this.address} 完成第 ${i + 1}/${times} 次投票`);
                    const delay = getRandomValue(2, 4);
                    logger.info(`等待 ${delay} 秒后继续下一个投票`);
                    await sleep(delay);
                } catch (error) {
                    logger.error(`钱包 [${this.walletIndex}] ${this.address} 第 ${i + 1}/${times} 次投票失败: ${error.message}`);
                }
            }
            return true;
        } catch (error) {
            logger.error(`投票失败: ${error.message}`);
            return false;
        }
    }

    async task() {

        // 登录注册
        const login = await this.#login();
        if (!login) {
            logger.error(`钱包 [${this.walletIndex} ${this.address} 登录失败`);
            return false;
        }

        // 获取用户信息
        const remoteUserData = await this.#getRemoteUserData();
        if (!remoteUserData) {
            logger.error(`钱包 [${this.walletIndex}] ${this.address} 获取用户信息失败`);
            return false;
        }
        if (remoteUserData.todayFeedCount === 0) {
            logger.warn(`钱包 [${this.walletIndex}] ${this.address} 今日已投满`);
            return true;
        }
        await randomSleep(2, 4);
        // 处理邀请
        const invite = await this.#invite(remoteUserData);
        if (!invite) {
            logger.error(`钱包 [${this.walletIndex}] ${this.address} 处理邀请失败`);
            return false;
        }
        // 投票
        return await this.#vote(remoteUserData.todayFeedCount);
    }

}

export default Aicraft;
