const { ethers } = require('ethers');
const { formatEther, parseEther } = ethers.utils;
const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');
const crypto = require('crypto');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const XLSX = require('xlsx');
const path = require('path');
const config = require('../../config.json');
const logger = require('../../logger.js');



class MonadFaucet {
    constructor() {
        this.RPC_URL = 'https://testnet-rpc.monad.xyz/';
        this.provider = new ethers.providers.JsonRpcProvider(this.RPC_URL);
        this.proxyUrl = 'http://user-sub.songsu-type-dc:<EMAIL>:1467';
        this.THREAD_COUNT = 2; // 并发数量
    }

    // 读取钱包信息
    readWallets() {
        try {
            const workbook = XLSX.readFile(path.join(__dirname, '../../wallet.xlsx'), {
                cellText: false,
                raw: true
            });

            const sheet = workbook.Sheets[workbook.SheetNames[0]];
            const wallets = XLSX.utils.sheet_to_json(sheet);

            return wallets.map(wallet => ({
                address: wallet.address.toString(),
                pk: wallet.pk.toString()
            }));
        } catch (error) {
            logger.error('读取钱包文件失败:', error);
            throw error;
        }
    }

    generateVisitorId() {
        const components = {
            timestamp: Date.now(),
            random: Math.random(),
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            platform: 'Windows',
            language: 'zh-CN',
            colorDepth: '24',
            resolution: '2560x1440',
            availableResolution: '2560x1400',
            timezone: 'Asia/Shanghai',
            timezoneOffset: -480,
            sessionStorage: true,
            localStorage: true,
            indexedDb: true,
            cpuCores: 16,
            deviceMemory: 32,
            uniqueId: crypto.randomBytes(16).toString('hex')
        };

        const componentsString = Object.entries(components)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}:${value}`)
            .join('|');

        return crypto.createHash('md5').update(componentsString).digest('hex');
    }

    async getCaptchaToken() {
        try {
            logger.info('开始获取验证码token...');

            const URL = "http://api.nocaptcha.io/api/wanda/recaptcha/universal";
            const data = {
                sitekey: '6LcItOMqAAAAAF9ANohQEN4jGOjHRxU8f5MNJZHu',
                referer: 'https://testnet.monad.xyz',
                size: 'normal',
                title: 'Monad Testnet: Test, Play and Build on Monad Testnet',
                // action: 'drip_request',
                proxy: this.proxyUrl
            };

            const headers = {
                'User-Token': '750a8585-f584-4eac-9703-313699454aed',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };

            const proxyAgent = new HttpsProxyAgent(this.proxyUrl);

            const response = await axios({
                method: 'post',
                url: URL,
                data: data,
                headers: headers,
                httpsAgent: proxyAgent,
                timeout: 60000,
                validateStatus: function (status) {
                    return status >= 200 && status < 500;
                }
            });

            if (!response.data) {
                throw new Error('API响应为空');
            }

            if (response.data.status !== 1) {
                throw new Error(`API错误: ${response.data.msg || '未知错误'}`);
            }

            // 从正确的路径获取token
            const token = response.data.data?.token;
            if (!token) {
                logger.error('API响应结构:', response.data);
                throw new Error('API响应中没有token');
            }

            logger.success('成功获取验证码token');
            logger.debug('Token前30位:', token.substring(0, 30) + '...');

            return token;
        } catch (error) {
            if (error.response) {
                logger.error('验证码API错误:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data,
                    headers: error.response.headers
                });
            } else if (error.request) {
                logger.error('验证码请求失败:', {
                    message: error.message,
                    code: error.code
                });
            } else {
                logger.error('验证码获取错误:', error.message);
            }
            throw new Error('获取验证码失败: ' + error.message);
        }
    }

    async claim(address, maxRetries = 3) {
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 每次尝试都重新生成所有必要数据
                logger.info(`开始第 ${retryCount + 1} 次尝试领水 (地址: ${address})`);

                // 重新生成visitorId
                const visitorId = this.generateVisitorId();
                logger.info('生成新的visitorId');

                // 重新获取验证码token
                logger.info('重新获取验证码token...');
                const recaptchaToken = await this.getCaptchaToken();

                logger.info('发送领水请求...');
                const response = await axios({
                    method: 'post',
                    url: 'https://testnet.monad.xyz/api/claim',
                    data: {
                        address: address,
                        recaptchaToken: recaptchaToken,
                        visitorId: visitorId
                    },
                    headers: {
                        'authority': 'testnet.monad.xyz',
                        'accept': '*/*',
                        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'content-type': 'application/json',
                        'origin': 'https://testnet.monad.xyz',
                        'referer': 'https://testnet.monad.xyz/',
                        'sec-ch-ua': '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    },
                    httpsAgent: new HttpsProxyAgent(this.proxyUrl),
                    timeout: 30000
                });

                logger.success(`领水成功: ${response.data.message || '成功'}`);
                return response.data;
            } catch (error) {
                if (error.response) {
                    const errorMessage = error.response.data?.message || error.response.data || error.message;

                    if (error.response.status === 400) {
                        retryCount++;
                        if (retryCount < maxRetries) {
                            logger.warn(`领水请求失败 (400): ${errorMessage}`);
                            logger.warn(`准备第 ${retryCount + 1} 次完整重试...`);
                            // 随机延迟3-5秒后重试
                            const delay = Math.floor(Math.random() * 2000) + 3000;
                            logger.info(`等待 ${delay/1000} 秒后重试...`);
                            await new Promise(resolve => setTimeout(resolve, delay));
                            continue;
                        } else {
                            // 达到最大重试次数
                            logger.error(`地址 ${address} 领水失败，已重试 ${maxRetries} 次: ${errorMessage}`);
                        }
                    } else {
                        // 非400错误直接报错
                        logger.error(`领水请求失败 (${error.response.status}): ${errorMessage}`);
                    }
                } else if (error.request) {
                    logger.error(`请求发送失败: ${error.message}`);
                } else {
                    logger.error(`领水错误: ${error.message}`);
                }

                throw error;
            }
        }
    }

    // Worker线程处理函数
    async workerProcess(wallets) {
        for (const wallet of wallets) {
            try {
                logger.info(`处理钱包: ${wallet.address}`);
                await this.claim(wallet.address);
                // 每个钱包处理完后等待随机时间
                const delay = Math.floor(Math.random() * 5000) + 5000;
                await new Promise(resolve => setTimeout(resolve, delay));
            } catch (error) {
                logger.error(`处理钱包 ${wallet.address} 失败，继续处理下一个钱包`);
                continue;
            }
        }
    }

    // 分配钱包到不同线程
    distributeWallets(wallets, threadCount) {
        const walletsPerThread = Math.ceil(wallets.length / threadCount);
        const distribution = [];

        for (let i = 0; i < threadCount; i++) {
            const start = i * walletsPerThread;
            const end = Math.min(start + walletsPerThread, wallets.length);
            if (start < wallets.length) {
                distribution.push(wallets.slice(start, end));
            }
        }

        return distribution;
    }

    // 启动多线程处理
    async startWorkers() {
        try {
            const wallets = this.readWallets();
            logger.info(`读取到 ${wallets.length} 个钱包`);

            const distribution = this.distributeWallets(wallets, this.THREAD_COUNT);
            const workers = [];

            for (let i = 0; i < distribution.length; i++) {
                const worker = new Worker(__filename, {
                    workerData: {
                        wallets: distribution[i],
                        threadId: i
                    }
                });

                worker.on('message', (message) => {
                    logger.info(`Worker ${i}: ${message}`);
                });

                worker.on('error', (error) => {
                    logger.error(`Worker ${i} 错误:`, error);
                });

                worker.on('exit', (code) => {
                    if (code !== 0) {
                        logger.error(`Worker ${i} 异常退出，退出码: ${code}`);
                    }
                });

                workers.push(worker);
            }

            // 等待所有worker完成
            await Promise.all(workers.map(worker => {
                return new Promise((resolve) => {
                    worker.on('exit', resolve);
                });
            }));

            logger.success('所有钱包处理完成');
        } catch (error) {
            logger.error('启动workers失败:', error);
            throw error;
        }
    }
}

// Worker线程执行逻辑
if (!isMainThread) {
    const { wallets, threadId } = workerData;
    const faucet = new MonadFaucet();

    (async () => {
        try {
            await faucet.workerProcess(wallets);
            parentPort.postMessage(`线程 ${threadId} 完成处理`);
        } catch (error) {
            logger.error(`线程 ${threadId} 处理失败:`, error);
        }
    })();
}

// 主函数
async function main() {
    try {
        logger.info('程序开始执行...');
        const faucet = new MonadFaucet();
        await faucet.startWorkers();
    } catch (error) {
        logger.error('程序执行失败:', error);
    } finally {
        process.exit();
    }
}

// 如果是主线程，则执行main函数
if (isMainThread) {
    main().catch(error => {
        logger.error('未处理的错误:', error);
        process.exit(1);
    });
}

module.exports = MonadFaucet;


