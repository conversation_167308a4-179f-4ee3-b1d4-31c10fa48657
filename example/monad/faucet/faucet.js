import { ethers } from 'ethers';
import axios from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { randomBytes, createHash } from 'crypto';
import { resolveFromModule } from '../../../base/tools/path.js';
import logger from "../../../base/tools/logger.js";
import CSV from "../../../base/tools/csv.js";
import fakeUa from "fake-useragent";
import crypto from 'crypto';
import {
    parseTOMLConfig,
    filterWalletsByIndex,
    getRandomValue,
    sleep
} from "../../../base/tools/common.js";

function getCSVFilePath() {
    return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

class MonadFaucet {
    constructor() {
        // 加载配置
        const configPath = resolveFromModule(import.meta.url, "../config.toml");
        const config = parseTOMLConfig(configPath);
        const monadConfig = config.monad;

        this.RPC_URL = monadConfig.rpc || 'https://testnet-rpc.monad.xyz/';
        this.provider = new ethers.JsonRpcProvider(this.RPC_URL);
        this.proxyUrl = config.PROXY_URL;

        // 配置延迟参数
        this.minDelaySeconds = config.MIN_DELAY_SECONDS || 5;
        this.maxDelaySeconds = config.MAX_DELAY_SECONDS || 15;
        this.nocaptchaKey = config.NOCAPTCHA_KEY;
    }

    getPlatformFromUserAgent(userAgent) {
        if (userAgent.includes('Windows')) {
            return 'Windows';
        } else if (userAgent.includes('Macintosh')) {
            return 'macOS';
        } else if (userAgent.includes('Linux')) {
            return 'Linux';
        } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
            return 'iOS';
        } else if (userAgent.includes('Android')) {
            return 'Android';
        }
        return 'Windows'; // 默认返回Windows
    }

    generateRandomFingerprint() {
        // 生成随机但真实的 User-Agent
        const userAgent = fakeUa();
        const platform = this.getPlatformFromUserAgent(userAgent);

        const regions = [
            {
                language: 'zh-CN',
                timezone: 'Asia/Shanghai',
                timezoneOffset: -480
            },
            {
                language: 'ru-RU',
                timezone: 'Europe/Moscow',
                timezoneOffset: -180
            },
            {
                language: 'en-SG',
                timezone: 'Asia/Singapore',
                timezoneOffset: -480
            },
            {
                language: 'fr-FR',
                timezone: 'Europe/Paris',
                timezoneOffset: -60
            },
            {
                language: 'de-DE',
                timezone: 'Europe/Berlin',
                timezoneOffset: -60
            },
            {
                language: 'ja-JP',
                timezone: 'Asia/Tokyo',
                timezoneOffset: -540
            },
            {
                language: 'ko-KR',
                timezone: 'Asia/Seoul',
                timezoneOffset: -540
            },
            {
                language: 'en-GB',
                timezone: 'Europe/London',
                timezoneOffset: 0
            }
        ];

        // 随机选择地区
        const regionInfo = regions[Math.floor(Math.random() * regions.length)];

        // 根据平台生成合适的分辨率
        let resolutions;
        if (platform === 'Windows' || platform === 'Macintosh') {
            resolutions = [
                '1920x1080',
                '2560x1440',
                '3840x2160',
                '1366x768',
                '1440x900',
                '1536x864'
            ];
        } else if (platform === 'iOS' || platform === 'Android') {
            resolutions = [
                '390x844',  // iPhone 12/13/14
                '428x926',  // iPhone 12/13/14 Pro Max
                '360x800',  // Common Android
                '412x915',  // Pixel 6
                '360x780'   // Common Android
            ];
        } else {
            resolutions = ['1920x1080'];
        }

        const resolution = resolutions[Math.floor(Math.random() * resolutions.length)];
        // 简化可用分辨率的计算，固定减去任务栏高度
        const [width, height] = resolution.split('x').map(Number);
        const availableResolution = platform === 'Windows'
            ? `${width}x${height - 40}`  // Windows 任务栏默认高度
            : platform === 'macOS'
                ? `${width}x${height - 28}`  // macOS 菜单栏默认高度
                : resolution;  // 其他平台保持一致

        // 根据平台设置合理的硬件参数
        let cpuCores, deviceMemory;
        if (platform === 'Windows' || platform === 'Macintosh') {
            cpuCores = [4, 6, 8, 12, 16][Math.floor(Math.random() * 5)];
            deviceMemory = [8, 16, 32][Math.floor(Math.random() * 3)];
        } else {
            cpuCores = [4, 6, 8][Math.floor(Math.random() * 3)];
            deviceMemory = [4, 6, 8][Math.floor(Math.random() * 3)];
        }

        const components = {
            timestamp: Date.now(),
            random: Math.random(),
            userAgent: userAgent,
            platform: platform,
            language: regionInfo.language,
            colorDepth: '24',
            resolution: resolution,
            availableResolution: availableResolution,
            timezone: regionInfo.timezone,
            timezoneOffset: regionInfo.timezoneOffset,
            sessionStorage: true,
            localStorage: true,
            indexedDb: true,
            cpuCores: cpuCores,
            deviceMemory: deviceMemory,
            uniqueId: randomBytes(16).toString('hex')
        };


        const componentsString = Object.entries(components)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}:${value}`)
        .join('|');

        return crypto.createHash('md5').update(componentsString).digest('hex');
    }

    async getCaptchaToken() {
        try {
            logger.info('开始获取验证码token...');

            const URL = "http://api.nocaptcha.io/api/wanda/recaptcha/universal";
            const data = {
                sitekey: '6LcItOMqAAAAAF9ANohQEN4jGOjHRxU8f5MNJZHu',
                referer: 'https://testnet.monad.xyz',
                size: 'normal',
                title: 'Monad Testnet: Test, Play and Build on Monad Testnet',
                proxy: this.proxyUrl
            };

            const headers = {
                'User-Token': this.nocaptchaKey,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };

            const proxyAgent = new HttpsProxyAgent(this.proxyUrl);

            const response = await axios({
                method: 'post',
                url: URL,
                data: data,
                headers: headers,
                httpsAgent: proxyAgent,
                timeout: 60000,
                validateStatus: function (status) {
                    return status >= 200 && status < 500;
                }
            });

            if (!response.data) {
                throw new Error('API响应为空');
            }

            if (response.data.status !== 1) {
                throw new Error(`API错误: ${response.data.msg || '未知错误'}`);
            }

            // 从正确的路径获取token
            const token = response.data.data?.token;
            if (!token) {
                logger.error('API响应结构:', response.data);
                throw new Error('API响应中没有token');
            }

            logger.success('成功获取验证码token');
            logger.info('Token前30位:', token.substring(0, 30) + '...');

            return token;
        } catch (error) {
            if (error.response) {
                logger.error('验证码API错误:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data,
                    headers: error.response.headers
                });
            } else if (error.request) {
                logger.error('验证码请求失败:', {
                    message: error.message,
                    code: error.code
                });
            } else {
                logger.error('验证码获取错误:', error.message);
            }
            throw new Error('获取验证码失败: ' + error.message);
        }
    }

    async claim(address, maxRetries = 3) {
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 每次尝试都重新生成所有必要数据
                logger.info(`开始第 ${retryCount + 1} 次尝试领水 (地址: ${address})`);

                // 重新生成visitorId
                const visitorId = this.generateRandomFingerprint();
                logger.info('生成新的visitorId');

                // 重新获取验证码token
                logger.info('重新获取验证码token...');
                const recaptchaToken = await this.getCaptchaToken();

                logger.info('发送领水请求...');
                const response = await axios({
                    method: 'post',
                    url: 'https://testnet.monad.xyz/api/claim',
                    data: {
                        address: address,
                        recaptchaToken: recaptchaToken,
                        visitorId: visitorId
                    },
                    headers: {
                        'authority': 'testnet.monad.xyz',
                        'accept': '*/*',
                        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'content-type': 'application/json',
                        'origin': 'https://testnet.monad.xyz',
                        'referer': 'https://testnet.monad.xyz/',
                        'sec-ch-ua': '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin',
                        'user-agent': fakeUa()
                    },
                    httpsAgent: new HttpsProxyAgent(this.proxyUrl),
                    timeout: 30000
                });

                logger.success(`领水成功: ${response.data.message || '成功'}`);
                return response.data;
            } catch (error) {
                if (error.response) {
                    const errorMessage = error.response.data?.message || error.response.data || error.message;

                    if (error.response.status === 400) {
                        retryCount++;
                        if (retryCount < maxRetries) {
                            logger.warn(`领水请求失败 (400): ${errorMessage}`);
                            logger.warn(`准备第 ${retryCount + 1} 次完整重试...`);
                            // 随机延迟3-5秒后重试
                            const delay = Math.floor(Math.random() * 2000) + 3000;
                            logger.info(`等待 ${delay/1000} 秒后重试...`);
                            await new Promise(resolve => setTimeout(resolve, delay));
                            continue;
                        } else {
                            // 达到最大重试次数
                            logger.error(`地址 ${address} 领水失败，已重试 ${maxRetries} 次: ${errorMessage}`);
                        }
                    } else {
                        // 非400错误直接报错
                        logger.error(`领水请求失败 (${error.response.status}): ${errorMessage}`);
                    }
                } else if (error.request) {
                    logger.error(`请求发送失败: ${error.message}`);
                } else {
                    logger.error(`领水错误: ${error.message}`);
                }

                throw error;
            }
        }
    }

    async processFaucet(wallet) {
        const { private_key, index, address } = wallet;

        if (!address?.trim()) {
            logger.warn(`钱包 ${index} 的地址为空，跳过处理`);
            return {
                success: false,
                message: "地址为空"
            };
        }

        try {
            logger.info(`开始处理钱包 ${index}: ${address}`);
            const result = await this.claim(address);

            logger.success(`钱包 ${index} 领水成功`);
            return {
                success: true,
                data: result
            };
        } catch (error) {
            logger.error(`钱包 ${index} 领水失败: ${error.message}`);
            return {
                success: false,
                message: error.message
            };
        }
    }
}

async function main() {
    try {
        const args = process.argv.slice(2);
        const indexArg = args[0];

        logger.info('程序开始执行...');
        const faucet = new MonadFaucet();

        // 加载钱包
        const walletList = await CSV.read(getCSVFilePath());
        const filteredWallets = filterWalletsByIndex(indexArg, walletList);
        const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
        const totalWallets = shuffledWallets.length;

        // 统计变量
        let successCount = 0;
        let failedCount = 0;
        let failedIndexes = [];

        for (let [currentIndex, wallet] of shuffledWallets.entries()) {
            logger.info(`处理第 ${currentIndex + 1}/${totalWallets} 个钱包`);

            const result = await faucet.processFaucet(wallet);

            if (result.success) {
                successCount++;
            } else {
                failedCount++;
                failedIndexes.push(wallet.index);
            }

            // 如果不是最后一个钱包，则等待随机时间
            if (currentIndex < totalWallets - 1) {
                const waitTime = getRandomValue(faucet.minDelaySeconds, faucet.maxDelaySeconds);
                logger.info(`等待 ${waitTime} 秒后处理下一个钱包...`);
                await sleep(waitTime);
            }
        }

        // 输出统计结果
        const summaryMsg = failedCount > 0
            ? `共处理 ${totalWallets} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个，失败钱包编号: ${failedIndexes.join(", ")}`
            : `共处理 ${totalWallets} 个钱包，成功 ${successCount} 个，失败 ${failedCount} 个`;

        logger.success(summaryMsg);

    } catch (error) {
        logger.error('程序执行失败:', error);
    }
}

// 执行主函数
main().catch(error => {
    logger.error('未处理的错误:', error);
    process.exit(1);
});

export default MonadFaucet;


