import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { retry, getRandomValue, sleep } from "../../../base/tools/common.js";

/**
 * @typedef {Object} ChainConfig
 * @property {string} rpc - RPC 节点地址
 * @property {string} explorer_url - 区块浏览器地址
 * @property {string} address - 合约地址
 * @property {number} chainId - 链 ID
 * @property {string} currency - 货币符号
 * @property {number} service_fee - 服务费
 */

// Orbiter配置常量
const ORBITER_CONFIG = {
  MAX_WAIT_TIME: 600,  // 最大等待时间（秒）
  BRIDGE_ADDRESS: "******************************************",
  GAS_LIMIT: 21000,    // 固定 gas limit
  GAS_BUFFER: 1.5,     // gas 缓冲倍数
  BALANCE_DECIMALS: {   // 余额随机小数位
    MIN: 2,
    MAX: 3
  },
  CHECK_INTERVAL: 10,   // 检查资金到账间隔（秒）
};

// 错误码常量
const ERROR_CODES = {
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  BRIDGE_AMOUNT_TOO_LOW: 'BRIDGE_AMOUNT_TOO_LOW',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  FUNDS_TIMEOUT: 'FUNDS_TIMEOUT'
};

export default class Orbiter {
  #index;
  #proxy;
  #bridgeContractAddress;
  #bridgeMaxTime;
  #fromChainConfig;
  #toChainConfig;
  #fromProvider;
  #fromWallet;
  #toProvider;
  #toWallet;
  #fromContract;
  #toContract;

  /**
   * Orbiter跨链桥构造函数
   * @param {number} index - 钱包索引
   * @param {string} proxy - 代理地址
   * @param {string} privateKey - 私钥
   * @param {ChainConfig} fromChainConfig - 源链配置
   * @param {ChainConfig} toChainConfig - 目标链配置
   * @param {string} bridgeContractAddress - 桥合约地址
   * @param {number} bridgeMaxTime - 最大等待时间
   */
  constructor(
    index,
    proxy,
    privateKey,
    fromChainConfig,
    toChainConfig,
    bridgeContractAddress = ORBITER_CONFIG.BRIDGE_ADDRESS,
    bridgeMaxTime = ORBITER_CONFIG.MAX_WAIT_TIME
  ) {
    // 参数验证
    if (!privateKey) throw new Error('Private key is required');
    if (!fromChainConfig?.rpc) throw new Error('From chain RPC is required');
    if (!toChainConfig?.rpc) throw new Error('To chain RPC is required');

    this.#index = index;
    this.#proxy = proxy;
    this.#bridgeContractAddress = bridgeContractAddress;
    this.#bridgeMaxTime = bridgeMaxTime;
    this.#fromChainConfig = fromChainConfig;
    this.#toChainConfig = toChainConfig;
    this.#fromContract = null;
    this.#toContract = null;

    // 初始化providers和wallet
    this.#fromProvider = new ethers.JsonRpcProvider(fromChainConfig.rpc);
    this.#fromWallet = new ethers.Wallet(privateKey, this.#fromProvider);

    this.#toProvider = new ethers.JsonRpcProvider(toChainConfig.rpc);
    this.#toWallet = new ethers.Wallet(privateKey, this.#toProvider);

    // 初始化合约
    const tokenABI = ["function balanceOf(address owner) view returns (uint256)"];
    if (fromChainConfig.contract_address) {
      this.#fromContract = new ethers.Contract(fromChainConfig.contract_address, tokenABI, this.#fromWallet);
    }

    if (toChainConfig.contract_address) {
      this.#toContract = new ethers.Contract(toChainConfig.contract_address, tokenABI, this.#toWallet);
    }
  }

  async #getGasParams() {
    const [block, feeData] = await Promise.all([
      this.#fromProvider.getBlock("latest"),
      this.#fromProvider.getFeeData()
    ]);

    const baseFee = block.baseFeePerGas;
    const maxPriorityFee = feeData.maxPriorityFeePerGas;
    const gasBuffer = ORBITER_CONFIG.GAS_BUFFER;

    return {
      maxFeePerGas: ((baseFee + maxPriorityFee) * BigInt(Math.floor(gasBuffer * 10))) / BigInt(10),
      maxPriorityFeePerGas: (maxPriorityFee * BigInt(Math.floor(gasBuffer * 10))) / BigInt(10)
    };
  }

  async #waitForFunds(initialBalance) {
    const maxAttempts = Math.floor(this.#bridgeMaxTime / ORBITER_CONFIG.CHECK_INTERVAL);
    logger.info(`[${this.#index}] 等待资金到账 (最大时长: ${this.#bridgeMaxTime}秒)...`);

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const currentBalance = await this.getToChainTokenBalance();

        if (currentBalance > initialBalance) {
          logger.success(`[${this.#index}] 资金到账!`);
          return true;
        }

        const remainingTime = (maxAttempts - attempt) * ORBITER_CONFIG.CHECK_INTERVAL;
        logger.info(
          `[${this.#index}] 等待资金到账... (尝试次数: ${attempt + 1}/${maxAttempts}, 剩余时长: ${remainingTime}秒)`
        );
        await sleep(ORBITER_CONFIG.CHECK_INTERVAL);
      } catch (e) {
        logger.error(`[${this.#index}] 检查token余额失败: ${e.message}`);
        await sleep(ORBITER_CONFIG.CHECK_INTERVAL);
      }
    }

    logger.warn(`[${this.#index}] 等待资金到账超时, 最大时长: ${this.#bridgeMaxTime}秒`);
    return false;
  }

  async #getBridgeAmount(balanceWei, gasParams) {
    // console.log(`原有金额: ${ethers.formatEther(balanceWei)}`);
    const gasCostWei = gasParams.maxFeePerGas * BigInt(ORBITER_CONFIG.GAS_LIMIT);
    const gasCostWithBuffer = (gasCostWei * BigInt(150)) / BigInt(100);

    // 计算服务费（加上50%缓冲）
    const serviceFeeWei = (ethers.parseEther(this.#fromChainConfig.service_fee.toString()) * BigInt(150)) / BigInt(100);
    balanceWei = balanceWei - gasCostWithBuffer - serviceFeeWei;
    if (balanceWei < 0) {
      return 0n;
    }

    const random = getRandomValue(
      ORBITER_CONFIG.BALANCE_DECIMALS.MIN,
      ORBITER_CONFIG.BALANCE_DECIMALS.MAX
    );
    let balance = Math.floor(
      parseFloat(ethers.formatEther(balanceWei)) * Math.pow(10, random)
    ) / Math.pow(10, random);

    balanceWei = ethers.parseEther(balance.toString());
    // console.log(`跨链金额: ${ethers.formatEther(balanceWei)}`);
    return balanceWei <= 0 ? 0n : balanceWei;
  }

  async getFromChainNativeBalance() {
    const balance = await retry(() => this.#fromProvider.getBalance(this.#fromWallet.address), 3, 2000);
    return ethers.formatEther(balance);
  }

  async getToChainNativeBalance() {
    const balance = await retry(() => this.#toProvider.getBalance(this.#toWallet.address), 3, 2000);
    return ethers.formatEther(balance);
  }

  /**
   * 获取目标链代币余额
   * @returns {Promise<string>} 目标链代币余额
   */
  async getToChainTokenBalance() {
    if (this.#toContract) {
      const balance = await retry(() => this.#toContract.balanceOf(this.#toWallet.address), 3, 2000);
      return ethers.formatEther(balance);
    } else {
      return await this.getToChainNativeBalance();
    }
  }

  async bridge(amount = 0) {
    try {
      // 获取余额和gas参数
      const [balanceWei, gasParams] = await Promise.all([
        this.#fromProvider.getBalance(this.#fromWallet.address),
        retry(() => this.#getGasParams())
      ]);

      // 计算跨链金额
      const amountWei = amount === 0
        ? await this.#getBridgeAmount(balanceWei, gasParams)
        : ethers.parseEther(amount.toString());

      if (amountWei <= 0) {
        return {
          success: false,
          code: ERROR_CODES.BRIDGE_AMOUNT_TOO_LOW,
          message: `[${this.#index}] 跨链金额不足`
        };
      }

      // 计算总金额并检查余额
      const serviceFeeWei = ethers.parseEther(this.#fromChainConfig.service_fee.toString());
      const bridgeAmountWei = amountWei + serviceFeeWei;

      if (balanceWei < bridgeAmountWei) {
        return {
          success: false,
          code: ERROR_CODES.INSUFFICIENT_BALANCE,
          message: `[${this.#index}] 余额不足`
        };
      }

      logger.info(
        `[${this.#index}] ${this.#fromChainConfig.currency}余额: ${ethers.formatEther(balanceWei)} ` +
        `准备跨链金额: ${ethers.formatEther(bridgeAmountWei)} ${this.#fromChainConfig.currency}`
      );

      // 获取初始余额
      const initialToBalance = await retry(() =>
        this.getToChainTokenBalance()
      );

      // 发送交易
      const transaction = {
        to: this.#bridgeContractAddress,
        value: bridgeAmountWei,
        chainId: this.#fromChainConfig.chainId,
        type: 0,
        gasLimit: ORBITER_CONFIG.GAS_LIMIT
      };

      const tx = await retry(async () => await this.#fromWallet.sendTransaction(transaction), 3, 3000);
      logger.info(`[${this.#index}] 跨链交易已发送, 等待确认...`);

      const receipt = await retry(async () => await tx.wait(), 3, 3000);
      const txHashStr = tx.hash.startsWith("0x") ? tx.hash.slice(2) : tx.hash;

      if (receipt.status !== 1) {
        return {
          success: false,
          code: ERROR_CODES.TRANSACTION_FAILED,
          message: `[${this.#index}] 跨链失败`
        };
      }

      logger.success(
        `[${this.#index}] 跨链交易成功, TX: ${this.#fromChainConfig.explorer_url}${txHashStr}`
      );

      // 等待资金到账
      const isFundsArrived = await this.#waitForFunds(initialToBalance);
      return isFundsArrived ? {
        success: true,
        message: `[${this.#index}] 跨链成功,并且资金已到账 TX: ${this.#fromChainConfig.explorer_url}${txHashStr}`,
        data: {
          txHash: txHashStr,
          amountWei: amountWei,
          amount: ethers.formatEther(amountWei)
        }
      } : {
        success: false,
        code: ERROR_CODES.FUNDS_TIMEOUT,
        message: `[${this.#index}] 跨链失败, TX: ${this.#fromChainConfig.explorer_url}${txHashStr}`
      };

    } catch (error) {
      logger.error(`[${this.#index}] 跨链异常: ${error.message}`);
      throw error;
    }
  }
}
