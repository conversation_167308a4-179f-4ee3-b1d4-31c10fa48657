import logger from "../../../base/tools/logger.js";
import { filterWalletsByIndex, parseTOMLConfig, getRandomValue, sleep } from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import CSV from "../../../base/tools/csv.js";
import Orbiter from "./orbiter.js";
import SecureEncryption from "../../../base/tools/secure-encryption.js";

/**
 * @typedef {Object} ChainConfig
 * @property {string} rpc - RPC节点
 * @property {string} explorer_url - 浏览器地址
 * @property {string} address - 合约地址
 * @property {number} chainId - 链ID
 * @property {string} currency - 货币符号
 * @property {number} service_fee - 服务费
 */

// 配置常量
const CONFIG = {
  MIN_DELAY: 10,  // 最小延迟（秒）
  MAX_DELAY: 30,  // 最大延迟（秒）
  MIN_AMOUNT: 0.01,  // 最小跨链数量
  MAX_AMOUNT: 0.1    // 最大跨链数量
};

/**
 * 获取配置文件路径
 * @returns {string} 配置文件路径
 */
function getConfigPath() {
  return resolveFromModule(import.meta.url, "./config.toml");
}

/**
 * 获取CSV文件路径
 * @returns {string} CSV文件路径
 */
function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

/**
 * 主函数
 */
async function main() {
  try {
    // 解析命令行参数
    const args = process.argv.slice(2);
    const [indexArg, fromChain = "sepolia", toChain = "monad", amount = 0] = args;

    if (!indexArg) {
      throw new Error("请提供钱包索引范围，例如: 1-10");
    }

    // 读取配置
    const config = parseTOMLConfig(getConfigPath());
    const fromChainConfig = config[fromChain];
    const toChainConfig = config[toChain];

    logger.info(`开始跨链任务，源链: ${fromChain}, 目标链: ${toChain}, 跨链金额: ${amount}`);

    // 读取钱包列表
    const walletList = await CSV.read(getCSVFilePath());
    const filteredWallets = filterWalletsByIndex(indexArg, walletList);

    // 随机打乱钱包顺序
    const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

    logger.info(`总共处理 ${shuffledWallets.length} 个钱包`);

    // 统计
    let successCount = 0;
    let failedCount = 0;

    const secureEncryption = new SecureEncryption();

    // 处理每个钱包
    for (let i = 0; i < shuffledWallets.length; i++) {
      const wallet = shuffledWallets[i];
      const { index, address, proxy } = wallet;

      // 解密私钥
      let private_key = wallet.private_key;
      const isEncrypted = await secureEncryption.isEncrypted(private_key);
      if (isEncrypted) {
        private_key = await secureEncryption.decrypt(private_key);
      }

      logger.info(`\n开始处理第 ${i + 1}/${shuffledWallets.length} 个钱包`);
      logger.info(`钱包信息 - 序号: ${index}, 地址: ${address}`);

      if (!private_key?.trim()) {
        logger.warn(`钱包 ${index} 私钥为空，跳过`);
        continue;
      }

      try {

        // 创建Orbiter实例
        const orbiter = new Orbiter(
          index,
          proxy,
          private_key,
          fromChainConfig,
          toChainConfig
        );

        // 获取当前余额
        const balance = await orbiter.getFromChainNativeBalance();
        logger.info(`当前余额: ${balance} ${fromChainConfig.currency}`);

        // 确定跨链金额
        const bridgeAmount = amount;
        if (bridgeAmount > balance) {
          logger.error(`钱包 ${index} 余额不足，跳过`);
          continue;
        }

        // 执行跨链
        const result = await orbiter.bridge(bridgeAmount);
        if (result.success) {
          successCount++;
        } else {
          failedCount++;
          logger.error(`钱包 ${index} 跨链失败: ${result.message}`);
        }

      } catch (error) {
        failedCount++;
        logger.error(`钱包 ${index} 处理异常: ${error.message}`);
      }

      // 不是最后一个钱包则添加延迟
      if (i < shuffledWallets.length - 1) {
        const delay = getRandomValue(CONFIG.MIN_DELAY, CONFIG.MAX_DELAY);
        logger.info(`等待 ${delay} 秒后处理下一个钱包...`);
        await sleep(delay);
      }
    }

    // 打印统计信息
    logger.info("\n========== 任务统计 ==========");
    logger.info(`总计处理: ${shuffledWallets.length} 个钱包`);
    logger.info(`成功: ${successCount} 个`);
    logger.info(`失败: ${failedCount} 个`);
    logger.info("==============================\n");

  } catch (error) {
    logger.error("程序执行错误:", error);
    process.exit(1);
  }
}

// 执行脚本
// 示例: node example/monad/orbiter/task_bridge.js 1-10 0.05
main().catch(console.error);



