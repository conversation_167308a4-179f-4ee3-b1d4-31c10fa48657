import logger from "../../../base/tools/logger.js";
import { filterWalletsByIndex, parseTOMLConfig, getRandomValue, sleep } from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import CSV from "../../../base/tools/csv.js";
import Orbiter from "./orbiter.js";
import AmbientDex from "../ambient/ambient.js";
import { ethers } from "ethers";
import SecureEncryption from "../../../base/tools/secure-encryption.js";

/**
 * @typedef {Object} ChainConfig
 * @property {string} rpc - RPC节点
 * @property {string} explorer_url - 浏览器地址
 * @property {string} address - 合约地址
 * @property {number} chainId - 链ID
 * @property {string} currency - 货币符号
 * @property {number} service_fee - 服务费
 */

// 配置常量
const CONFIG = {
  MIN_DELAY: 10,  // 最小延迟（秒）
  MAX_DELAY: 30,  // 最大延迟（秒）
  MIN_AMOUNT: 0.01,  // 最小跨链数量
  MAX_AMOUNT: 0.1    // 最大跨链数量
};

/**
 * 获取配置文件路径
 * @returns {string} 配置文件路径
 */
function getConfigPath() {
  return resolveFromModule(import.meta.url, "./config.toml");
}

/**
 * 获取CSV文件路径
 * @returns {string} CSV文件路径
 */
function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../eth_wallet.csv");
}

// 根据rpc获取余额
async function getBalance(rpc, address) {
  const provider = new ethers.JsonRpcProvider(rpc);
  const balance = await provider.getBalance(address);
  return ethers.formatEther(balance);
}

/**
 * 主函数
 */
async function main() {
  try {
    // 解析命令行参数
    const args = process.argv.slice(2);
    const [indexArg] = args;

    if (!indexArg) {
      throw new Error("请提供钱包索引范围，例如: 1-10");
    }

    // 读取配置
    const config = parseTOMLConfig(getConfigPath());
    const sepoliaConfig = config["sepolia"];
    const lineaSepoliaConfig = config["linea_sepolia"];
    const monadConfig = config["monad"];

    // 读取钱包列表
    const walletList = await CSV.read(getCSVFilePath());
    const filteredWallets = filterWalletsByIndex(indexArg, walletList);

    // 随机打乱钱包顺序
    const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

    logger.info(`总共处理 ${shuffledWallets.length} 个钱包`);

    const secureEncryption = new SecureEncryption();

    // 处理每个钱包
    for (let i = 0; i < shuffledWallets.length; i++) {
      const wallet = shuffledWallets[i];
      const { index, address, proxy } = wallet;

      // 解密私钥
      let private_key = wallet.private_key;
      const isEncrypted = await secureEncryption.isEncrypted(private_key);
      if (isEncrypted) {
        private_key = await secureEncryption.decrypt(private_key);
      }

      logger.info(`\n开始处理第 ${i + 1}/${shuffledWallets.length} 个钱包`);
      logger.info(`钱包信息 - 序号: ${index}, 地址: ${address}`);

      if (!private_key?.trim()) {
        logger.warn(`钱包 ${index} 私钥为空，跳过`);
        continue;
      }

      // 获取linea sepolia 跟 sepolia 余额
      const [
        lineaSepoliaBalance,
        sepoliaBalance,
      ] = await Promise.all([
        getBalance(lineaSepoliaConfig.rpc, address),
        getBalance(sepoliaConfig.rpc, address),
      ]);

      logger.info(`[${index}] 当前余额 -  LINEA_SEPOLIA:${lineaSepoliaBalance}  SEPOLIA:${sepoliaBalance}`);

      // 如果linea sepolia余额大于0.5e，先跨链到sepolia
      try {
        if (ethers.parseEther(lineaSepoliaBalance) >= ethers.parseEther("0.1")) {
          const orbiter = new Orbiter(
            index,
            proxy,
            private_key,
            lineaSepoliaConfig,
            sepoliaConfig
          );

          const result = await orbiter.bridge();
          if (result.success) {
            await sleep(getRandomValue(5, 20));
          } else {
            logger.error(`钱包 ${index} 从 linea sepolia 跨链到 sepolia 失败: ${result.message}`);
          }
        }
      } catch (error) {
        logger.error(`钱包 ${index} 从 linea sepolia 跨链到 sepolia 失败: ${error.message}`);
      }

      try {
        // 如果sepolia余额大于0.5e，跨链到monad
        if (ethers.parseEther(sepoliaBalance) >= ethers.parseEther("0.1")) {
          const orbiter = new Orbiter(
            index,
            proxy,
            private_key,
            sepoliaConfig,
            monadConfig
          );

          const result = await orbiter.bridge();
          if (result.success) {
            await sleep(getRandomValue(5, 20));
          } else {
            logger.error(`钱包 ${index} 从 sepolia 跨链到 monad 失败: ${result.message}`);
          }
        }
      } catch (error) {
        logger.error(`钱包 ${index} 从 sepolia 跨链到 monad 失败: ${error.message}`);
      }

      // 如果monad eth 余额大于0.5e，把 eth swap 成 monad
      try {
        const ambient = new AmbientDex(index, private_key, monadConfig.rpc, proxy);
        const decimalBalance = await ambient.getDecimalBalanceWithToken("ETH");
        const ethBalance = ambient.convertTokenToDecimal("ETH", decimalBalance)
        if (ethers.parseEther(ethBalance) >= ethers.parseEther("0.1")) {
          let nativeBalanceWei = await ambient.getNativeBalance();
          let nativeBalance = ethers.formatEther(nativeBalanceWei);
          // swap前
          logger.info(`[${index}] 跨链前余额 -  MON:${nativeBalance}  ETH:${ethBalance}`);
          await ambient.swapToken2Mon("ETH", decimalBalance);
          // swap后
          await sleep(getRandomValue(3, 5));
          const afterBalance = await ambient.getBalanceWithToken("ETH");
          nativeBalanceWei = await ambient.getNativeBalance();
          nativeBalance = ethers.formatEther(nativeBalanceWei);
          logger.info(`[${index}]跨链后余额 - MON: ${nativeBalance}  ETH: ${afterBalance}`);
        }

      } catch (error) {
        logger.error(`钱包 ${index} 把eth swap 成 monad 失败: ${error.message}`);
      }

      // 不是最后一个钱包则添加延迟
      if (i < shuffledWallets.length - 1) {
        const delay = getRandomValue(CONFIG.MIN_DELAY, CONFIG.MAX_DELAY);
        logger.info(`等待 ${delay} 秒后处理下一个钱包...`);
        await sleep(delay);
      }
    }

  } catch (error) {
    logger.error("程序执行错误:", error);
    process.exit(1);
  }
}

// 执行脚本
// 示例: node example/monad/orbiter/task_bridge_swap.js 1-10
main().catch(console.error);



