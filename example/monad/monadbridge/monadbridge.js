import { ethers } from "ethers";
import { getRandomValue, retry, sleep } from "../../../base/tools/common.js";
import logger from "../../../base/tools/logger.js";


/**
 * @typedef {Object} ChainConfig
 * @property {string} rpc - RPC 节点地址
 * @property {string} explorer_url - 区块浏览器地址
 * @property {number} chainId - 链 ID
 * @property {string} currency - 货币符号
 */

// WormholeBridge配置常量
const WORMHOLE_CONFIG = {
  MAX_WAIT_TIME: 300,  // 最大等待时间（秒）
  BRIDGE_ADDRESS: "******************************************", // 虫洞桥合约地址
  GAS_LIMIT: 375000,   // 虫洞跨链的gas limit
  GAS_BUFFER: 1.5,     // gas 缓冲倍数
  BALANCE_DECIMALS: {  // 余额随机小数位
    MIN: 2,
    MAX: 3
  },
  CHECK_INTERVAL: 50,  // 检查资金到账间隔（秒）
  MONAD_CHAIN_ID: 48,  // Monad在虫洞中的链ID
};


// 错误码常量
const ERROR_CODES = {
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  BRIDGE_AMOUNT_TOO_LOW: 'BRIDGE_AMOUNT_TOO_LOW',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  FUNDS_TIMEOUT: 'FUNDS_TIMEOUT',
  INVALID_PARAMETERS: 'INVALID_PARAMETERS'
};

// WETH合约地址
const WETH_CONTRACT_ADDRESS = '******************************************';

const WETH_ABI = [
  {
    "constant": true,
    "inputs": [{ "name": "_owner", "type": "address" }],
    "name": "balanceOf",
    "outputs": [{ "name": "balance", "type": "uint256" }],
    "type": "function"
  }
];


export default class WormholeBridge {
  #index;
  #proxy;
  #bridgeContractAddress;
  #bridgeMaxTime;
  #fromChainConfig;
  #toChainConfig;
  #fromProvider;
  #fromWallet;
  #toProvider;
  #toWallet;
  #bridgeContract;

  /**
   * Wormhole跨链桥构造函数
   * @param {number} index - 钱包索引
   * @param {string} proxy - 代理地址
   * @param {string} privateKey - 私钥
   * @param {ChainConfig} fromChainConfig - 源链配置
   * @param {ChainConfig} toChainConfig - 目标链配置
   * @param {string} bridgeContractAddress - 桥合约地址
   * @param {number} bridgeMaxTime - 最大等待时间
   */
  constructor(
    index,
    proxy,
    privateKey,
    fromChainConfig,
    toChainConfig,
    bridgeContractAddress = WORMHOLE_CONFIG.BRIDGE_ADDRESS,
    bridgeMaxTime = WORMHOLE_CONFIG.MAX_WAIT_TIME
  ) {
    // 参数验证
    if (!privateKey) throw new Error('Private key is required');
    if (!fromChainConfig?.rpc) throw new Error('From chain RPC is required');
    if (!toChainConfig?.rpc) throw new Error('To chain RPC is required');

    this.#index = index;
    this.#proxy = proxy;
    this.#bridgeContractAddress = bridgeContractAddress;
    this.#bridgeMaxTime = bridgeMaxTime;
    this.#fromChainConfig = fromChainConfig;
    this.#toChainConfig = toChainConfig;

    // 初始化providers和wallet
    this.#fromProvider = new ethers.JsonRpcProvider(fromChainConfig.rpc);
    this.#fromWallet = new ethers.Wallet(privateKey, this.#fromProvider);

    this.#toProvider = new ethers.JsonRpcProvider(toChainConfig.rpc);
    this.#toWallet = new ethers.Wallet(privateKey, this.#toProvider);

  }

  async #getGasParams() {
    const [block, feeData] = await Promise.all([
      this.#fromProvider.getBlock("latest"),
      this.#fromProvider.getFeeData()
    ]);

    const baseFee = block.baseFeePerGas;
    const maxPriorityFee = feeData.maxPriorityFeePerGas;
    const gasBuffer = WORMHOLE_CONFIG.GAS_BUFFER;

    return {
      maxFeePerGas: ((baseFee + maxPriorityFee) * BigInt(Math.floor(gasBuffer * 10))) / BigInt(10),
      maxPriorityFeePerGas: (maxPriorityFee * BigInt(Math.floor(gasBuffer * 10))) / BigInt(10)
    };
  }

  async #waitForFunds(initialBalance) {
    const maxAttempts = Math.floor(this.#bridgeMaxTime / WORMHOLE_CONFIG.CHECK_INTERVAL);
    logger.info(`[${this.#index}] 等待资金到账 (最大时长: ${this.#bridgeMaxTime}秒)...`);

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const currentBalance = await this.getwWETHBalance();

        if (parseFloat(currentBalance) > parseFloat(initialBalance)) {
          logger.success(`[${this.#index}] 资金到账!`);
          return true;
        }

        const remainingTime = (maxAttempts - attempt) * WORMHOLE_CONFIG.CHECK_INTERVAL;
        logger.info(
          `[${this.#index}] 等待资金到账... (尝试次数: ${attempt + 1}/${maxAttempts}, 剩余时长: ${remainingTime}秒)`
        );
        await sleep(WORMHOLE_CONFIG.CHECK_INTERVAL);
      } catch (e) {
        logger.error(`[${this.#index}] 检查余额失败: ${e.message}`);
        await sleep(WORMHOLE_CONFIG.CHECK_INTERVAL);
      }
    }

    logger.warn(`[${this.#index}] 等待资金到账超时, 最大时长: ${this.#bridgeMaxTime}秒`);
    return false;
  }

  async #getBridgeAmount(balanceWei, gasParams) {
    // console.log(`原有金额: ${ethers.formatEther(balanceWei)}`);
    const gasCostWei = gasParams.maxFeePerGas * BigInt(WORMHOLE_CONFIG.GAS_LIMIT);
    const gasCostWithBuffer = (gasCostWei * BigInt(150)) / BigInt(100);

    // 计算服务费（加上50%缓冲）
    const serviceFeeWei = (ethers.parseEther(this.#fromChainConfig.service_fee.toString()) * BigInt(150)) / BigInt(100);
    balanceWei = balanceWei - gasCostWithBuffer - serviceFeeWei;
    if (balanceWei < 0) {
      return 0n;
    }

    const random = getRandomValue(
      WORMHOLE_CONFIG.BALANCE_DECIMALS.MIN,
      WORMHOLE_CONFIG.BALANCE_DECIMALS.MAX
    );
    let balance = Math.floor(
      parseFloat(ethers.formatEther(balanceWei)) * Math.pow(10, random)
    ) / Math.pow(10, random);

    balanceWei = ethers.parseEther(balance.toString());
    // console.log(`跨链金额: ${ethers.formatEther(balanceWei)}`);
    return balanceWei <= 0 ? 0n : balanceWei;
  }

  async getFromChainNativeBalance() {
    const balance = await retry(() => this.#fromProvider.getBalance(this.#fromWallet.address), 3, 2000);
    return ethers.formatEther(balance);
  }

  async getToChainNativeBalance() {
    const balance = await retry(() => this.#toProvider.getBalance(this.#toWallet.address), 3, 2000);
    return ethers.formatEther(balance);
  }



  /**
   * 获取 Monad 链上的 wWETH 余额
   * @param {string} address - 要查询的地址，默认为当前钱包地址
   * @returns {Promise<string>} - 格式化后的 WETH 余额
   */
  async getwWETHBalance(address = null) {
    try {
      logger.info(`[${this.#index}] 开始查询 Monad 链上的 WETH 余额...`);

      // 使用提供的地址或默认使用当前钱包地址
      const targetAddress = address || this.#toWallet.address;

      // 创建 WETH 合约实例
      const wethContract = new ethers.Contract(
        WETH_CONTRACT_ADDRESS,
        WETH_ABI,
        this.#toProvider
      );

      // 调用 balanceOf 方法查询余额
      const balance = await retry(
        () => wethContract.balanceOf(targetAddress),
        3,
        2000
      );

      const formattedBalance = ethers.formatEther(balance);
      logger.info(`[${this.#index}] Monad 链上地址 ${targetAddress} 的 wWETH 余额: ${formattedBalance}`);

      return formattedBalance;
    } catch (error) {
      logger.error(`[${this.#index}] 查询 WETH 余额失败: ${error.message}`);
      throw new Error(`查询 WETH 余额失败: ${error.message}`);
    }
  }



  buildCrosschainData(amount, recipient) {
    try {
      // 使用固定金额以匹配交易
      const amountInWei = ethers.parseEther(amount);

      // 方法签名
      const methodId = "0xe5d486a5";

      // 移除接收地址的 '0x' 前缀
      const cleanRecipient = recipient.toLowerCase().replace('0x', '');

      // 对参数进行编码
      // 1. 金额
      const encodedAmount = ethers.zeroPadValue(ethers.toBeHex(amountInWei), 32).slice(2);

      // 2. 源链ID
      const encodedSourceChain = ethers.zeroPadValue(
        ethers.toBeHex(WORMHOLE_CONFIG.MONAD_CHAIN_ID),
        32
      ).slice(2);

      // 3. Gas限制: 目标链上执行所需的gas - 修复这里!
      // 使用硬编码的 375000，而不是目标链ID
      const gasLimit = 375000; // 正确的 Gas 限制值
      const encodedGasLimit = ethers.zeroPadValue(
        ethers.toBeHex(gasLimit),
        32
      ).slice(2);

      // 4. 接收地址
      const encodedRecipient = '000000000000000000000000' + cleanRecipient;

      // 拼接所有数据
      const inputData = methodId +
        encodedAmount +
        encodedSourceChain +
        encodedGasLimit +
        encodedRecipient;

      return inputData;
    } catch (error) {
      logger.error(`构建跨链交易数据失败: ${error.message}`);
      throw new Error(`构建跨链交易数据失败: ${error.message}`);
    }
  }



  /**
   * 构建并发送以太坊交易的通用方法
   * @param {string} contractAddress - 合约地址
   * @param {string} inputData - 交易输入数据
   * @param {string|number} value - 交易金额 wei
   * @returns {Promise<string>} 交易哈希
   */
  async buildAndSendTransaction(contractAddress, inputData, value = '0') {
    try {
      const wallet = this.#fromWallet;
      const fromAddress = wallet.address;

      // const txValue = ethers.parseUnits(value.toString(), 'wei');

      // 构建交易对象
      const transaction = {
        from: fromAddress,
        to: contractAddress,
        value: value,
        data: inputData,
      };

      const tx = await wallet.sendTransaction(transaction);

      return tx;
    } catch (error) {
      throw new Error(`交易发送失败: ${error.message}`);
    }
  }

  async bridge(amount = 0) {
    try {
      // 获取余额和gas参数
      const [balanceWei, gasParams] = await Promise.all([
        this.#fromProvider.getBalance(this.#fromWallet.address),
        retry(() => this.#getGasParams())
      ]);

      // 不带服务费金额
      let amountWithoutFee = 0;
      // 不带服务费金额wei
      let amountWithoutFeeWei = 0;
      // 总金额
      let totalAmount = 0;
      // 总金额wei
      let totalAmountWei = 0;

      if (amount != 0) {
        // 如果传入金额不为0，则计算总金额
        amountWithoutFee = amount;
        amountWithoutFeeWei = ethers.parseEther(amountWithoutFee.toString());
      } else {
        // 如果传入金额为0，则计算跨链金额
        amountWithoutFeeWei = await this.#getBridgeAmount(balanceWei, gasParams);
        amountWithoutFee = Number(ethers.formatEther(amountWithoutFeeWei));
      }

      // 服务费
      const serviceFee = this.#fromChainConfig.service_fee;
      const serviceFeeWei = ethers.parseEther(serviceFee.toString());
      totalAmountWei = amountWithoutFeeWei + serviceFeeWei;
      totalAmount = Number(ethers.formatEther(totalAmountWei));
      if (totalAmountWei <= 0) {
        return {
          success: false,
          code: ERROR_CODES.BRIDGE_AMOUNT_TOO_LOW,
          message: `[${this.#index}] 跨链金额不足`
        };
      }

      // 检查余额是否足够
      if (balanceWei < totalAmountWei) {
        return {
          success: false,
          code: ERROR_CODES.INSUFFICIENT_BALANCE,
          message: `[${this.#index}] 余额不足`
        };
      }

      // 获取初始余额
      const initialToBalance = await this.getwWETHBalance();
      const txData = this.buildCrosschainData(amountWithoutFee.toString(), this.#toWallet.address);
      const txValue = totalAmountWei.toString();

      logger.info(`[${this.#index}] 跨链交易金额: ${totalAmount} ETH`);

      // 验证数据不为空
      if (!txData || txData === '0x') {
        throw new Error('交易数据构造失败，数据为空');
      }

      const tx = await this.buildAndSendTransaction(this.#bridgeContractAddress, txData, txValue);
      logger.info(`[${this.#index}] 跨链交易已发送, ${this.#fromChainConfig.explorer_url}${tx.hash.slice(2)}`);

      // 等待交易确认
      const receipt = await tx.wait();
      if (receipt.status === 0) {
        return {
          success: false,
          code: ERROR_CODES.TRANSACTION_FAILED,
          message: `[${this.#index}] 交易失败`
        };
      }

      // 等待资金到账
      logger.info(`[${this.#index}] 交易已确认，等待资金到达目标链...`);

      // 等待15分钟
      await new Promise(resolve => setTimeout(resolve, 60000 * 15));

      const isFundsArrived = await this.#waitForFunds(initialToBalance);
      return isFundsArrived ? {
        success: true,
        message: `[${this.#index}] 跨链成功,并且资金已到账 TX: ${this.#fromChainConfig.explorer_url}${tx.hash.slice(2)}`,
        data: {
          txHash: tx.hash.slice(2),
          amountWei: totalAmountWei,
          amount: ethers.formatEther(totalAmountWei)
        }
      } : {
        success: false,
        code: ERROR_CODES.FUNDS_TIMEOUT,
        message: `[${this.#index}] 跨链失败, TX: ${this.#fromChainConfig.explorer_url}${tx.hash.slice(2)}`
      };

    } catch (error) {
      logger.error(`[${this.#index}] 跨链异常: ${error.message}`);
      throw error;
    }
  }
}
