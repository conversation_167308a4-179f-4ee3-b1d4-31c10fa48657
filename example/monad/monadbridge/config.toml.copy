# 最大并发数
MAX_CONCURRENT = 1

# 重试延迟时间（毫秒）
RETRY_DELAY = 3000

# 随机延迟范围（毫秒）
RANDOM_DELAY = 2000

# 代理地址
PROXY_URL = ""

# 最小延迟时间（秒）
MIN_DELAY_SECONDS = 10
# 最大延迟时间（秒）
MAX_DELAY_SECONDS = 30

# 每个task之间的间隔 最小10秒，最大30秒
TASK_INTERVAL_MIN_SECONDS = 10
TASK_INTERVAL_MAX_SECONDS = 30

# MonadBridge合约地址
MONAD_BRIDGE_ADDRESS = "******************************************"

# MonadWrappedETH Token 合约地址
MONAD_WETH_ADDRESS = ""



[monad]
chainId = 10143
currency = "MON"
contract_address = "******************************************"
rpc = "https://testnet-rpc.monad.xyz"
explorer_url = "https://testnet.monadexplorer.com/tx/"
service_fee = 0
max_fee = 1000


[sepolia]
chainId = 11155111
currency = "ETH"
contract_address = ""
rpc = "https://ethereum-sepolia-rpc.publicnode.com"
explorer_url = "https://sepolia.etherscan.io/tx/0x"
service_fee = 0.003625
fee = 1000


[linea_sepolia]
chainId = 59141
currency = "ETH"
contract_address = ""
rpc = "https://rpc.sepolia.linea.build"
explorer_url = "https://sepolia.lineascan.build/tx/0x"
service_fee = 0.010000000000009526
fee = 1000
