import TOML from "@iarna/toml";
import axios from "axios";
import { ethers } from "ethers";
import fs from "fs";
import { HttpsProxyAgent } from "https-proxy-agent";
import { getUA } from "../../base/tools/fake_useragent.js";

import { filterWalletsByIndex, getRandomValue, sleep } from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const tomlConfig = TOML.parse(fs.readFileSync(configPath, "utf-8"));
const PROXY_URL = tomlConfig.PROXY_URL;

import SecureEncryption from "../../base/tools/secure-encryption.js";

const MAX_CONCURRENT = tomlConfig.MAX_CONCURRENT; // 最大并发数
const MIN_DELAY_SECONDS = tomlConfig.MIN_DELAY_SECONDS; // 最小延迟时间（秒）
const MAX_DELAY_SECONDS = tomlConfig.MAX_DELAY_SECONDS; // 最大延迟时间（秒）

// 线程池类
class ThreadPool {
  constructor(maxThreads) {
    this.maxThreads = maxThreads;
    this.running = new Set();
  }

  async add(task) {
    // 使用 while 循环确保真正等待任务完成
    while (this.running.size >= this.maxThreads) {
      try {
        // 等待任意一个任务完成
        const finishedTask = await Promise.race([...this.running]);
        // 等待一会，避免立即开始下一个任务
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('任务执行失败:', error);
      }
    }

    let promise;
    promise = (async () => {
      try {
        const result = await task();
        return result;
      } finally {
        this.running.delete(promise);
      }
    })();

    this.running.add(promise);
    return promise;
  }
}

/**
 * 获取跨链交易数据
 * @param {*} userAddress 用户地址
 * @param {*} solAddress 目标地址
 * @param {*} proxyUrl 代理地址
 * @param {*} amountInWei 金额
 * @param {*} chainName 链名称
 * @returns
 */
async function getTransactionData(userAddress, toAddress, proxyUrl, amount, chainName) {
  // 获取源链和目标链配置
  const fromChainConfig = tomlConfig[chainName];
  const toChainConfig = tomlConfig["eth"];

  if (!fromChainConfig || !toChainConfig) {
    throw new Error(`链配置错误: ${chainName} 或 eth 未找到`);
  }

  const fromChainId = Number(fromChainConfig.chainId);
  const toChainId = Number(toChainConfig.chainId);

  if (!proxyUrl) {
    proxyUrl = PROXY_URL;
  }

  const headers = {
    "User-Agent": getUA(),
    "Content-Type": "application/json",
  };

  try {
    const amountStr = amount.toString();

    const response = await axios.post(
      "https://api.relay.link/quote",
      {
        user: userAddress,
        originChainId: fromChainId,
        destinationChainId: toChainId,
        amount: amountStr,
        destinationCurrency: "******************************************",
        originCurrency: "******************************************",
        recipient: toAddress,
        referrer: "relay.link/swap",
        tradeType: "EXACT_INPUT",
        useExternalLiquidity: false,
      },
      {
        headers: headers,
        httpsAgent: new HttpsProxyAgent(proxyUrl),
        timeout: 30000,
      },
    );
    return {
      data: response.data.steps[0].items[0].data,
      amount: amountStr,
    };
  } catch (error) {
    logger.error(`获取交易数据失败: ${error.message}`);
    throw error;
  }
}

/**
 * 获取跨链金额
 * @param {*} userAddress 用户地址
 * @param {*} chainName 链名称
 * @param {*} index 索引
 * @returns
 */
async function getBridgeAmount(userAddress, chainName, index) {
  const chainConfig = tomlConfig[chainName];
  if (!chainConfig) {
    throw new Error(`不支持的链名称: ${chainName}`);
  }

  const provider = new ethers.JsonRpcProvider(chainConfig.rpc);
  const balance = await provider.getBalance(userAddress);
  const formatBalance = ethers.formatEther(balance);

  logger.info(`账户: ${index} ${chainName} 余额: ${formatBalance} ETH`);

  // 转账limit 暂时写了中间值 500000n
  const gasLimit = 500000n;
  const maxFeePerGas = ethers.parseUnits("0.1", "gwei");
  const estimatedGasCost = gasLimit * maxFeePerGas;

  // 计算可用余额（总余额减去预留的 gas 费用）
  const availableBalance = balance - estimatedGasCost;

  // 如果可用余额小于0，返回false
  if (availableBalance <= 0n) {
    logger.error(`余额不足支付 gas | 地址: ${userAddress} | 余额: ${formatBalance} ETH`);
    return false;
  }

  return availableBalance;
}

/**
 * 获取当前网络Gas价格信息
 * @param {ethers.Provider} provider
 * @returns {Promise<{baseFee: string, maxFeePerGas: string, maxPriorityFeePerGas: string}>}
 */
const getGasPrice = async (provider, multiplier = 1.1) => {
  try {
    // 获取最新区块
    const block = await provider.getBlock("latest");
    // 获取基础费用
    const baseFee = block.baseFeePerGas;

    // 小费
    const maxPriorityFeePerGas = ethers.parseUnits("0.1", "gwei");
    // 建议的maxFeePerGas（基础费用 * multiplier + 小费）
    const multiplierBigInt = BigInt(Math.floor(multiplier * 1000)) / 1000n;
    const maxFeePerGas = baseFee * multiplierBigInt + maxPriorityFeePerGas;

    return {
      baseFee: ethers.formatUnits(baseFee, "gwei"),
      maxPriorityFeePerGas: ethers.formatUnits(maxPriorityFeePerGas, "gwei"),
      maxFeePerGas: ethers.formatUnits(maxFeePerGas, "gwei"),
    };
  } catch (error) {
    logger.error("获取Gas价格失败:", error.message);
    throw error;
  }
};

/**
 * 诊断交易失败原因
 * @param {string} txHash - 交易哈希
 * @param {ethers.Provider} provider - Provider实例
 */
const diagnoseTxFailure = async (txHash, provider) => {
  try {
    logger.info("\n=== 交易失败诊断 ===");

    // 1. 检查交易状态
    const tx = await provider.getTransaction(txHash);
    if (!tx) {
      logger.info("交易状态: 未找到交易，可能未被节点接收");
      return;
    }

    // 2. 获取当前网络状态和费用数据
    const [currentBlock, feeData] = await Promise.all([
      provider.getBlock("latest"),
      provider.getFeeData(),
    ]);

    // 3. 详细的交易信息
    logger.info("\n=== 交易详情 ===");
    logger.info(`交易哈希: ${txHash}`);
    logger.info(`发送地址: ${tx.from}`);
    logger.info(`接收地址: ${tx.to}`);
    logger.info(`交易金额: ${ethers.formatEther(tx.value)} ETH`);
    logger.info(`Gas限制: ${tx.gasLimit.toString()}`);
    logger.info(`Nonce: ${tx.nonce}`);

    // 4. Gas费用对比
    logger.info("\n=== Gas费用对比 ===");
    logger.info("当前网络费用:");
    logger.info(`- 基础费用: ${ethers.formatUnits(feeData.gasPrice || "0", "gwei")} Gwei`);
    logger.info(`- 建议最大费用: ${ethers.formatUnits(feeData.maxFeePerGas || "0", "gwei")} Gwei`);
    logger.info(
      `- 建议优先费用: ${ethers.formatUnits(feeData.maxPriorityFeePerGas || "0", "gwei")} Gwei`,
    );

    logger.info("\n交易设置费用:");
    logger.info(`- 最大费用: ${ethers.formatUnits(tx.maxFeePerGas || tx.gasPrice, "gwei")} Gwei`);
    if (tx.maxPriorityFeePerGas) {
      logger.info(`- 优先费用: ${ethers.formatUnits(tx.maxPriorityFeePerGas, "gwei")} Gwei`);
    }

    // 5. 余额检查
    const balance = await provider.getBalance(tx.from);
    const estimatedGasCost = tx.gasLimit * (tx.maxFeePerGas || tx.gasPrice);
    const totalCost = tx.value + estimatedGasCost;

    logger.info("\n=== 余额信息 ===");
    logger.info(`当前余额: ${ethers.formatEther(balance)} ETH`);
    logger.info(`预估Gas成本: ${ethers.formatEther(estimatedGasCost)} ETH`);
    logger.info(`总需花费: ${ethers.formatEther(totalCost)} ETH`);

    // 6. 问题分析
    logger.info("\n=== 可能原因分析 ===");
    if (tx.maxFeePerGas && tx.maxFeePerGas < feeData.maxFeePerGas) {
      logger.info("- Gas价格过低：交易设置的Gas价格低于当前网络建议价格");
      logger.info(
        `  建议将maxFeePerGas提高到至少 ${ethers.formatUnits(feeData.maxFeePerGas, "gwei")} Gwei`,
      );
    }

    if (balance < totalCost) {
      logger.info("- 余额不足：账户余额不足以支付交易总成本");
      logger.info(`  还需要额外 ${ethers.formatEther(totalCost - balance)} ETH`);
    }

    // 7. 检查Nonce
    const currentNonce = await provider.getTransactionCount(tx.from);
    if (tx.nonce < currentNonce) {
      logger.info("- Nonce过低：交易的Nonce低于当前账户Nonce");
      logger.info(`  交易Nonce: ${tx.nonce}, 当前账户Nonce: ${currentNonce}`);
    }

    // 8. 网络状态
    logger.info("\n=== 网络状态 ===");
    logger.info(`当前区块: ${currentBlock.number}`);
    logger.info(`区块时间: ${new Date(currentBlock.timestamp * 1000).toLocaleString()}`);
  } catch (error) {
    logger.error("诊断过程出错:", error);
  }
};

/**
 * 跨链到 EVM 链
 * 目前是把 from 链的 所有 ETH 跨链到 EVM 链
 * @param {*} address 地址
 * @param {*} privateKey 私钥
 * @param {*} index 索引
 * @param {*} proxyUrl 代理地址
 * @param {*} chainName 链名称
 * @param {*} amount 转账金额
 * @returns
 */
async function bridgeToEVM(address, privateKey, index, proxyUrl, chainName, amount) {
  try {
    const chainConfig = tomlConfig[chainName];
    if (!chainConfig) {
      throw new Error(`不支持的链名称: ${chainName}`);
    }

    const provider = new ethers.JsonRpcProvider(chainConfig.rpc);
    const gasPrice = await getGasPrice(provider);

    const wallet = new ethers.Wallet(privateKey, provider);
    const userAddress = wallet.address;

    let amountInWei = 0;
    if (amount > 0) {
      amountInWei = ethers.parseUnits(amount.toString(), "ether");
    } else {
      amountInWei = await getBridgeAmount(userAddress, chainName, index);
    }

    if (!amountInWei) {
      const balance = await provider.getBalance(userAddress);
      const formatBalance = ethers.formatEther(balance);
      throw new Error(`余额不足支付 gas | 地址: ${userAddress} | 余额: ${formatBalance} ETH`);
    }

    logger.info(
      ` 账户: ${index} 开始获取交易数据 | 地址: ${userAddress} | 转账金额: ${ethers.formatEther(
        amountInWei,
      )} ETH`,
    );
    const { data: transactionData } = await getTransactionData(
      userAddress,
      address,
      proxyUrl,
      amountInWei,
      chainName,
    );

    const maxFeePerGas = ethers.parseUnits(gasPrice.maxFeePerGas, "gwei");
    const maxPriorityFeePerGas = ethers.parseUnits(gasPrice.maxPriorityFeePerGas, "gwei");

    const to = transactionData.to;
    if (!to) {
      throw new Error("跨链地址错误");
    }

    const estimatedGas = await provider.estimateGas({
      to: to,
      value: amountInWei,
      data: transactionData.data,
    });
    const gasLimit = (estimatedGas * 150n) / 100n;

    logger.info(`预计gas: ${estimatedGas} | 建议gas: ${gasLimit}`);

    const tx = await wallet.sendTransaction({
      to: to,
      value: amountInWei,
      data: transactionData.data,
      gasLimit: gasLimit,
      maxFeePerGas: maxFeePerGas,
      maxPriorityFeePerGas: maxPriorityFeePerGas,
    });

    try {
      logger.info("开始等待交易确认");
      const receipt = await tx.wait(1, 120000);
      if (receipt.status === 1) {
        logger.success(
          ` 账户: ${index} 跨链成功 | 金额: ${ethers.formatEther(amountInWei)} ETH | 交易哈希: ${tx.hash
          }`,
        );
        return true;
      }
    } catch (error) {
      if (error.message.includes("timeout")) {
        await diagnoseTxFailure(tx.hash, provider);
      }
      throw error;
    }

    throw new Error("交易失败");
  } catch (error) {
    logger.error(`账户: ${index} 跨链失败: ${error.message}`);
    throw error;
  }
}

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function getBalance(address, rpc) {
  const provider = new ethers.JsonRpcProvider(rpc);
  const balance = await provider.getBalance(address);
  return ethers.formatEther(balance);
}

async function main() {
  try {
    const args = process.argv.slice(2);
    const indexArg = args[0];
    const amount = args[1] || 0;

    if (!indexArg) {
      logger.error("请提供正确的参数");
      process.exit(1);
    }

    const walletList = await CSV.read(getCSVFilePath());
    const filteredWallets = filterWalletsByIndex(indexArg, walletList);
    const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
    const totalWallets = shuffledWallets.length;

    logger.info(`总共找到 ${totalWallets} 个地址需要处理`);

    const pool = new ThreadPool(MAX_CONCURRENT);

    const secureEncryption = new SecureEncryption();

    let chains = ["arb", "base", "op", "linea", "scroll", "zks"];

    // 创建任务列表
    const tasks = shuffledWallets.map((item, index) => async () => {
      logger.info(`开始处理第 ${index + 1}/${totalWallets} 个钱包: ${item.address}`);
      try {
        const balance = await getBalance(item.address, tomlConfig.eth.rpc);
        if (balance > 0.005) {
          logger.info(`地址: ${item.address} 余额: ${balance} ETH`);
          return;
        }

        // 解密私钥（提前解密，避免在循环中重复解密）
        let private_key = item.private_key;
        const isEncrypted = await secureEncryption.isEncrypted(private_key);
        if (isEncrypted) {
          private_key = await secureEncryption.decrypt(private_key);
        }

        for (const chainName of chains) {
          const chainConfig = tomlConfig[chainName];
          if (!chainConfig) {
            logger.error(`找不到 ${chainName} 的配置信息`);
            continue;
          }

          const balance = await getBalance(item.address, chainConfig.rpc);
          if (balance < 0.002) {
            logger.info(`地址: ${item.address} ${chainName} 余额少于0.002 ETH, 跳过`);
            continue;
          }

          logger.info(`地址: ${item.address} ${chainName} 余额: ${balance} ETH`);
          logger.info(`开始执行 从 ${chainName} 跨链 到 evm主网`);

          try {
            const result = await bridgeToEVM(
              item.address,
              private_key,
              item.index,
              item.proxy_url,
              chainName,
              amount,
            );

            if (result) {
              const delaySeconds = getRandomValue(3, 10);
              logger.info(`跨链成功，等待 ${delaySeconds} 秒后继续...`);
              await sleep(delaySeconds);
              break;
            }
          } catch (error) {
            logger.error(`处理地址 ${item.index} ${item.address} ${chainName} 失败: ${error.message}`);
          }
        }
      } catch (error) {
        logger.error(`处理地址 ${item.address} 失败: ${error.message}`);
      } finally {
        logger.info(`完成处理第 ${index + 1}/${totalWallets} 个钱包: ${item.address}`);
      }
    });

    // 执行任务
    logger.info(`开始执行任务，并发数: ${MAX_CONCURRENT}`);
    await Promise.all(tasks.map(task => pool.add(task)));
    logger.info('所有任务执行完成');

  } catch (error) {
    logger.error(`程序执行失败: ${error.message}`);
    process.exit(1);
  }
}

main();
