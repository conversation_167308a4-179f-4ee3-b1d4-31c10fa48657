import TOML from "@iarna/toml";
import fs from "fs";
import { BaseTransfer } from '../../base/evm/transaction.js';
import { filterWalletsByIndex } from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const tomlConfig = TOML.parse(fs.readFileSync(configPath, "utf-8"));

// 最大并发数
const MAX_CONCURRENT = tomlConfig.MAX_CONCURRENT || 3;

class CommonTransfer extends BaseTransfer {
  constructor(rpc, currency = 'ETH') {
    try {
      const config = {
        RPC_URL: rpc,
        currency: currency,
      };
      super(config);
    } catch (error) {
      console.error('初始化 ETHTransfer 失败:', error.message);
      throw error;
    }
  }
}


function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}




async function processQueue(items, processor, maxConcurrency) {
  const queue = [...items];
  const running = new Set();
  const results = [];

  // 启动下一个任务
  function startNext() {
    if (queue.length === 0) return null;

    const item = queue.shift();
    const promise = (async () => {
      let result;
      try {
        result = await processor(item);
        return result; // 返回结果供收集
      } catch (error) {
        console.error(`Error processing item: ${item}, ${error}`);
        return undefined; // 明确标记失败
      } finally {
        running.delete(promise);
        const next = startNext();
        if (next) running.add(next);
      }
    })();

    return promise;
  }

  // 初始启动最大并发数的任务
  for (let i = 0; i < Math.min(maxConcurrency, items.length); i++) {
    const promise = startNext();
    if (promise) running.add(promise);
  }

  // 收集结果并等待所有任务完成
  while (running.size > 0 || queue.length > 0) {
    if (running.size === 0 && queue.length > 0) {
      const promise = startNext();
      if (promise) running.add(promise);
    }
    const completed = await Promise.race(running);
    if (completed !== undefined) results.push(completed);
  }

  return results;
}


async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0]; // 获取钱包索引参数
  const chainName = args[1];
  const amount = args[2] || 0; // 转账金额

  const chainConfig = tomlConfig[chainName];
  if (!chainConfig) {
    logger.error(`找不到 ${chainName} 的配置信息`);
    process.exit(1);
  }

  // 加载钱包
  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
  const totalWallets = shuffledWallets.length;

  logger.info(`总共找到 ${totalWallets} 个地址需要处理`);

  const rpc = chainConfig.rpc;
  const currency = chainConfig.currency;
  const gasBuffer = chainConfig.gas_buffer || 1.0;

  const transfer = new CommonTransfer(rpc, currency);

  await processQueue(
    shuffledWallets,
    wallet => transfer.processTransaction(wallet, amount, gasBuffer),
    MAX_CONCURRENT
  );
  // await transfer.processTransactions(shuffledWallets, amount, gasBuffer);
}

// node task_transfer.js 1 eth 0.001
main().catch(console.error);
