import CSV from "../../base/tools/csv.js";
import { resolveFromModule } from "../../base/tools/path.js";
import logger from "../../base/tools/logger.js";
import { getRandomValue, parseIndexRange, randomSleep, sleep } from "../../base/tools/common.js";
import NFTMinter from "./nft_minter.js";
import { getRecentTxCount } from "../../base/evm/utils.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0]; // 获取钱包索引参数
  const mintCount = args[1] || 1; // 获取 mint nft 数量

  if (!indexArg) {
    logger.error("index为空, 请检查参数...");
    return;
  }

  const configPath = resolveFromModule(import.meta.url, "./config.toml");
  const minter = new NFTMinter(configPath);

  const indexRange = parseIndexRange(indexArg);
  const walletList = await CSV.read(getCSVFilePath());

  // 根据 index 范围过滤钱包列表
  const filteredWallets = indexRange
    ? walletList.filter((w) => {
      const idx = Number(w.index);
      if (indexRange.type === "range") {
        return idx >= indexRange.start && idx <= indexRange.end;
      } else if (indexRange.type === "list") {
        return indexRange.indices.includes(idx);
      } else if (indexRange.type === "single") {
        return idx === indexRange.index;
      }
      return true;
    })
    : walletList;

  const secureEncryption = new SecureEncryption();

  for (let wallet of filteredWallets) {

    // 解密私钥
    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
    }

    const index = wallet.index;
    const address = wallet.address;

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    try {
      // 开始 mint nft
      await minter.batchMint(index, private_key, address, mintCount);
    } catch (error) {
      logger.error(`钱包序号 ${index} 执行失败: ${error.message}`);
      // 继续处理下一个钱包
      continue;
    }

    // 随机3~5秒
    await randomSleep(3, 5);
    const txCount = await getRecentTxCount(address, minter.eth_config.rpc);
    logger.info(`当天交易量为 ${txCount}`);

    // 任务间随机延迟 5-20 秒
    const delay = getRandomValue(5, 20);
    logger.info(`等待 ${delay} 秒后执行下一个任务...`);
    await sleep(delay);
  }
}

// 批量MINT NFT
// node example/evm/task_mint_nft.js 1-10 10
main();
