import XLSX from 'xlsx';
import logger from '../../base/tools/logger.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

export class MonAnalyzer {
  static async analyzeMonBalances(inputPath) {
    try {
      // 获取当前文件的目录路径
      const __filename = fileURLToPath(import.meta.url);
      const __dirname = path.dirname(__filename);

      // 构建Excel文件的完整路径
      const excelPath = path.join(__dirname, inputPath);

      logger.info(`尝试读取文件: ${excelPath}`);

      const workbook = XLSX.readFile(excelPath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      const stats = {
        total: 0,
        lessThan1: 0,
        between1And10: 0,
        moreThan10: 0,
        totalBalance: 0,
        avgBalance: 0,
        maxBalance: 0,
        minBalance: Infinity,
        // 存储小于1 MON的地址信息
        lessThan1Addresses: []
      };

      // 分析每个钱包的余额
      data.forEach((row, index) => {
        const monBalance = Number(row.MONAD_MON);
        const address = row.address;

        if (isNaN(monBalance)) return;

        stats.total++;
        stats.totalBalance += monBalance;

        if (monBalance > 0) {
          stats.minBalance = Math.min(stats.minBalance, monBalance);
          stats.maxBalance = Math.max(stats.maxBalance, monBalance);
        }

        // 记录小于1 MON的地址
        if (monBalance < 1) {
          stats.lessThan1++;
          stats.lessThan1Addresses.push({
            index: index + 1,  // Excel行号从1开始
            address,
            balance: monBalance
          });
        } else if (monBalance <= 10) {
          stats.between1And10++;
        } else {
          stats.moreThan10++;
        }
      });

      // 计算平均值
      stats.avgBalance = stats.totalBalance / stats.total;

      // 打印分析报告
      logger.info('\n========== MON 余额分析报告 ==========');
      logger.info(`总钱包数: ${stats.total}`);
      logger.info('\n基础区间统计:');
      logger.info(`余额 < 1 MON:   ${stats.lessThan1} 个地址 (${((stats.lessThan1 / stats.total) * 100).toFixed(2)}%)`);
      logger.info(`余额 1-10 MON:  ${stats.between1And10} 个地址 (${((stats.between1And10 / stats.total) * 100).toFixed(2)}%)`);
      logger.info(`余额 > 10 MON:  ${stats.moreThan10} 个地址 (${((stats.moreThan10 / stats.total) * 100).toFixed(2)}%)`);

      logger.info('\n总体统计:');
      logger.info(`总余额: ${stats.totalBalance.toFixed(4)} MON`);
      logger.info(`平均余额: ${stats.avgBalance.toFixed(4)} MON`);
      logger.info(`最大余额: ${stats.maxBalance.toFixed(4)} MON`);
      logger.info(`最小非零余额: ${stats.minBalance === Infinity ? 0 : stats.minBalance.toFixed(4)} MON`);

      // 将小于1 MON的地址写入CSV
      if (stats.lessThan1Addresses.length > 0) {
        const outputPath = path.join(__dirname, 'less_than_1_mon.csv');
        const csvContent = [
          // CSV 头部
          'Index,Address,Balance',
          // 数据行
          ...stats.lessThan1Addresses
            .sort((a, b) => b.balance - a.balance)
            .map(item => `${item.index},${item.address},${item.balance.toFixed(4)}`)
        ];

        // 写入CSV文件
        fs.writeFileSync(outputPath, csvContent.join('\n'));
        logger.info(`\n已将余额小于1 MON的地址信息写入: ${outputPath}`);
        logger.info(`共写入 ${stats.lessThan1Addresses.length} 条记录`);
      }

      logger.info('====================================\n');

      return stats;

    } catch (error) {
      logger.error(`MON 余额分析失败: ${error.message}`);
      throw error;
    }
  }
}

export const runAnalysis = async (inputFile = 'balance.xlsx') => {
  try {
    await MonAnalyzer.analyzeMonBalances(inputFile);
  } catch (error) {
    logger.error('分析执行失败:', error);
  }
};

runAnalysis();
