import Inscription from "../../base/evm/inscription.js"; // 假设有一个 Inscription 类
import logger from "../../base/tools/logger.js";
import { parseTOMLConfig, sleep, getRandomValue } from "../../base/tools/common.js";
import { retry } from "../../base/tools/common.js";

class InscriptionMinter {
  constructor(configPath) {
    this.config = parseTOMLConfig(configPath);
    this.eth_config = this.config.eth;
    this.finishedInscriptions = new Map(); // 用于记录已铸造的铭文索引
  }

  async getRandomInscriptionTick() {
    const inscription_tick_list = this.config.INSCRIPTION_TICK_LIST;
    const random_index = Math.floor(Math.random() * inscription_tick_list.length);
    return inscription_tick_list[random_index];
  }

  async mintInscription(index, address, private_key) {
    const rpc = this.eth_config.rpc;
    const tick = await this.getRandomInscriptionTick();
    let idx;
    let attempts = 0;
    const maxAttempts = 10; // 最大尝试次数

    while (attempts < maxAttempts) {
      // 生成随机 index
      idx = getRandomValue(1, 21000);

      // 检查是否已经 mint 过
      if (this.isIndexMinted(tick, idx)) {
        attempts++;
        logger.warn(`[铭文] index ${idx} 已经被使用过，重新生成...`);
        continue;
      }

      try {
        const hash = await this._mint(index, address, private_key, tick, idx);
        if (hash) {
          this.recordMintedIndex(tick, idx);
          logger.success(`[铭文] 钱包[${index}] 铸造铭文成功: ${tick} - ${idx} -  ${hash}`);
          return hash;
        }
      } catch (err) {
        logger.error(`[铭文] 钱包[${index}] 铸造失败: ${err.message}`);

        // 如果是因为 index 已被使用，记录下来
        if (err.message.includes("index already used") || err.message.includes("already minted")) {
          this.recordMintedIndex(tick, idx);
          attempts++;
          continue;
        }

        // 如果是致命错误（比如余额不足），直接退出
        if (
          err.message.includes("insufficient funds") ||
          err.message.includes("gas required exceeds allowance")
        ) {
          throw err;
        }

        // 其他错误等待后重试
        const delay = Math.floor(Math.random() * 30) + 10; // 10-40秒随机延迟
        logger.warn(`[铭文] 遇到错误，等待 ${delay} 秒后重试...`);
        await sleep(delay);
        attempts++;
      }
    }

    logger.error(`[铭文] 钱包[${index}] ${address} 尝试 ${maxAttempts} 次后仍未成功`);
    return false;
  }

  async _mint(index, address, private_key, tick, idx) {
    const rpc = this.eth_config.rpc;

    const inscription = new Inscription(private_key, rpc, this.eth_config.max_fee);
    const receipt = await inscription.mint("erc-20", tick, idx, "1000");
    if (receipt.hash) {
      return receipt.hash;
    }

    logger.error(`[铭文] 钱包[${index}] ${address} 铸造铭文失败: ${idx}`);
    return false;
  }

  isIndexMinted(tick, idx) {
    return this.finishedInscriptions.has(tick) && this.finishedInscriptions.get(tick).has(idx);
  }

  recordMintedIndex(tick, idx) {
    if (!this.finishedInscriptions.has(tick)) {
      this.finishedInscriptions.set(tick, new Set());
    }
    this.finishedInscriptions.get(tick).add(idx);
  }

  async batchMint(index, private_key, address, targetMintTimes = 10) {
    let successfulMints = 0;
    for (let i = 0; i < targetMintTimes; i++) {
      try {
        logger.info(`[铭文] 钱包 ${index} 开始第 ${i + 1}/${targetMintTimes} 次 Mint`);

        const hash = await retry(async () => await this.mintInscription(index, address, private_key), 3, 5000);
        if (hash) {
          successfulMints++;
          logger.info(`[铭文] 钱包 ${index} 第 ${successfulMints} 次 Mint 成功`);
        } else {
          logger.warn(`[铭文] 钱包 ${index} Mint 失败，可能已经 Mint 过`);
        }

        if (i < targetMintTimes - 1) {
          const delay = Math.floor(Math.random() * 20) + 5; // 5-25秒随机延迟
          logger.info(`[铭文] 等待 ${delay} 秒后继续下一次 Mint...`);
          await sleep(delay);
        }
      } catch (err) {
        logger.error(`[铭文] 钱包 ${index} Mint 失败: ${err.message}`);

        // 如果是致命错误（比如余额不足），直接退出
        if (
          err.message.includes("insufficient funds") ||
          err.message.includes("gas required exceeds allowance")
        ) {
          throw err;
        }

        // 其他错误记录后继续下一次
        i--; // 重试当前次数
        const delay = Math.floor(Math.random() * 30) + 10; // 10-40秒随机延迟
        logger.warn(`[铭文] 遇到错误，等待 ${delay} 秒后重试...`);
        await sleep(delay);
      }
    }

    logger.info(`[铭文] 钱包 ${index} 完成 Mint 任务: ${successfulMints}/${targetMintTimes}`);
    return successfulMints;
  }
}

export default InscriptionMinter;
