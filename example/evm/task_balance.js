import { ethers } from "ethers";
import { Wallet } from "../../base/evm/wallet.js";
import { resolveFromModule } from "../../base/tools/path.js";
import { readExcelData, updateExcelData } from "../../base/tools/excel.js";
import logger from "../../base/tools/logger.js";
import { filterWalletsByIndex, parseTOMLConfig } from "../../base/tools/common.js";
import OKXApi from "../../base/tools/okx_api.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const tomlConfig = parseTOMLConfig(configPath);

// 获取所有链的配置
function getAllChains() {
  const chains = {};
  for (const [key, value] of Object.entries(tomlConfig)) {
    // 只获取链的配置（排除其他配置项）
    if (typeof value === "object" && "chainId" in value) {
      chains[key] = value;
    }
  }
  return chains;
}

async function getWalletList() {
  const excelFile = resolveFromModule(import.meta.url, "./balance.xlsx");

  return readExcelData(
    excelFile,
    {
      index: "index",
      address: "address",
    },
    [],
  );
}

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  const chainArg = args[1];

  const excelFile = resolveFromModule(import.meta.url, "./balance.xlsx");

  const walletList = await getWalletList();
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 获取所有链的配置
  const chains = getAllChains();

  // 根据链参数过滤链列表
  const filteredChains = chainArg
    ? Object.fromEntries(
      Object.entries(chains).filter(([key]) => {
        const chainNames = chainArg.toLowerCase().split(',').map(name => name.trim());
        return chainNames.includes(key.toLowerCase());
      })
    )
    : chains;

  logger.info(`找到 ${Object.keys(filteredChains).length} 条链`);

  for (const wallet of filteredWallets) {
    let tokenBalances = {};

    const chainEntries = Object.entries(filteredChains);
    for (let i = 0; i < chainEntries.length; i += 2) {
      const batchEntries = chainEntries.slice(i, i + 2);

      const promises = batchEntries.map(async ([chainName, chainConfig]) => {
        const { rpc, currency: token, chainId } = chainConfig;
        try {
          let privateKey = wallet.privateKey || "";
          const w = new Wallet(privateKey, rpc);
          const balance = await w.getBalance(wallet.address);
          const formattedBalance = Number(ethers.formatEther(balance)).toFixed(5);
          logger.success(
            `【${wallet.index}】[${chainName}] ${wallet.address} Balance: ${formattedBalance} ${token}`,
          );

          // 生成Excel列名：链名_货币，全部大写
          const excelToken = `${chainName}_${token}`.toUpperCase();
          tokenBalances[excelToken] = formattedBalance;

          if (chainName === "eth") {
            const nonce = await w.getNonce(wallet.address);
            tokenBalances["ETH_NONCE"] = nonce;
            logger.success(`【${wallet.index}】[${chainName}] ${wallet.address} Nonce: ${nonce}`);

            tokenBalances["ETH_NFT"] = await getNFTCountByAddress(wallet.address);
          } else if (chainName === "monad") {
            const nonce = await w.getNonce(wallet.address);
            tokenBalances["MONAD_NONCE"] = nonce;
            logger.success(
              `【${wallet.index}】[${chainName}] ${wallet.address} Nonce: ${nonce}`,
            );
          }

          const balance_usd = await getTotalBalanceUSD(wallet.address, chainId);
          const excel_usd = `${chainName}_USD`
          tokenBalances[excel_usd] = balance_usd;
          logger.success(
            `【${wallet.index}】[${chainName}] ${wallet.address} balance usd: ${balance_usd}`,
          );

          await sleep(1000);
        } catch (error) {
          logger.error(`【${wallet.index}】[${chainName}] 查询失败:`, error.message);
          const excelToken = `${chainName}_${token}`.toUpperCase();
          tokenBalances[excelToken] = "ERROR";
        }
      });

      await Promise.all(promises);
      await sleep(1000);
    }

    // 更新 Excel
    if (Object.keys(tokenBalances).length > 0) {
      updateExcelData(excelFile, { index: wallet.index }, tokenBalances);
    }
  }
}

/**
 * 获取钱包地址的 NFT 总数量
 * @param {*} address 钱包地址
 * @returns
 */
async function getNFTCountByAddress(address) {
  const api_key = tomlConfig.OKX_API_KEY;
  const secret_key = tomlConfig.OKX_SECRET_KEY;
  const passphrase = tomlConfig.OKX_PASSPHRASE;
  const project = tomlConfig.OKX_PROJECT;
  const api = new OKXApi(api_key, secret_key, passphrase, project);
  const nftCount = await api.getNFTCount(address, "eth");
  return nftCount;
}


/**
 * 获取钱包某个链下的余额价值
 * @param {*} address 钱包地址
 * @param {*} chainId chainId
 * @returns
 */
async function getTotalBalanceUSD(address, chainId = '1') {
  const api_key = tomlConfig.OKX_API_KEY;
  const secret_key = tomlConfig.OKX_SECRET_KEY;
  const passphrase = tomlConfig.OKX_PASSPHRASE;
  const project = tomlConfig.OKX_PROJECT;
  const api = new OKXApi(api_key, secret_key, passphrase, project);
  const totalUSD = await api.getTotalBalanceUSD(address, chainId);
  return totalUSD;
}

// 示例
// node balance.js 1 eth 查询指定索引的钱包余额
// node balance.js 1-10 eth 查询指定索引范围的钱包余额
// node balance.js 1,2,3 eth 查询指定索引的钱包余额
// node balance.js eth 查询所有钱包的 ETH 余额
// node balance.js bsc 查询所有钱包的 BNB 余额
// node balance.js "" eth 查询所有钱包，但只查询 eth 链
// node balance.js 1-10 eth,bsc,op 查询指定索引范围和指定链的钱包余额

main();

// logger.info(await getNFTCountByAddress(""));
