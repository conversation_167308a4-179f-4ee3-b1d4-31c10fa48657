import CSV from "../../base/tools/csv.js";

import { resolveFromModule } from "../../base/tools/path.js";
import logger from "../../base/tools/logger.js";
import {
  filterWalletsByIndex,
  randomSleep,
  sleep,
  getRandomValue,
} from "../../base/tools/common.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import { getRecentTxCount } from "../../base/evm/utils.js";
import InscriptionMinter from "./insc_minter.js";

async function main() {


  const args = process.argv.slice(2);
  const indexArg = args[0]; // 获取钱包索引参数
  const mintCount = args[1] || 1; // 获取 mint 铭文数量, 默认1

  const configPath = resolveFromModule(import.meta.url, "./config.toml");
  const minter = new InscriptionMinter(configPath);

  if (!indexArg) {
    logger.error("index为空, 请检查参数...");
    return;
  }

  const walletList = await CSV.read(resolveFromModule(import.meta.url, "./eth_wallet.csv"));
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  const secureEncryption = new SecureEncryption();
  for (let i = 0; i < filteredWallets.length; i++) {
    const wallet = filteredWallets[i];
    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
    }
    const index = wallet.index;
    const address = wallet.address;

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    let txCount = await getRecentTxCount(address, minter.eth_config.rpc);
    if (txCount >= 3) {
      logger.info(`钱包 ${index} 当天交易量为 ${txCount}, 跳过mint`);
      return;
    }
    // 随机3~5秒
    await randomSleep(3, 5);
    await minter.batchMint(index, private_key, address, mintCount);

    if (i == filteredWallets.length - 1) {
      return;
    }

    // 任务间随机延迟 5-20 秒
    const delay = getRandomValue(5, 20);
    logger.info(`等待 ${delay} 秒后执行下一个任务...`);
    await sleep(delay);
  }
}

// 批量MINT铭文
// node example/evm/task_mint_insc.js 1-10 10
main();
