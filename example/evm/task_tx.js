import { getRecentTxCount } from "../../base/evm/utils.js";
import { Wallet } from "../../base/evm/wallet.js";
import { filterWalletsByIndex, getRandomValue, parseTOMLConfig, sleep } from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import SecureEncryption from '../../base/tools/secure-encryption.js';
import InscriptionMinter from "./insc_minter.js";
import NFTMinter from "./nft_minter.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);
const eth_config = config.eth;
const rpc = eth_config.rpc;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0]; // 获取钱包索引参数

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  // 定义可用的任务列表
  const tasks = [
    {
      name: "Inscription Mint",
      executor: new InscriptionMinter(configPath),
      action: async (executor, wallet) => {
        // 先检查nonce是否大于3
        const w = new Wallet(wallet.private_key, rpc);
        const nonce = await w.getNonce(wallet.address);
        if (nonce > 3) {
          logger.info(`[铭文] 钱包 ${wallet.index} 的nonce大于3，跳过铸造`);
          return;
        }

        const mintTimes = getRandomValue(1, 3); // 铭文随机 1-3 次
        logger.info(`[铭文] 计划执行 ${mintTimes} 次铸造`);
        await executor.batchMint(wallet.index, wallet.private_key, wallet.address, mintTimes);
      },
    },
    {
      name: "NFT Mint",
      executor: new NFTMinter(configPath),
      action: async (executor, wallet) => {
        // 先检查是否已经拥有 NFT
        const NFTCount = await executor.getNFTCountByAddress(wallet.address);
        if (NFTCount > 0) {
          logger.info(`[NFT] 钱包 ${wallet.index} 已拥有 NFT，跳过铸造`);
          return;
        }

        const mintTimes = getRandomValue(1, 2); // NFT 随机 1-2 次
        logger.info(`[NFT] 计划执行 ${mintTimes} 次铸造`);
        await executor.batchMint(wallet.index, wallet.private_key, wallet.address, mintTimes);
      },
    },
  ];

  const secureEncryption = new SecureEncryption();

  for (let wallet of shuffledWallets) {

    // 解密私钥
    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    const index = wallet.index;
    const address = wallet.address;

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    logger.info(`开始执行任务: ${index} ${address}`);

    let txCount = await getRecentTxCount(address, rpc);
    if (txCount >= 5) {
      logger.info(`钱包 ${index} 当天交易量为 ${txCount}, 跳过此任务...`);
      continue;
    }

    // 随机打乱任务顺序
    const shuffledTasks = [...tasks].sort(() => Math.random() - 0.5);
    for (const task of shuffledTasks) {
      try {
        logger.info(`执行任务: ${task.name}`);
        await task.action(task.executor, wallet);
      } catch (error) {
        logger.error(`执行任务 ${task.name} 失败:`, error);
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    logger.info(`任务完成: ${index} ${address}, 耗时 ${duration} 秒`);

    // 任务间随机延迟 5-15 秒
    const delay = getRandomValue(5, 25);
    await sleep(delay);
  }
}

// 执行任务
// node example/evm/task_tx.js 1-10
main();
