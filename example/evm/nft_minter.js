import NFT from "../../base/evm/nft.js";
import logger from "../../base/tools/logger.js";
import { parseTOMLConfig, sleep, getRandomValue } from "../../base/tools/common.js";
import OKXA<PERSON> from "../../base/tools/okx_api.js";
import { ethers } from "ethers";

class NFTMinter {
  constructor(configPath) {
    this.config = parseTOMLConfig(configPath);
    if (!this.config) {
      throw new Error(`${configPath}配置文件为空`);
    }

    this.eth_config = this.config.eth;
    if (!this.eth_config) {
      throw new Error(`${configPath}配置文件 eth配置项 为空`);
    }

    this.abiMintfunERC721 = [
      "function mint(uint256) payable",
      "function balanceOf(address) view returns (uint256)",
      "function symbol() view returns (string)",
      "function totalSupply() view returns (uint256)",
      "function freeSupply() view returns (uint256)",
    ];
  }

  async mintNFT(index, address, private_key) {
    const nft_contract_info = this.getRandomNFTContract();
    const nft_contract_address = nft_contract_info[0];
    const nft_amount = nft_contract_info[1];
    const rpc = this.eth_config.rpc;

    try {
      // 创建合约实例来检查mint是否需要付费
      const provider = new ethers.JsonRpcProvider(rpc);
      const contract = new ethers.Contract(nft_contract_address, this.abiMintfunERC721, provider);

      // 检查mint交易是否需要支付ETH
      try {
        const value = await contract.mint.staticCall(nft_amount);
        if (value > 0) {
          logger.warn(`[NFT] 合约 ${nft_contract_address} 需要支付费用，跳过`);
          return false;
        }
      } catch (e) {
        // 如果调用失败，继续尝试mint
        logger.debug(`检查mint费用失败: ${e.message}`);
      }

      logger.info(
        `[Mint NFT]
        钱包序号: ${index}
        钱包地址: ${address}
        合约地址: ${nft_contract_address}
        NFT数量: ${nft_amount}`.replace(/^\s+/gm, ""),
      );

      const nft = new NFT(
        private_key,
        nft_contract_address,
        rpc,
        this.abiMintfunERC721,
        this.eth_config.max_fee,
        nft_amount,
        0  // 确保不发送ETH
      );

      const receipt = await nft.mint();
      return receipt.hash ? true : false;
    } catch (err) {
      logger.error(`[NFT] 钱包 ${index} mintNFT失败: ${err.message}`);
      throw err;
    }
  }

  /**
   * 通过配置文件数组随机获取nft合约
   * @returns
   */
  getRandomNFTContract() {
    try {
      const nft_contract_address_list = this.config.NFT_CONTRACT_ADDRESS_LIST;
      const random_index = Math.floor(Math.random() * nft_contract_address_list.length);
      const nft_contract_info = nft_contract_address_list[random_index];
      const nft_contract_address = nft_contract_info.split(" ")[0];
      const nft_amount = nft_contract_info.split(" ")[1];
      return [nft_contract_address, nft_amount];
    } catch (err) {
      logger.error(`[NFT] getRandomNFTContract 失败: ${err.message}`);
      throw err;
    }
  }

  /**
   * 重试次数
   * @param {*} fn
   * @param {*} maxRetries
   * @param {*} delay
   * @returns
   */
  async withRetry(fn, maxRetries = 3, delay = 2) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (err) {
        if (i === maxRetries - 1) throw err;

        if (
          err.message.includes("ECONNRESET") ||
          err.message.includes("timeout") ||
          err.message.includes("network error")
        ) {
          logger.warn(`网络错误，等待 ${delay} 秒后重试(${i + 1}/${maxRetries})...`);
          await sleep(delay);
          delay *= 1.5;
          continue;
        }
        throw err;
      }
    }
  }

  /**
   * 批量mint nft
   * @param {*} index 下标
   * @param {*} private_key 私钥
   * @param {*} address 地址
   * @param {*} mintCount mint次数，默认10次
   * @returns
   */
  async batchMint(index, private_key, address, targetMintTimes = 10) {
    let successfulMints = 0;
    logger.info(`[NFT] 钱包 ${index} 计划 Mint ${targetMintTimes} 次`);

    for (let i = 0; i < targetMintTimes; i++) {
      try {
        logger.info(`[NFT] 钱包 ${index} 开始第 ${i + 1}/${targetMintTimes} 次 Mint`);

        const result = await this.withRetry(async () => {
          return await this.mintNFT(index, address, private_key);
        });

        if (result) {
          successfulMints++;
          logger.info(`[NFT] 钱包 ${index} 第 ${successfulMints} 次 Mint 成功`);
        } else {
          logger.warn(`[NFT] 钱包 ${index} Mint 失败，可能已经 Mint 过`);
        }

        if (i < targetMintTimes - 1) {
          const delay = getRandomValue(5, 25);
          logger.info(`[NFT] 等待 ${delay} 秒后继续下一次 Mint...`);
          await sleep(delay);
        }
      } catch (err) {
        logger.error(`[NFT] 钱包 ${index} Mint 失败: ${err.message}`);

        if (
          err.message.includes("insufficient funds") ||
          err.message.includes("gas required exceeds allowance")
        ) {
          throw err;
        }

        i--; // 重试当前次数
        const delay = Math.floor(Math.random() * 30) + 10; // 10-40秒随机延迟
        logger.warn(`[NFT] 遇到错误，等待 ${delay} 秒后重试...`);
        await sleep(delay);
      }
    }

    logger.info(`[NFT] 钱包 ${index} 完成 Mint 任务: ${successfulMints}/${targetMintTimes}`);
    return successfulMints;
  }

  /**
   * 获取钱包地址的 NFT 总数量
   * @param {*} address 钱包地址
   * @returns
   */
  async getNFTCountByAddress(address) {
    const api_key = this.config.OKX_API_KEY;
    const secret_key = this.config.OKX_SECRET_KEY;
    const passphrase = this.config.OKX_PASSPHRASE;
    const project = this.config.OKX_PROJECT;
    const api = new OKXApi(api_key, secret_key, passphrase, project);
    const nftCount = await api.getNFTCount(address, "eth");
    return nftCount;
  }
}

export default NFTMinter;
