import SuiWallet from "../../base/sui/wallet.js";
import fs from "fs/promises";
import readline from "readline";

async function createWallets(number) {
  const suiWallet = new SuiWallet();
  suiWallet.createWallets(number, "sui_wallets.csv");
}

async function checkBalances(inputFile) {
  const suiWallet = new SuiWallet();
  const addresses = (await fs.readFile(inputFile, "utf-8"))
    .split("\n")
    .map((addr) => addr.trim())
    .filter((addr) => addr);
  suiWallet.checkBalances(addresses);
}

async function getPrivateKeyAndAddress(mnemonic) {
  const suiWallet = new SuiWallet();
  const result = await suiWallet.getPrivateKeyAndAddress(mnemonic);
  console.log(result);
}

async function showMenu() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = (prompt) => {
    return new Promise((resolve) => {
      rl.question(prompt, resolve);
    });
  };

  while (true) {
    try {
      console.log("\n=== SUI 钱包工具 ===");
      console.log("1. 创建钱包");
      console.log("2. 查询余额");
      console.log("3. 从助记词导入钱包");
      console.log("4. 退出程序");

      const answer = await question("请选择功能 (1-4): ");

      switch (answer.trim()) {
        case "1": {
          const number = await question("请输入要创建的钱包数量 (默认100): ");
          const walletCount = number.trim() === "" ? 100 : parseInt(number);
          if (isNaN(walletCount) || walletCount <= 0) {
            console.log("错误：请输入有效的数字！");
            continue;
          }
          await createWallets(walletCount);
          break;
        }
        case "2": {
          let inputFile = await question("请输入要查询的文件路径 (默认 address.txt): ");
          if (inputFile.trim() === "") {
            inputFile = "address.txt";
          }
          // 清理文件路径，移除多余的引号
          let cleanPath = inputFile.trim().replace(/['"]+/g, "");
          let finalPath = cleanPath === "" ? "address.txt" : cleanPath;

          await checkBalances(finalPath);
          // 添加等待用户按回车继续
          await question("\n按回车键继续...\n");
          break;
        }
        case "3": {
          const mnemonic = await question("请输入助记词: ");
          await getPrivateKeyAndAddress(mnemonic);
          break;
        }
        case "4": {
          console.log("感谢使用，再见！");
          rl.close();
          break;
        }
        default:
          console.log("无效的选择，请重试");
      }
    } catch (error) {
      console.error("发生错误：", error.message);
    }
  }
}


showMenu();


