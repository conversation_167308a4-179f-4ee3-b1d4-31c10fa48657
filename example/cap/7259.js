!function () {
    try {
        var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {}
            , r = (new e.Error).stack;
        r && (e._sentryDebugIds = e._sentryDebugIds || {},
            e._sentryDebugIds[r] = "e4606527-2b6e-415f-b133-ef08700b57aa",
            e._sentryDebugIdIdentifier = "sentry-dbid-e4606527-2b6e-415f-b133-ef08700b57aa")
    } catch (e) { }
}(),
    (self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[7358, 8792], {
        7529: (e, r, t) => {
            "use strict";
            t.d(r, {
                v: () => a
            });
            var n = t(64921)
                , s = t(36557);
            let l = e => {
                let { dsn: r, errorSampleRate: t } = e;
                return {
                    environment: "production",
                    dsn: r,
                    release: "web-v0.56.4",
                    tracesSampleRate: t,
                    sampleRate: 1,
                    debug: !1,
                    beforeSend(e) {
                        var r, n;
                        return (null == e ? void 0 : null === (n = e.exception) || void 0 === n ? void 0 : null === (r = n.values) || void 0 === r ? void 0 : r.some(e => {
                            var r;
                            return null == e ? void 0 : null === (r = e.value) || void 0 === r ? void 0 : r.includes("Minified React error")
                        }
                        )) ? null : "fatal" === e.level || Math.random() <= t ? e : null
                    }
                }
            }
                , o = e => e instanceof Error ? e.message : "object" == typeof e && null !== e ? "message" in e ? "object" == typeof e.message ? JSON.stringify(e.message) : String(e.message) : "data" in e ? "object" == typeof e.data ? JSON.stringify(e.data) : String(e.data) : JSON.stringify(e) : String(e)
                , i = "true" === t(37811).env.NEXT_PUBLIC_LOCAL_SERVER
                , a = {
                    setUser: s.gV,
                    info: e => {
                        i || (0,
                            s.wd)(e, "debug")
                    }
                    ,
                    error: (e, r) => {
                        let t = "An error occurred";
                        t = (null == r ? void 0 : r.msg) ? r.msg : o(e);
                        let n = e instanceof Error ? e : Error(t);
                        if (!i)
                            try {
                                var l;
                                (0,
                                    s.Cp)(n, {
                                        level: null !== (l = null == r ? void 0 : r.level) && void 0 !== l ? l : "error",
                                        tags: null == r ? void 0 : r.tags,
                                        extra: null == r ? void 0 : r.extra
                                    })
                            } catch (e) {
                                (0,
                                    s.Cp)(e)
                            }
                    }
                    ,
                    setup: e => (0,
                        n.Ts)(l(e)),
                    debug: function () {
                        for (var e = arguments.length, r = Array(e), t = 0; t < e; t++)
                            r[t] = arguments[t];
                        let n = "log";
                        ["log", "info", "warn", "debug"].includes(r[0]) && (r[0],
                            r.slice(1))
                    }
                }
        }
        ,
        16117: () => { }
        ,
        60546: (e, r, t) => {
            var n = t(7529).v;
            globalThis._sentryRewritesTunnelPath = void 0,
                globalThis.SENTRY_RELEASE = {
                    id: "web-v0.56.4"
                },
                globalThis._sentryBasePath = void 0,
                globalThis._sentryRewriteFramesAssetPrefixPath = "/new-web-prd",
                n.setup({
                    dsn: "https://<EMAIL>/21",
                    errorSampleRate: .01
                })
        }
        ,
        94057: (e, r, t) => {
            Promise.resolve().then(t.t.bind(t, 89149, 23)),
                Promise.resolve().then(t.t.bind(t, 44607, 23)),
                Promise.resolve().then(t.t.bind(t, 12031, 23)),
                Promise.resolve().then(t.t.bind(t, 68, 23)),
                Promise.resolve().then(t.t.bind(t, 5088, 23)),
                Promise.resolve().then(t.t.bind(t, 19928, 23)),
                Promise.resolve().then(t.t.bind(t, 25104, 23)),
                Promise.resolve().then(t.t.bind(t, 59878, 23))
        }
    }, e => {
        var r = r => e(e.s = r);
        e.O(0, [6117], () => (r(60546),
            r(47756),
            r(94057))),
            _N_E = e.O()
    }
    ]);
