import express from 'express';
import { w } from './w_function.js';

const app = express();
app.use(express.json());
function toSnakeCase(obj) {
    if (Array.isArray(obj)) {
        return obj.map(toSnakeCase);
    } else if (obj !== null && typeof obj === 'object') {
        return Object.keys(obj).reduce((acc, key) => {
            const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
            acc[snakeKey] = toSnakeCase(obj[key]);
            return acc;
        }, {});
    }
    return obj;
}
// POST /w 接口
app.post('/geetest', async (req, res) => {
    const { campaignId, credId, genTime, type, shouldEncrypt } = req.body;
    // if (!campaignId || !credId) {
    //     return res.status(400).json({ error: 'campaignId and credId are required' });
    // }
    // 根据type选择apiName
    let apiName;
    if (type === 'SyncCredentialValue') {
        apiName = 'SyncCredentialValue';
    } else if (type == 'AddTypedCredentialItems') {
        apiName = 'AddTypedCredentialItems';
    } else if (type == 'PrepareParticipate') {
        apiName = 'PrepareParticipate';
    }
    try {
        const result = await w(campaignId, credId, apiName, genTime, shouldEncrypt);
        res.json(toSnakeCase(result));
    } catch (error) {
        console.error('服务器错误:', error);
        const errorMessage = error && error.message ? error.message : 'Internal Server Error';
        res.status(500).json({ error: errorMessage });
    }
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`w_function服务已启动，监听端口 ${PORT}`);
});
