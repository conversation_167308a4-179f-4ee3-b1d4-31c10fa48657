import fs from 'fs';
import path from 'path';

async function analyzeWasm() {
    try {
        console.log('=== 开始分析wasm文件 ===');

        // 1. 读取wasm文件
        const wasmPath = path.join(process.cwd(), 'wasm_lib_bg.fa653cde.wasm');
        console.log('读取wasm文件:', wasmPath);

        if (!fs.existsSync(wasmPath)) {
            console.error('wasm文件不存在:', wasmPath);
            return;
        }

        const wasmBuffer = fs.readFileSync(wasmPath);
        console.log('wasm文件大小:', wasmBuffer.length, 'bytes');

        // 2. 加载wasm模块
        console.log('\n=== 加载wasm模块 ===');
        const { instance } = await WebAssembly.instantiate(wasmBuffer);
        console.log('wasm模块加载成功');

        // 3. 打印所有导出函数
        console.log('\n=== 导出函数列表 ===');
        const exports = instance.exports;
        const exportNames = Object.keys(exports);
        console.log('导出函数数量:', exportNames.length);

        exportNames.forEach((name, index) => {
            const func = exports[name];
            const type = typeof func;
            console.log(`${index + 1}. ${name}: ${type}`);

            if (type === 'function') {
                console.log(`   函数参数数量: ${func.length}`);
            }
        });

        // 4. 尝试调用可能的生成函数
        console.log('\n=== 尝试调用生成函数 ===');

        // 查找可能的生成函数
        const possibleGenerators = ['generate_data', 'generateData', 'generate', 'gen_data', 'genData'];

        for (const funcName of possibleGenerators) {
            if (exports[funcName]) {
                console.log(`\n尝试调用 ${funcName}:`);
                try {
                    // 尝试不同的参数组合
                    const testCases = [
                        [], // 无参数
                        [0], // 数字
                        ['test'], // 字符串
                        [0, 'test'], // 数字+字符串
                        ['test', 0], // 字符串+数字
                    ];

                    for (let i = 0; i < testCases.length; i++) {
                        const args = testCases[i];
                        try {
                            console.log(`  测试参数 ${i + 1}: [${args.map(arg => typeof arg === 'string' ? `"${arg}"` : arg).join(', ')}]`);
                            const result = exports[funcName](...args);
                            console.log(`    成功! 返回值:`, result);
                            console.log(`    返回值类型:`, typeof result);
                            if (typeof result === 'number') {
                                console.log(`    数值:`, result);
                            }
                        } catch (error) {
                            console.log(`    失败:`, error.message);
                        }
                    }
                } catch (error) {
                    console.log(`  调用失败:`, error.message);
                }
            }
        }

        // 5. 尝试调用初始化函数
        console.log('\n=== 尝试调用初始化函数 ===');
        const possibleInits = ['init', 'initialize', 'setup', 'start', 'main'];

        for (const funcName of possibleInits) {
            if (exports[funcName]) {
                console.log(`\n尝试调用 ${funcName}:`);
                try {
                    const result = exports[funcName]();
                    console.log(`  成功! 返回值:`, result);
                } catch (error) {
                    console.log(`  失败:`, error.message);
                }
            }
        }

        // 6. 检查内存
        console.log('\n=== 检查内存 ===');
        if (exports.memory) {
            console.log('内存存在');
            const memory = exports.memory;
            console.log('内存大小:', memory.buffer.byteLength, 'bytes');
            console.log('内存页数:', memory.buffer.byteLength / 65536);
        } else {
            console.log('未找到内存导出');
        }

        // 7. 尝试调用所有函数（简单测试）
        console.log('\n=== 测试所有函数 ===');
        for (const name of exportNames) {
            const func = exports[name];
            if (typeof func === 'function' && func.length === 0) {
                try {
                    console.log(`测试无参函数 ${name}:`);
                    const result = func();
                    console.log(`  成功! 返回值:`, result);
                } catch (error) {
                    console.log(`  失败:`, error.message);
                }
            }
        }

        console.log('\n=== 分析完成 ===');

    } catch (error) {
        console.error('分析过程中出错:', error);
    }
}

// 运行分析
analyzeWasm();
