import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import path from 'path';
import { H } from './98546.js';

puppeteer.use(StealthPlugin());

// 假设这些是从其他模块导入的
const M = {
    K: {
        AddTypedCredentialItems: "AddTypedCredentialItems",
        SyncCredentialValue: "SyncCredentialValue",
        PrepareParticipate: "PrepareParticipate"
    }
};

const l = {
    I: {
        Append: "Append"
    }
};

// 假设的h函数（GraphQL mutation）
async function h({ variables }) {
    // 这里是模拟的GraphQL调用
    console.log("执行GraphQL mutation:", variables);
    return { success: true };
}

// 假设的er对象
const er = {
    error: (e) => console.error("错误:", e)
};

// 修改w函数，使用Puppeteer执行H函数
async function w(t, i, apiName = M.K.AddTypedCredentialItems, genTime = null, shouldEncrypt = false) {
    let browser;
    try {
        browser = await puppeteer.launch({
            headless: true, // 使用新的无头模式
            args: [
                '--window-size=1920,1080',
                '--disable-blink-features=AutomationControlled', // 关闭自动化标志
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ]
        });
        const page = await browser.newPage();
        await page.setViewport({ width: 1920, height: 1080 });
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9'
        });

        // 导航到本地服务器上的一个空白页面，为模块加载提供正确的上下文
        await page.goto('http://localhost:3000');

        // 将H函数及其依赖注入到浏览器环境中执行
        // 我们不再传递genTime，让H函数在浏览器内部自己生成，以确保时间戳的即时性
        const a = await page.evaluate(async (apiName, shouldEncrypt) => {
            // 从本地服务器动态导入H函数
            const { H } = await import('./98546.js');
            // 使用 (0, H) 的方式调用，以确保 this 上下文是 undefined 或全局对象
            // 这模拟了原始代码中的 (0, B.H) 写法
            return (0, H)({ apiName, shouldEncrypt });
        }, apiName, shouldEncrypt);

        await h({
            variables: {
                input: {
                    credId: i,
                    campaignId: t,
                    operation: l.I.Append,
                    items: [],
                    captcha: a
                }
            }
        });
        return a;
    } catch (e) {
        er.error(e);
        throw e;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// 导出函数
export { w };

// // 测试函数
// async function testWFunction() {
//     try {
//         console.log("开始测试w函数 (shouldEncrypt: true)...");
//         // 关键：根据真实调用场景，apiName设为PrepareParticipate, shouldEncrypt设为true
//         const result = await w(
//             "GCrMzt68vJ",
//             "572641124745740288",
//             M.K.PrepareParticipate,
//             null,
//             true
//         );
//         console.log("w函数返回结果:", result);
//         return result;
//     } catch (error) {
//         console.error("w函数执行失败:", error);
//         throw error;
//     }
// }

// //如果直接运行此文件，执行测试
// if (import.meta.url.endsWith('w_function.js')) {
//     testWFunction();
// }
