"use strict";
// Removed Node.js specific imports: fs, path, url, crypto
// Removed mock 'self' object to allow the real browser 'self' to be used.
let wasm;

let _t = Date.now()
    , _r = Math.random();
if (_t < 0 || _r > 2)
    throw Error("Invalid state");

function addToExternrefTable0(e) {
    let n = wasm.__externref_table_alloc();
    return wasm.__wbindgen_export_2.set(n, e),
        n
}

function handleError(e, n) {
    try {
        return e.apply(this, n)
    } catch (n) {
        let e = addToExternrefTable0(n);
        wasm.__wbindgen_exn_store(e)
    }
}

let WASM_VECTOR_LEN = 0
    , cachedUint8ArrayMemory0 = null;
function getUint8ArrayMemory0() {
    return (null === cachedUint8ArrayMemory0 || 0 === cachedUint8ArrayMemory0.byteLength) && (cachedUint8ArrayMemory0 = new Uint8Array(wasm.memory.buffer)),
        cachedUint8ArrayMemory0
}

let cachedTextEncoder = "undefined" != typeof TextEncoder ? new TextEncoder("utf-8") : {
    encode: () => {
        throw Error("TextEncoder not available")
    }
}
    , encodeString = "function" == typeof cachedTextEncoder.encodeInto ? function (e, n) {
        return cachedTextEncoder.encodeInto(e, n)
    }
        : function (e, n) {
            let t = cachedTextEncoder.encode(e);
            return n.set(t),
            {
                read: e.length,
                written: t.length
            }
        }
    ;

function passStringToWasm0(e, n, t) {
    if (void 0 === t) {
        let t = cachedTextEncoder.encode(e)
            , r = n(t.length, 1) >>> 0;
        return getUint8ArrayMemory0().subarray(r, r + t.length).set(t),
            WASM_VECTOR_LEN = t.length,
            r
    }
    let r = e.length
        , a = n(r, 1) >>> 0
        , s = getUint8ArrayMemory0()
        , i = 0;
    for (; i < r; i++) {
        let n = e.charCodeAt(i);
        if (n > 127)
            break;
        s[a + i] = n
    }
    if (i !== r) {
        0 !== i && (e = e.slice(i)),
            a = t(a, r, r = i + 3 * e.length, 1) >>> 0;
        let n = encodeString(e, getUint8ArrayMemory0().subarray(a + i, a + r));
        i += n.written,
            a = t(a, r, i, 1) >>> 0
    }
    return WASM_VECTOR_LEN = i,
        a
}

let cachedDataViewMemory0 = null;
function getDataViewMemory0() {
    return (null === cachedDataViewMemory0 || !0 === cachedDataViewMemory0.buffer.detached || void 0 === cachedDataViewMemory0.buffer.detached && cachedDataViewMemory0.buffer !== wasm.memory.buffer) && (cachedDataViewMemory0 = new DataView(wasm.memory.buffer)),
        cachedDataViewMemory0
}

let cachedTextDecoder = "undefined" != typeof TextDecoder ? new TextDecoder("utf-8", {
    ignoreBOM: !0,
    fatal: !0
}) : {
    decode: () => {
        throw Error("TextDecoder not available")
    }
};

function getStringFromWasm0(e, n) {
    return e >>>= 0,
        cachedTextDecoder.decode(getUint8ArrayMemory0().subarray(e, e + n))
}

function isLikeNone(e) {
    return null == e
}

"undefined" != typeof TextDecoder && cachedTextDecoder.decode();

let CLOSURE_DTORS = "undefined" == typeof FinalizationRegistry ? {
    register: () => { }
    ,
    unregister: () => { }
} : new FinalizationRegistry(e => {
    wasm.__wbindgen_export_5.get(e.dtor)(e.a, e.b)
}
);

function makeMutClosure(e, n, t, r) {
    let a = {
        a: e,
        b: n,
        cnt: 1,
        dtor: t
    }
        , s = function () {
            for (var e = arguments.length, n = Array(e), t = 0; t < e; t++)
                n[t] = arguments[t];
            a.cnt++;
            let s = a.a;
            a.a = 0;
            try {
                return r(s, a.b, ...n)
            } finally {
                0 == --a.cnt ? (wasm.__wbindgen_export_5.get(a.dtor)(s, a.b),
                    CLOSURE_DTORS.unregister(a)) : a.a = s
            }
        };
    return s.original = a,
        CLOSURE_DTORS.register(s, a, a),
        s
}

function debugString(e) {
    let n;
    let t = typeof e;
    if ("number" == t || "boolean" == t || null == e)
        return "".concat(e);
    if ("string" == t)
        return '"'.concat(e, '"');
    if ("symbol" == t) {
        let n = e.description;
        return null == n ? "Symbol" : "Symbol(".concat(n, ")")
    }
    if ("function" == t) {
        let n = e.name;
        return "string" == typeof n && n.length > 0 ? "Function(".concat(n, ")") : "Function"
    }
    if (Array.isArray(e)) {
        let n = e.length
            , t = "[";
        n > 0 && (t += debugString(e[0]));
        for (let r = 1; r < n; r++)
            t += ", " + debugString(e[r]);
        return t + "]"
    }
    let r = /\[object ([^\]]+)\]/.exec(toString.call(e));
    if (!r || !(r.length > 1))
        return toString.call(e);
    if ("Object" == (n = r[1]))
        try {
            return "Object(" + JSON.stringify(e) + ")"
        } catch (e) {
            return "Object"
        }
    return e instanceof Error ? "".concat(e.name, ": ").concat(e.message, "\n").concat(e.stack) : n
}

let cachedFloat64ArrayMemory0 = null;
function getFloat64ArrayMemory0() {
    return (null === cachedFloat64ArrayMemory0 || 0 === cachedFloat64ArrayMemory0.byteLength) && (cachedFloat64ArrayMemory0 = new Float64Array(wasm.memory.buffer)),
        cachedFloat64ArrayMemory0
}

function passArrayF64ToWasm0(e, n) {
    let t = n(8 * e.length, 8) >>> 0;
    return getFloat64ArrayMemory0().set(e, t / 8),
        WASM_VECTOR_LEN = e.length,
        t
}

function generate_data(e, n, t) {
    var r = isLikeNone(e) ? 0 : passArrayF64ToWasm0(e, wasm.__wbindgen_malloc)
        , a = WASM_VECTOR_LEN;
    let s = passStringToWasm0(n, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
        , i = WASM_VECTOR_LEN;
    var o = isLikeNone(t) ? 0 : passStringToWasm0(t, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
        , c = WASM_VECTOR_LEN;
    return wasm.generate_data(r, a, s, i, o, c)
}

function __wbg_adapter_34(e, n, t) {
    wasm.closure60_externref_shim(e, n, t)
}

function __wbg_adapter_37(e, n) {
    wasm._dyn_core__ops__function__FnMut_____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h1b6979430400b8c0(e, n)
}

function __wbg_adapter_40(e, n, t) {
    wasm.closure103_externref_shim(e, n, t)
}

function __wbg_adapter_130(e, n, t, r) {
    wasm.closure167_externref_shim(e, n, t, r)
}

let __wbindgen_enum_RtcIceGatheringState = ["new", "gathering", "complete"];

async function __wbg_load(e, n) {
    if ("function" == typeof Response && e instanceof Response) {
        if ("function" == typeof WebAssembly.instantiateStreaming)
            try {
                return await WebAssembly.instantiateStreaming(e, n)
            } catch (n) {
                if ("application/wasm" != e.headers.get("Content-Type"))
                    ;
                else
                    throw n
            }
        let t = await e.arrayBuffer();
        return await WebAssembly.instantiate(t, n)
    }
    {
        let t = await WebAssembly.instantiate(e, n);
        return t instanceof WebAssembly.Instance ? {
            instance: t,
            module: e
        } : t
    }
}

function __wbg_get_imports() {
    let imports = {};
    return imports.wbg = {},
        imports.wbg.__wbg_apply_eb9e9b97497f91e4 = function () {
            return handleError(function (e, n, t) {
                return Reflect.apply(e, n, t)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_buffer_609cc3eee51ed158 = function (e) {
            return e.buffer
        }
        ,
        imports.wbg.__wbg_call_672a4d21634d4a24 = function () {
            return handleError(function (e, n) {
                return e.call(n)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_call_7cccdd69e0791ae2 = function () {
            return handleError(function (e, n, t) {
                return e.call(n, t)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_close_314acc3eb034fb66 = function (e) {
            e.close()
        }
        ,
        imports.wbg.__wbg_construct_b91ff0e53b60c0c3 = function () {
            return handleError(function (e, n) {
                return Reflect.construct(e, n)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_cookie_867793258a291a9b = function () {
            return handleError(function (e, n) {
                let t = passStringToWasm0(n.cookie, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
                    , r = WASM_VECTOR_LEN;
                getDataViewMemory0().setInt32(e + 4, r, !0),
                    getDataViewMemory0().setInt32(e + 0, t, !0)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_createElement_8c9931a732ee2fea = function () {
            return handleError(function (e, n, t) {
                return e.createElement(getStringFromWasm0(n, t))
            }, arguments)
        }
        ,
        imports.wbg.__wbg_crypto_574e78ad8b13b65f = function (e) {
            return e.crypto
        }
        ,
        imports.wbg.__wbg_document_d249400bd7bd996d = function (e) {
            let n = e.document;
            return isLikeNone(n) ? 0 : addToExternrefTable0(n)
        }
        ,
        imports.wbg.__wbg_eval_e10dc02e9547f640 = function () {
            return handleError(function (arg0, arg1) {
                const code = getStringFromWasm0(arg0, arg1);
                console.log("WASM is trying to eval():", code);
                let ret = eval(code);
                return ret
            }, arguments)
        }
        ,
        imports.wbg.__wbg_exec_3e2d2d0644c927df = function (e, n, t) {
            let r = e.exec(getStringFromWasm0(n, t));
            return isLikeNone(r) ? 0 : addToExternrefTable0(r)
        }
        ,
        imports.wbg.__wbg_getContext_e9cf379449413580 = function () {
            return handleError(function (e, n, t) {
                let r = e.getContext(getStringFromWasm0(n, t));
                return isLikeNone(r) ? 0 : addToExternrefTable0(r)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_getExtension_ff0fb1398bcf28c3 = function () {
            return handleError(function (e, n, t) {
                let r = e.getExtension(getStringFromWasm0(n, t));
                return isLikeNone(r) ? 0 : addToExternrefTable0(r)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_getParameter_e3429f024018310f = function () {
            return handleError(function (e, n) {
                return e.getParameter(n >>> 0)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_getRandomValues_b8f5dbd5f3995a9e = function () {
            return handleError(function (e, n) {
                e.getRandomValues(n)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_get_67b2ba62fc30de12 = function () {
            return handleError(function (e, n) {
                return Reflect.get(e, n)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_get_b9b93047fe3cf45b = function (e, n) {
            return e[n >>> 0]
        }
        ,
        imports.wbg.__wbg_hardwareConcurrency_e840281060cd1953 = function (e) {
            return e.hardwareConcurrency
        }
        ,
        imports.wbg.__wbg_has_a5ea9117f258a0ec = function () {
            return handleError(function (e, n) {
                return Reflect.has(e, n)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_iceGatheringState_be775ae14a30b513 = function (e) {
            let n = e.iceGatheringState;
            return (__wbindgen_enum_RtcIceGatheringState.indexOf(n) + 1 || 4) - 1
        }
        ,
        imports.wbg.__wbg_instanceof_HtmlDocument_c2114067e0fabc29 = function (e) {
            let n;
            try {
                n = e instanceof HTMLDocument
            } catch (e) {
                n = !1
            }
            return n
        }
        ,
        imports.wbg.__wbg_instanceof_Promise_935168b8f4b49db3 = function (e) {
            let n;
            try {
                n = e instanceof Promise
            } catch (e) {
                n = !1
            }
            return n
        }
        ,
        imports.wbg.__wbg_instanceof_WebGl2RenderingContext_2b6045efeb76568d = function (e) {
            let n;
            try {
                n = e instanceof WebGL2RenderingContext
            } catch (e) {
                n = !1
            }
            return n
        }
        ,
        imports.wbg.__wbg_instanceof_Window_def73ea0955fc569 = function (e) {
            let n;
            try {
                n = e instanceof Window
            } catch (e) {
                n = !1
            }
            return n
        }
        ,
        imports.wbg.__wbg_languages_d8dad509faf757df = function (e) {
            return e.languages
        }
        ,
        imports.wbg.__wbg_length_e2d2a49132c1b256 = function (e) {
            return e.length
        }
        ,
        imports.wbg.__wbg_msCrypto_a61aeb35a24c1329 = function (e) {
            return e.msCrypto
        }
        ,
        imports.wbg.__wbg_navigator_1577371c070c8947 = function (e) {
            return e.navigator
        }
        ,
        imports.wbg.__wbg_new_23a2665fac83c611 = function (e, n) {
            try {
                var t = {
                    a: e,
                    b: n
                }
                    , r = (e, n) => {
                        let r = t.a;
                        t.a = 0;
                        try {
                            return __wbg_adapter_130(r, t.b, e, n)
                        } finally {
                            t.a = r
                        }
                    }
                    ;
                return new Promise(r)
            } finally {
                t.a = t.b = 0
            }
        }
        ,
        imports.wbg.__wbg_new_405e22f390576ce2 = function () {
            return {}
        }
        ,
        imports.wbg.__wbg_new_63847613cde5d4bc = function (e, n, t, r) {
            return new RegExp(getStringFromWasm0(e, n), getStringFromWasm0(t, r))
        }
        ,
        imports.wbg.__wbg_new_78feb108b6472713 = function () {
            return []
        }
        ,
        imports.wbg.__wbg_new_a12002a7f91c75be = function (e) {
            return new Uint8Array(e)
        }
        ,
        imports.wbg.__wbg_newnoargs_105ed471475aaf50 = function (e, n) {
            const code = getStringFromWasm0(e, n);
            console.log("WASM is trying to new Function():", code);
            return Function(code)
        }
        ,
        imports.wbg.__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a = function (e, n, t) {
            return new Uint8Array(e, n >>> 0, t >>> 0)
        }
        ,
        imports.wbg.__wbg_newwithconfiguration_d716d1bf0ec3af80 = function () {
            return handleError(function (e) {
                return new RTCPeerConnection(e)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_newwithlength_a381634e90c276d4 = function (e) {
            return new Uint8Array(e >>> 0)
        }
        ,
        imports.wbg.__wbg_node_905d3e251edff8a2 = function (e) {
            return e.node
        }
        ,
        imports.wbg.__wbg_of_2eaf5a02d443ef03 = function (e) {
            return Array.of(e)
        }
        ,
        imports.wbg.__wbg_platform_faf02c487289f206 = function () {
            return handleError(function (e, n) {
                let t = passStringToWasm0(n.platform, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
                    , r = WASM_VECTOR_LEN;
                getDataViewMemory0().setInt32(e + 4, r, !0),
                    getDataViewMemory0().setInt32(e + 0, t, !0)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_pow_35f95838ecd24e51 = function (e, n) {
            return Math.pow(e, n)
        }
        ,
        imports.wbg.__wbg_process_dc0fbacc7c1c06f7 = function (e) {
            return e.process
        }
        ,
        imports.wbg.__wbg_push_737cfc8c1432c2c6 = function (e, n) {
            return e.push(n)
        }
        ,
        imports.wbg.__wbg_queueMicrotask_97d92b4fcc8a61c5 = function (e) {
            queueMicrotask(e)
        }
        ,
        imports.wbg.__wbg_queueMicrotask_d3219def82552485 = function (e) {
            return e.queueMicrotask
        }
        ,
        imports.wbg.__wbg_randomFillSync_ac0988aba3254290 = function () {
            return handleError(function (e, n) {
                e.randomFillSync(n)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_require_60cc747a6bc5215a = function () {
            return handleError(function () {
                return module.require
            }, arguments)
        }
        ,
        imports.wbg.__wbg_resolve_4851785c9c5f573d = function (e) {
            return Promise.resolve(e)
        }
        ,
        imports.wbg.__wbg_setTimeout_f2fe5af8e3debeb3 = function () {
            return handleError(function (e, n, t) {
                return e.setTimeout(n, t)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_set_65595bdd868b3009 = function (e, n, t) {
            e.set(n, t >>> 0)
        }
        ,
        imports.wbg.__wbg_set_bb8cecf6a62b9f46 = function () {
            return handleError(function (e, n, t) {
                return Reflect.set(e, n, t)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_seticeservers_c55a30bcf9201706 = function (e, n) {
            e.iceServers = n
        }
        ,
        imports.wbg.__wbg_setonicecandidate_4482190aeed37791 = function (e, n) {
            e.onicecandidate = n
        }
        ,
        imports.wbg.__wbg_setonicegatheringstatechange_6fd24b05e005ca20 = function (e, n) {
            e.onicegatheringstatechange = n
        }
        ,
        imports.wbg.__wbg_static_accessor_GLOBAL_88a902d13a557d07 = function () {
            let e = "undefined" == typeof global ? self : global;
            return isLikeNone(e) ? 0 : addToExternrefTable0(e)
        }
        ,
        imports.wbg.__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0 = function () {
            let e = "undefined" == typeof globalThis ? self : globalThis;
            return isLikeNone(e) ? 0 : addToExternrefTable0(e)
        }
        ,
        imports.wbg.__wbg_static_accessor_SELF_37c5d418e4bf5819 = function () {
            let e = "undefined" == typeof self ? self : self;
            return isLikeNone(e) ? 0 : addToExternrefTable0(e)
        }
        ,
        imports.wbg.__wbg_static_accessor_WINDOW_5de37043a91a9c40 = function () {
            let e = "undefined" == typeof window ? self : window;
            return isLikeNone(e) ? 0 : addToExternrefTable0(e)
        }
        ,
        imports.wbg.__wbg_subarray_aa9065fa9dc5df96 = function (e, n, t) {
            return e.subarray(n >>> 0, t >>> 0)
        }
        ,
        imports.wbg.__wbg_then_44b73946d2fb3e7d = function (e, n) {
            return e.then(n)
        }
        ,
        imports.wbg.__wbg_then_48b406749878a531 = function (e, n, t) {
            return e.then(n, t)
        }
        ,
        imports.wbg.__wbg_userAgent_12e9d8e62297563f = function () {
            return handleError(function (e, n) {
                let t = passStringToWasm0(n.userAgent, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
                    , r = WASM_VECTOR_LEN;
                getDataViewMemory0().setInt32(e + 4, r, !0),
                    getDataViewMemory0().setInt32(e + 0, t, !0)
            }, arguments)
        }
        ,
        imports.wbg.__wbg_versions_c01dfd4722a88165 = function (e) {
            return e.versions
        }
        ,
        imports.wbg.__wbindgen_boolean_get = function (e) {
            let n = e;
            return "boolean" == typeof n ? +!!n : 2
        }
        ,
        imports.wbg.__wbindgen_cb_drop = function (e) {
            let n = e.original;
            return 1 == n.cnt-- && (n.a = 0,
                !0)
        }
        ,
        imports.wbg.__wbindgen_closure_wrapper198 = function (e, n, t) {
            return makeMutClosure(e, n, 61, __wbg_adapter_34)
        }
        ,
        imports.wbg.__wbindgen_closure_wrapper200 = function (e, n, t) {
            return makeMutClosure(e, n, 61, __wbg_adapter_37)
        }
        ,
        imports.wbg.__wbindgen_closure_wrapper299 = function (e, n, t) {
            return makeMutClosure(e, n, 104, __wbg_adapter_40)
        }
        ,
        imports.wbg.__wbindgen_debug_string = function (e, n) {
            let t = passStringToWasm0(debugString(n), wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
                , r = WASM_VECTOR_LEN;
            getDataViewMemory0().setInt32(e + 4, r, !0),
                getDataViewMemory0().setInt32(e + 0, t, !0)
        }
        ,
        imports.wbg.__wbindgen_init_externref_table = function () {
            let e = wasm.__wbindgen_export_2
                , n = e.grow(4);
            e.set(0, void 0),
                e.set(n + 0, void 0),
                e.set(n + 1, null),
                e.set(n + 2, !0),
                e.set(n + 3, !1)
        }
        ,
        imports.wbg.__wbindgen_is_function = function (e) {
            return "function" == typeof e
        }
        ,
        imports.wbg.__wbindgen_is_null = function (e) {
            return null === e
        }
        ,
        imports.wbg.__wbindgen_is_object = function (e) {
            let n = e;
            return "object" == typeof n && null !== n
        }
        ,
        imports.wbg.__wbindgen_is_string = function (e) {
            return "string" == typeof e
        }
        ,
        imports.wbg.__wbindgen_is_undefined = function (e) {
            return void 0 === e
        }
        ,
        imports.wbg.__wbindgen_memory = function () {
            return wasm.memory
        }
        ,
        imports.wbg.__wbindgen_number_get = function (e, n) {
            let t = n
                , r = "number" == typeof t ? t : void 0;
            getDataViewMemory0().setFloat64(e + 8, isLikeNone(r) ? 0 : r, !0),
                getDataViewMemory0().setInt32(e + 0, !isLikeNone(r), !0)
        }
        ,
        imports.wbg.__wbindgen_string_get = function (e, n) {
            let t = n
                , r = "string" == typeof t ? t : void 0;
            var a = isLikeNone(r) ? 0 : passStringToWasm0(r, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc)
                , s = WASM_VECTOR_LEN;
            getDataViewMemory0().setInt32(e + 4, s, !0),
                getDataViewMemory0().setInt32(e + 0, a, !0)
        }
        ,
        imports.wbg.__wbindgen_string_new = function (e, n) {
            return getStringFromWasm0(e, n)
        }
        ,
        imports.wbg.__wbindgen_throw = function (e, n) {
            throw Error(getStringFromWasm0(e, n))
        }
        ,
        imports
}

function __wbg_init_memory(e, n) { }

function __wbg_finalize_init(e, n) {
    return wasm = e.exports,
        __wbg_init.__wbindgen_wasm_module = n,
        cachedDataViewMemory0 = null,
        cachedFloat64ArrayMemory0 = null,
        cachedUint8ArrayMemory0 = null,
        wasm.__wbindgen_start(),
        wasm
}

function initSync(e) {
    if (void 0 !== wasm)
        return wasm;
    void 0 !== e && Object.getPrototypeOf(e) === Object.prototype && ({ module: e } = e);
    let n = __wbg_get_imports();
    return e instanceof WebAssembly.Module || (e = new WebAssembly.Module(e)),
        __wbg_finalize_init(new WebAssembly.Instance(e, n), e)
}

async function __wbg_init() {
    if (wasm) return;

    // In a browser environment, we fetch the wasm file.
    // The local server ('serve') will handle this request.
    const wasmPath = './wasm_lib_bg.755f6ec8.wasm';

    let imports = __wbg_get_imports();
    let { instance, module } = await __wbg_load(await fetch(wasmPath), imports);
    return __wbg_finalize_init(instance, module);
}

export const Ay = __wbg_init;
const original_generate_encrypted_browser_data = generate_data;

export async function generateData(e, n) {
    return original_generate_encrypted_browser_data(new Float64Array(await advancedDetection()), e, n)
}
export const Qc = generateData;

async function advancedDetection() {
    let e = [];
    for (let n = 0; n < 5; n++) {
        let n = performance.now();
        await new Promise(e => setTimeout(e, 0)),
            e.push(performance.now() - n)
    }
    return e
}

console.log_internal = function (e) { }
