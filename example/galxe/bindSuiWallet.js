import GalxeRequester from "../../base/galxe/request.js";
import { HttpsProxyAgent } from 'https-proxy-agent';

async function testCreateAccount() {
    try {
        const galxe = new GalxeRequester();

        // 使用本地代理
        const proxyUrl = `http://127.0.0.1:52001`;
        const httpsAgent = new HttpsProxyAgent(proxyUrl);

        // 1. 首先登录获取token
        console.log('\n=== 登录获取Token ===');
        const account = {
            address: "",
            privateKey: "",
            sui_addr:"",
            sui_pk:""
        };

        const signData = await galxe.generateSignData(account);
        const signInResult = await galxe.getSignInfo(signData, httpsAgent);

        if (!signInResult) {
            console.log('登录失败，无法继续操作');
            return;
        }

        // 2. 检查用户名是否可用
        const username = "ssdasdasdad";
        const exists = await galxe.checkUsernameExists(username, httpsAgent);

        if (exists) {
            console.log(`用户名 "${username}" 已被注册`);
            return;
        }

        // 3. 创建新账号
        console.log('\n=== 创建新账号 ===');
        const accountId = await galxe.createNewAccount({
            address: account.address,
            username: username,
            socialUsername: username
        }, httpsAgent);

        if (accountId) {
            console.log('账号创建成功！');
            console.log('账号ID:', accountId);
            console.log('保存的账号ID:', galxe.getAccountId());
        } else {
            console.log('账号创建失败');
        }

    } catch (error) {
        console.error('测试过程出错:', error.message);
    }
}


async function suiSign(message, sui_pk) {
  const galxe = new GalxeRequester();
  const sigMsg = await galxe.suiSign(message, sui_pk);
  console.log('生成的签名:', sigMsg);
}

// 单独的绑定 SUI 钱包测试函数
async function testBindSuiWallet() {
    try {
        console.log('\n=== 开始测试绑定 SUI 钱包 ===');
        const galxe = new GalxeRequester();
        const proxyUrl = `http://127.0.0.1:52001`;
        const httpsAgent = new HttpsProxyAgent(proxyUrl);
        console.log('代理地址:', proxyUrl);

        // 账户信息
        const account = {
            address: "",
            privateKey: "",
            sui_addr:"",
            sui_pk:""
        };
        console.log('\n账户信息:');
        console.log('ETH 地址:', account.address);
        console.log('SUI 地址:', account.sui_addr);

        // 1. 先登录
        console.log('\n=== 开始登录流程 ===');
        const signData = await galxe.generateSignData(account);
        console.log('生成的签名数据:', signData);

        const signInResult = await galxe.getSignInfo(signData, httpsAgent);
        console.log('登录结果:', signInResult ? '成功' : '失败');

        if (!signInResult) {
            console.log('登录失败，无法绑定 SUI 钱包');
            return;
        }

        // 2. 绑定 SUI 钱包
        console.log('\n=== 开始绑定 SUI 钱包 ===');
        const result = await galxe.bindSuiWallet(account, httpsAgent);
        console.log('绑定请求结果:', result);

        if (result.success) {
            console.log('✅', result.message);
        } else {
            console.log('❌', result.message);
            if (result.error) {
                console.log('错误详情:', result.error);
            }
        }

    } catch (error) {
        console.error('绑定 SUI 钱包出错:', error.message);
        console.error('错误堆栈:', error.stack);
    }
}

// 根据需要运行不同的测试
async function main() {
    const needBindSui = true; // 根据需要设置为 true 或 false

    if (needBindSui) {
        await testBindSuiWallet();
    } else {
        await testCreateAccount();
    }

    console.log('\n测试完成');
}

// // 运行测试
// main().catch(error => {
//     console.error('程序执行失败:', error);
// });


let message = 'You are updating your Galxe profile with sui address 0x77365d3af993704ddd32edfd622b7780eb891c36b8074b24fffa47928e22e8b1. Please sign the transaction to confirm.\nnonce: ********\nIssued At: 2025-01-17T07:45:54.323831\nExpiration Time: 2025-01-24T07:45:54.324020'

suiSign(message, "suiprivkey1qzmw5633ql2hx2t03k2qwm4zu6k55ka40f3hhxv6csu03nayj8d8qdy5znk")
