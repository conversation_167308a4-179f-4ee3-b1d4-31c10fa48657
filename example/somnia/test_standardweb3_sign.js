import { ethers } from "ethers";
import { Wallet } from "../../base/evm/wallet.js";
import StandardWeb3 from "./standardweb3.js";
import logger from "../../base/tools/logger.js";

// Somnia 测试网配置
const RPC_URL = "https://dream-rpc.somnia.network";

/**
 * 测试StandardWeb3签名功能
 */
async function testStandardWeb3Sign() {
  try {
    // 示例私钥（请替换为你的实际私钥）
    const privateKey = "0x你的私钥";
    
    logger.info("=== StandardWeb3 签名测试开始 ===");
    
    // 1. 创建钱包实例
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const address = wallet.getAddress();
    logger.info(`钱包地址: ${address}`);
    
    // 2. 创建StandardWeb3实例
    const standardWeb3 = new StandardWeb3(1, wallet, null);
    
    // 3. 执行签名
    const signResult = await standardWeb3.signIn();
    
    if (signResult.success) {
      logger.success("签名成功！");
      logger.info("签名数据:", JSON.stringify(signResult.data, null, 2));
      
      // 4. 验证签名（可选）
      const verifyResult = await standardWeb3.verifySignature(signResult.data);
      if (verifyResult.success && verifyResult.isValid) {
        logger.success("签名验证通过！");
      } else {
        logger.error("签名验证失败！");
      }
      
    } else {
      logger.error("签名失败:", signResult.msg);
    }
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
  }
}

/**
 * 批量测试多个钱包签名
 */
async function testMultipleWallets() {
  const wallets = [
    { index: 1, privateKey: "0x私钥1" },
    { index: 2, privateKey: "0x私钥2" },
    // 添加更多钱包...
  ];
  
  logger.info("=== 批量钱包签名测试开始 ===");
  
  for (const walletData of wallets) {
    try {
      logger.info(`\n--- 测试钱包 ${walletData.index} ---`);
      
      const wallet = new Wallet(walletData.privateKey, RPC_URL);
      await wallet.connect();
      
      const standardWeb3 = new StandardWeb3(walletData.index, wallet, null);
      const result = await standardWeb3.signIn();
      
      if (result.success) {
        logger.success(`钱包 ${walletData.index} 签名成功`);
        
        // 可以在这里添加将签名数据发送到服务器的逻辑
        // await sendSignatureToServer(result.data);
        
      } else {
        logger.error(`钱包 ${walletData.index} 签名失败: ${result.msg}`);
      }
      
      // 钱包间延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      logger.error(`钱包 ${walletData.index} 处理失败: ${error.message}`);
    }
  }
}

/**
 * 模拟发送签名数据到服务器
 */
async function sendSignatureToServer(signData) {
  try {
    // 这里是发送到StandardWeb3服务器的示例代码
    // 实际使用时需要根据API文档调整
    
    const response = await fetch('https://testnet.standardweb3.com/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        address: signData.address,
        signature: signData.signature,
        message: signData.message,
        chainId: signData.messageData.chainId,
        nonce: signData.messageData.nonce,
        requestId: signData.messageData.requestId
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      logger.success("签名数据发送成功:", result);
      return result;
    } else {
      logger.error("签名数据发送失败:", response.status, response.statusText);
      return null;
    }
    
  } catch (error) {
    logger.error("发送签名数据时发生错误:", error.message);
    return null;
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  // 单个钱包测试
  // testStandardWeb3Sign();
  
  // 批量钱包测试
  // testMultipleWallets();
  
  logger.info("请取消注释上面的测试函数来运行测试");
}
