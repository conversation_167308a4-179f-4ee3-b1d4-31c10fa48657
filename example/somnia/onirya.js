import { ethers } from "ethers";
import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";
import logger from "../../base/tools/logger.js";
import { randomSleep } from "../../base/tools/common.js";

class Onirya {
  constructor(index, wallet, proxy) {
    this.index = index;
    this.wallet = wallet;
    this.proxy = proxy;
    this.token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjcHZ5c2x1ZHliaHd5dGx1bm1wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA2MTU3OTgsImV4cCI6MjA1NjE5MTc5OH0.v3W5ag5OzFPCjvFYypzx4CeSPBm0cmDnvylvb_NERD8"

    // Supabase 配置
    this.SUPABASE_URL = 'https://pcpvysludybhwytlunmp.supabase.co';
    this.SUPABASE_HEADERS = {
      'accept': 'application/vnd.pgrst.object+json',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-HK,zh-TW;q=0.9,zh;q=0.8',
      'accept-profile': 'public',
      'apikey': this.token,
      'authorization': `Bearer ${this.token}`,
      'origin': 'https://onirya.xyz',
      'priority': 'u=1, i',
      'referer': 'https://onirya.xyz/',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'cross-site',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'x-client-info': 'supabase-js-web/2.49.8'
    };

    this.ONIRYA_CONTRACT = '0x2c25Cbd09A1ab0FBf2b4E0D2C49654C6A1afa8a8'; // Onirya contract
    
    // 合约ABI - 只包含需要的函数
    this.CONTRACT_ABI = [
      "function createWorld(address player) external payable",
      "function gatherArtifacts(address player, uint256 amount) external payable",
      "function gatherStardusts(address player, uint256 amount) external payable",
      "function speedUpResource(address player, string resourceType) external payable"
    ];
  }

  // 创建带代理的axios配置
  #createAxiosConfig(headers = {}) {
    let proxyAgent = null;
    if (this.proxy) {
      proxyAgent = new HttpsProxyAgent(this.proxy);
    }

    const config = {
      headers: { ...this.SUPABASE_HEADERS, ...headers },
      timeout: 30000,
    };

    if (proxyAgent) {
      config.httpsAgent = proxyAgent;
    }

    return config;
  }

  // 获取或创建用户
  async #getOrCreateUser() {
    try {
      const walletAddress = this.wallet.address;
      logger.info(`钱包 [${this.index}] 查询用户信息，钱包地址: ${walletAddress}`);

      try {
        const response = await axios.get(`${this.SUPABASE_URL}/rest/v1/users`, {
          params: {
            select: 'username,last_change',
            wallet_address: `eq.${walletAddress}`
          },
          ...this.#createAxiosConfig()
        });

        if (response.data && Object.keys(response.data).length > 0) {
          logger.info(`钱包 [${this.index}] 找到用户信息: ${response.data.username}`);

          // 计算 last_change + 48小时的时间
          const lastChangeDate = new Date(response.data.last_change);
          const nextChangeDate = new Date(lastChangeDate.getTime() + 48 * 60 * 60 * 1000); // 48小时后

          return {
            success: true,
            userExists: true,
            username: response.data.username,
            lastChange: response.data.last_change,
            nextChangeDate: nextChangeDate.toISOString(),
            msg: `用户已存在: ${response.data.username}`
          };
        } else {
          // 响应成功但没有数据，说明用户不存在
          logger.info(`钱包 [${this.index}] 用户不存在，需要创建新用户`);
          return await this.#createNewUser(walletAddress);
        }
      } catch (error) {
        // 检查是否是406状态码，表示用户不存在
        if (error.response && error.response.status === 406) {
          logger.info(`钱包 [${this.index}] 收到406状态码，用户不存在，需要创建新用户`);
          return await this.#createNewUser(walletAddress);
        }
        // 其他错误继续抛出
        throw error;
      }
    } catch (error) {
      logger.error(`钱包 [${this.index}] 查询用户信息失败: ${error.message}`);
      return {
        success: false,
        msg: `查询用户信息失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 创建新用户的私有方法
  async #createNewUser(walletAddress) {
    try {
      // 生成随机用户名
      const randomNum = Math.floor(100000 + Math.random() * 900000);
      let username = `user${randomNum}`;

      // 检查用户名是否已存在
      try {
        const checkResponse = await axios.get(`${this.SUPABASE_URL}/rest/v1/users`, {
          params: {
            select: '*',
            username: `eq.${username}`
          },
          ...this.#createAxiosConfig()
        });

        // 如果用户名已存在，生成新的
        if (checkResponse.data && Object.keys(checkResponse.data).length > 0) {
          const newRandomNum = Math.floor(100000 + Math.random() * 900000);
          username = `user${newRandomNum}`;
        }
      } catch (error) {
        // 如果是406错误，说明用户名不存在，可以使用
        if (error.response && error.response.status === 406) {
          logger.info(`钱包 [${this.index}] 用户名 ${username} 可用`);
        } else {
          throw error;
        }
      }

      // 创建新用户
      const createResponse = await axios.post(`${this.SUPABASE_URL}/rest/v1/users`, {
        wallet_address: walletAddress,
        username: username,
        last_change: new Date().toISOString()
      }, this.#createAxiosConfig({
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      }));

      logger.success(`钱包 [${this.index}] 创建新用户成功: ${username}`);

      return {
        success: true,
        userExists: false,
        username: username,
        lastChange: new Date().toISOString(),
        nextChangeDate: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(),
        msg: `创建新用户: ${username}`
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 创建新用户失败: ${error.message}`);
      throw error;
    }
  }

  // 查询世界信息
  async #getWorldInfo() {
    try {
      const walletAddress = this.wallet.address;
      logger.info(`钱包 [${this.index}] 查询世界信息，钱包地址: ${walletAddress}`);

      try {
        const response = await axios.get(`${this.SUPABASE_URL}/rest/v1/worlds`, {
          params: {
            select: '*',
            wallet_address: `eq.${walletAddress}`,
            is_active: 'eq.true'
          },
          ...this.#createAxiosConfig()
        });

        // 检查返回的数据
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          const worldData = response.data[0]; // 取第一个世界数据
          logger.success(`钱包 [${this.index}] 找到活跃世界: ID=${worldData.id}, dreamers=${worldData.dreamers}, artifacts=${worldData.artifacts}, stardusts=${worldData.stardusts}`);

          return {
            success: true,
            worldExists: true,
            worldData: worldData,
            id: worldData.id,
            dreamers: worldData.dreamers,
            artifacts: worldData.artifacts,
            stardusts: worldData.stardusts,
            capacity: worldData.capacity,
            stars: worldData.stars,
            dreamFadesTime: worldData.dream_fades_time,
            worldDestroyTime: worldData.world_destroy_time,
            createdAt: worldData.created_at,
            lastArtifactGather: worldData.last_artifact_gather,
            lastStardustGather: worldData.last_stardust_gather,
            lastArtifactSpeedup: worldData.last_artifact_speedup,
            lastStardustSpeedup: worldData.last_stardust_speedup,
            msg: `找到活跃世界: ID=${worldData.id}`
          };
        } else {
          // 响应成功但没有数据，说明世界不存在
          logger.info(`钱包 [${this.index}] 没有找到活跃的世界`);

          return {
            success: true,
            worldExists: false,
            worldData: null,
            msg: `没有找到活跃的世界`
          };
        }
      } catch (error) {
        // 检查是否是406状态码，表示世界不存在
        if (error.response && error.response.status === 406) {
          logger.info(`钱包 [${this.index}] 收到406状态码，世界不存在`);
          return {
            success: true,
            worldExists: false,
            worldData: null,
            msg: `世界不存在`
          };
        }
        // 其他错误继续抛出
        throw error;
      }
    } catch (error) {
      logger.error(`钱包 [${this.index}] 查询世界信息失败: ${error.message}`);
      return {
        success: false,
        msg: `查询世界信息失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 在数据库中创建世界记录
  async #createWorldInDB() {
    try {
      const walletAddress = this.wallet.address;
      logger.info(`钱包 [${this.index}] 在数据库中创建世界记录，钱包地址: ${walletAddress}`);

      // 计算时间
      const now = new Date();
      const dreamFadesTime = new Date(now.getTime() + 48 * 60 * 60 * 1000); // 48小时后
      const worldDestroyTime = new Date(now.getTime() + 96 * 60 * 60 * 1000); // 96小时后

      const worldData = {
        wallet_address: walletAddress,
        dreamers: 1,
        artifacts: 0,
        stardusts: 0,
        capacity: 10,
        stars: 0,
        dream_fades_time: dreamFadesTime.toISOString(),
        world_destroy_time: worldDestroyTime.toISOString(),
        is_active: true
      };

      const response = await axios.post(`${this.SUPABASE_URL}/rest/v1/worlds`, worldData, {
        params: {
          columns: '"wallet_address","dreamers","artifacts","stardusts","capacity","stars","dream_fades_time","world_destroy_time","is_active"',
          select: '*'
        },
        ...this.#createAxiosConfig({
          'accept': 'application/vnd.pgrst.object+json',
          'content-type': 'application/json',
          'content-profile': 'public',
          'prefer': 'return=representation'
        })
      });

      if (response.status === 201 && response.data) {
        logger.success(`钱包 [${this.index}] 数据库世界创建成功: ID=${response.data.id}`);

        return {
          success: true,
          worldData: response.data,
          id: response.data.id,
          dreamers: response.data.dreamers,
          artifacts: response.data.artifacts,
          stardusts: response.data.stardusts,
          capacity: response.data.capacity,
          stars: response.data.stars,
          dreamFadesTime: response.data.dream_fades_time,
          worldDestroyTime: response.data.world_destroy_time,
          createdAt: response.data.created_at,
          msg: `数据库世界创建成功: ID=${response.data.id}`
        };
      } else {
        return {
          success: false,
          msg: `数据库世界创建失败: 响应状态 ${response.status}`,
          error: 'Unexpected response status'
        };
      }
    } catch (error) {
      logger.error(`钱包 [${this.index}] 数据库世界创建失败: ${error.message}`);
      return {
        success: false,
        msg: `数据库世界创建失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 更新世界数据
  async #updateWorldInDB(worldId, updateData) {
    try {
      logger.info(`钱包 [${this.index}] 更新世界数据，世界ID: ${worldId}, 更新数据:`, JSON.stringify(updateData));

      const response = await axios.patch(`${this.SUPABASE_URL}/rest/v1/worlds`, updateData, {
        params: {
          id: `eq.${worldId}`
        },
        ...this.#createAxiosConfig({
          'content-type': 'application/json',
          'content-profile': 'public'
        })
      });

      if (response.status === 204) {
        logger.success(`钱包 [${this.index}] 世界数据更新成功: ID=${worldId}`);
        return {
          success: true,
          msg: `世界数据更新成功: ID=${worldId}`,
          worldId: worldId,
          updateData: updateData
        };
      } else {
        return {
          success: false,
          msg: `世界数据更新失败: 响应状态 ${response.status}`,
          error: 'Unexpected response status'
        };
      }
    } catch (error) {
      logger.error(`钱包 [${this.index}] 世界数据更新失败: ${error.message}`);
      return {
        success: false,
        msg: `世界数据更新失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 获取合约实例
  #getContract() {
    return new ethers.Contract(this.ONIRYA_CONTRACT, this.CONTRACT_ABI, this.wallet);
  }

  // 检查STT余额是否足够
  async #checkSTTBalance(requiredAmount = BigInt(0), estimatedGas = BigInt(300000)) {
    try {
      const sttBalance = await this.wallet.provider.getBalance(this.wallet.address);
      const feeData = await this.wallet.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(**********);
      const gasEstimate = estimatedGas * gasPrice;
      const totalSTTRequired = gasEstimate + requiredAmount;

      logger.info(`钱包 [${this.index}] STT余额: ${ethers.formatEther(sttBalance)}`);
      logger.info(`钱包 [${this.index}] 需要STT: ${ethers.formatEther(totalSTTRequired)} (gas: ${ethers.formatEther(gasEstimate)}, 费用: ${ethers.formatEther(requiredAmount)})`);

      if (sttBalance < totalSTTRequired) {
        return {
          success: false,
          msg: `STT余额不足 ｜ 当前: ${ethers.formatEther(sttBalance)} STT, 需要: ${ethers.formatEther(totalSTTRequired)} STT`,
          error: 'Insufficient STT balance'
        };
      }

      return { success: true, gasPrice, gasLimit: estimatedGas };
    } catch (error) {
      return {
        success: false,
        msg: `检查STT余额失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 智能合约创建世界
  async #createWorldContract() {
    try {
      logger.info(`钱包 [${this.index}] 开始执行智能合约createWorld...`);

      const contract = this.#getContract();

      // 先估算 gas
      let gasEstimate;
      try {
        gasEstimate = await contract.createWorld.estimateGas(this.wallet.address);
        // 增加 20% 的 gas 余量
        gasEstimate = gasEstimate * BigInt(120) / BigInt(100);
        logger.info(`钱包 [${this.index}] createWorld gas估算成功: ${gasEstimate.toString()}`);
      } catch (estimateError) {
        logger.info(`钱包 [${this.index}] createWorld gas估算失败，使用默认值: ${estimateError.message}`);
        gasEstimate = BigInt(300000); // 默认值
      }

      // 检查余额（包含估算的gas费用）
      const balanceCheck = await this.#checkSTTBalance(BigInt(0), gasEstimate);
      if (!balanceCheck.success) {
        return balanceCheck;
      }

      const tx = await contract.createWorld(this.wallet.address, {
        gasLimit: gasEstimate,
        gasPrice: balanceCheck.gasPrice
      });

      logger.info(`钱包 [${this.index}] createWorld交易...`);
      const receipt = await tx.wait();

      if (receipt.status === 1) {
        logger.success(`钱包 [${this.index}] createWorld成功，tx: ${tx.hash}`)
        return {
          success: true,
          msg: `智能合约createWorld成功 ｜ 哈希：${tx.hash}`,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          action: 'createWorldContract'
        };
      }else{
        logger.error(`钱包 [${this.index}] createWorld失败`)
      }

      return {
        success: false,
        msg: `智能合约createWorld失败 ｜ 哈希：${tx.hash}`,
        transactionHash: tx.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      return {
        success: false,
        msg: `智能合约createWorld失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }

  // 创建世界（完整流程：检查存在性 -> 智能合约 -> 数据库）
  async #createWorld() {
    try {
      logger.info(`钱包 [${this.index}] 开始创建世界流程...`);

      // 1. 先检查世界是否已存在
      logger.info(`钱包 [${this.index}] 检查世界是否已存在...`);
      const worldInfoResult = await this.#getWorldInfo();

      if (!worldInfoResult.success) {
        return {
          success: false,
          msg: `创建世界失败：查询世界信息失败 - ${worldInfoResult.msg}`,
          error: worldInfoResult.error,
          step: 'checkWorldExists'
        };
      }

      // 2. 如果世界已存在，直接返回成功
      if (worldInfoResult.worldExists) {
        logger.success(`钱包 [${this.index}] 世界已存在，跳过创建流程`);
        return {
          success: true,
          msg: `世界已存在，无需创建 ｜ 世界ID：${worldInfoResult.id}`,
          worldExists: true,
          worldId: worldInfoResult.id,
          worldData: worldInfoResult.worldData,
          action: 'createWorld'
        };
      }

      // 3. 世界不存在，调用智能合约创建
      logger.info(`钱包 [${this.index}] 世界不存在，开始调用智能合约创建...`);
      const contractResult = await this.#createWorldContract();

      if (!contractResult.success) {
        return {
          success: false,
          msg: `创建世界失败：智能合约调用失败 - ${contractResult.msg}`,
          error: contractResult.error,
          step: 'createWorldContract',
          transactionHash: contractResult.transactionHash
        };
      }

      // 4. 智能合约成功后，在数据库中创建世界记录
      logger.info(`钱包 [${this.index}] 智能合约创建成功，开始在数据库中创建世界记录...`);
      const dbResult = await this.#createWorldInDB();

      if (dbResult.success) {
        return {
          success: true,
          msg: `创建世界完整流程成功 ｜ 哈希：${contractResult.transactionHash} ｜ 世界ID：${dbResult.id}`,
          transactionHash: contractResult.transactionHash,
          blockNumber: contractResult.blockNumber,
          gasUsed: contractResult.gasUsed,
          worldId: dbResult.id,
          worldData: dbResult.worldData,
          action: 'createWorld'
        };
      } else {
        // 智能合约成功但数据库创建失败，整个流程失败
        logger.error(`钱包 [${this.index}] 智能合约创建成功，但数据库世界创建失败: ${dbResult.msg}`);
        return {
          success: false,
          msg: `创建世界失败：数据库创建失败 - ${dbResult.msg}`,
          transactionHash: contractResult.transactionHash,
          blockNumber: contractResult.blockNumber,
          gasUsed: contractResult.gasUsed,
          error: dbResult.error,
          step: 'createWorldInDB',
          action: 'createWorld'
        };
      }

    } catch (error) {
      return {
        success: false,
        msg: `创建世界流程失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 收集神器
  async #gatherArtifacts(amount = 1) {
    try {
      logger.info(`钱包 [${this.index}] 开始执行gatherArtifacts, amount: ${amount}...`);

      const contract = this.#getContract();

      // 先估算 gas
      let gasEstimate;
      try {
        gasEstimate = await contract.gatherArtifacts.estimateGas(this.wallet.address, BigInt(amount));
        // 增加 20% 的 gas 余量
        gasEstimate = gasEstimate * BigInt(120) / BigInt(100);
        logger.info(`钱包 [${this.index}] gatherArtifacts gas估算成功: ${gasEstimate.toString()}`);
      } catch (estimateError) {
        logger.info(`钱包 [${this.index}] gatherArtifacts gas估算失败，使用默认值: ${estimateError.message}`);
        gasEstimate = BigInt(300000); // 默认值
      }

      // 检查余额（包含估算的gas费用）
      const balanceCheck = await this.#checkSTTBalance(BigInt(0), gasEstimate);
      if (!balanceCheck.success) {
        return balanceCheck;
      }

      const tx = await contract.gatherArtifacts(this.wallet.address, BigInt(amount), {
        gasLimit: gasEstimate,
        gasPrice: balanceCheck.gasPrice
      });

      logger.info(`钱包 [${this.index}] gatherArtifacts交易已发送...`);
      const receipt = await tx.wait();

      if (receipt.status === 1) {
        logger.success(`钱包 [${this.index}] gatherArtifacts交易成功, tx:${tx.hash}`)
        return {
          success: true,
          msg: `gatherArtifacts成功 ｜ ${amount} 个 ｜ 哈希：${tx.hash}`,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          amount: amount,
          action: 'gatherArtifacts'
        };
      }

      return {
        success: false,
        msg: `gatherArtifacts失败 ｜ 哈希：${tx.hash}`,
        transactionHash: tx.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      return {
        success: false,
        msg: `gatherArtifacts失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }

  // 收集星尘
  async #gatherStardusts(amount = 1) {
    try {
      logger.info(`钱包 [${this.index}] 开始执行gatherStardusts...`);

      const contract = this.#getContract();

      // 先估算 gas
      let gasEstimate;
      try {
        gasEstimate = await contract.gatherStardusts.estimateGas(this.wallet.address, BigInt(amount));
        // 增加 20% 的 gas 余量
        gasEstimate = gasEstimate * BigInt(120) / BigInt(100);
        logger.info(`钱包 [${this.index}] gatherStardusts gas估算成功: ${gasEstimate.toString()}`);
      } catch (estimateError) {
        logger.info(`钱包 [${this.index}] gatherStardusts gas估算失败，使用默认值: ${estimateError.message}`);
        gasEstimate = BigInt(300000); // 默认值
      }

      // 检查余额（包含估算的gas费用）
      const balanceCheck = await this.#checkSTTBalance(BigInt(0), gasEstimate);
      if (!balanceCheck.success) {
        return balanceCheck;
      }

      const tx = await contract.gatherStardusts(this.wallet.address, BigInt(amount), {
        gasLimit: gasEstimate,
        gasPrice: balanceCheck.gasPrice
      });

      logger.info(`钱包 [${this.index}] gatherStardusts交易已发送...`);
      const receipt = await tx.wait();

      if (receipt.status === 1) {
        logger.success(`钱包 [${this.index}] gatherStardusts交易成功, tx:${tx.hash}`)

        return {
          success: true,
          msg: `gatherStardusts成功 ｜ ${amount} 个 ｜ 哈希：${tx.hash}`,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          amount: amount,
          action: 'gatherStardusts'
        };
      }

      return {
        success: false,
        msg: `gatherStardusts失败 ｜ 哈希：${tx.hash}`,
        transactionHash: tx.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      return {
        success: false,
        msg: `gatherStardusts失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }

  // 资源加速
  async #speedUpResource(resourceType = 'artifacts') {
    try {
      logger.info(`钱包 [${this.index}] 开始执行speedUpResource, resourceType: ${resourceType}...`);
      
      // speedUpResource 需要支付 0.0125 STT
      const requiredSTT = ethers.parseEther('0.0125');
      const contract = this.#getContract();

      // 先估算 gas
      let gasEstimate;
      try {
        gasEstimate = await contract.speedUpResource.estimateGas(this.wallet.address, resourceType, {
          value: requiredSTT
        });
        // 增加 20% 的 gas 余量
        gasEstimate = gasEstimate * BigInt(120) / BigInt(100);
        logger.info(`钱包 [${this.index}] gas估算成功: ${gasEstimate.toString()}`);
      } catch (estimateError) {
        logger.info(`钱包 [${this.index}] gas估算失败，使用默认值: ${estimateError.message}`);
        gasEstimate = BigInt(500000); // 使用更高的默认值
      }

      // 检查余额（包含估算的gas费用）
      const balanceCheck = await this.#checkSTTBalance(requiredSTT, gasEstimate);
      if (!balanceCheck.success) {
        return balanceCheck;
      }

      const tx = await contract.speedUpResource(this.wallet.address, resourceType, {
        value: requiredSTT,
        gasLimit: gasEstimate,
        gasPrice: balanceCheck.gasPrice
      });

      logger.info(`钱包 [${this.index}] speedUpResource交易已发送: ${tx.hash}`);
      const receipt = await tx.wait();

      if (receipt.status === 1) {
        return {
          success: true,
          msg: `speedUpResource成功 ｜ ${resourceType} ｜ 哈希：${tx.hash}`,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          resourceType: resourceType,
          cost: '0.0125 STT',
          action: 'speedUpResource'
        };
      }

      return {
        success: false,
        msg: `speedUpResource失败 ｜ 哈希：${tx.hash}`,
        transactionHash: tx.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      // 检查是否是合约执行回滚错误
      let errorMsg = error.message;
      if (error.message.includes('transaction execution reverted')) {
        errorMsg = `合约执行回滚，可能原因：1)冷却时间未到 2)资源类型无效 3)合约状态检查失败`;
      }

      return {
        success: false,
        msg: `speedUpResource失败, 错误信息: ${errorMsg}`,
        error: error.message,
        resourceType: resourceType
      };
    }
  }

  // 执行完整的游戏流程
  async execute() {
    const results = [];

    try {

      // 1. 获取或创建用户
      logger.info(`钱包 [${this.index}] 步骤 1: 获取或创建用户`);
      const userInfoResult = await this.#getOrCreateUser();
      results.push(userInfoResult);

      if (!userInfoResult.success) {
        return {
          success: false,
          msg: `execute失败：获取或创建用户失败 - ${userInfoResult.msg}`,
          results: results,
          failedStep: 'getOrCreateUser'
        };
      }

      logger.info(`钱包 [${this.index}] 用户信息: ${userInfoResult.msg}`);
      await randomSleep(1, 3);

      // 2. 创建世界（包含检查存在性）
      logger.info(`钱包 [${this.index}] 步骤 2: 创建世界`);
      const createWorldResult = await this.#createWorld();
      results.push(createWorldResult);

      if (!createWorldResult.success) {
        return {
          success: false,
          msg: `execute失败：创建世界失败 - ${createWorldResult.msg}`,
          results: results,
          failedStep: 'createWorld'
        };
      }

      logger.success(`钱包 [${this.index}] 创建世界: ${createWorldResult.msg}`);
      await randomSleep(1, 3);

      // 3. 获取世界信息（从createWorld结果中获取，或查询数据库）
      let worldInfo;
      if (createWorldResult.worldExists && createWorldResult.worldData) {
        // 世界已存在，使用返回的世界数据
        worldInfo = {
          id: createWorldResult.worldId,
          artifacts: createWorldResult.worldData.artifacts || 0,
          stardusts: createWorldResult.worldData.stardusts || 0,
          ...createWorldResult.worldData
        };
        logger.info(`钱包 [${this.index}] 使用已存在世界信息: artifacts=${worldInfo.artifacts}, stardusts=${worldInfo.stardusts}`);
      } else if (createWorldResult.worldData) {
        // 新创建的世界，使用返回的世界数据
        worldInfo = {
          id: createWorldResult.worldId,
          artifacts: createWorldResult.worldData.artifacts || 0,
          stardusts: createWorldResult.worldData.stardusts || 0,
          ...createWorldResult.worldData
        };
        logger.info(`钱包 [${this.index}] 使用新创建世界信息: artifacts=${worldInfo.artifacts}, stardusts=${worldInfo.stardusts}`);
      } else {
        // 备用方案：查询世界信息
        logger.info(`钱包 [${this.index}] 步骤 3: 查询世界信息（备用方案）`);
        const worldInfoResult = await this.#getWorldInfo();
        results.push(worldInfoResult);

        if (!worldInfoResult.success || !worldInfoResult.worldExists) {
          return {
            success: false,
            msg: `execute失败：查询世界信息失败或世界不存在 - ${worldInfoResult.msg}`,
            results: results,
            failedStep: 'getWorldInfo'
          };
        }

        worldInfo = {
          id: worldInfoResult.id,
          artifacts: worldInfoResult.artifacts || 0,
          stardusts: worldInfoResult.stardusts || 0,
          ...worldInfoResult.worldData
        };
        logger.info(`钱包 [${this.index}] 查询到世界信息: artifacts=${worldInfo.artifacts}, stardusts=${worldInfo.stardusts}`);
      }

      await randomSleep(1, 3);

      // 4. 根据资源数量决定是否需要收集（随机顺序执行）
      const gatherTasks = [];

      // 4.1 检查artifacts数量
      if (worldInfo.artifacts < 2) {
        const artifactsAmount = Math.floor(Math.random() * 3) + 2; // 2-4随机数
        gatherTasks.push({
          type: 'artifacts',
          method: () => this.#gatherArtifacts(artifactsAmount),
          name: 'gatherArtifacts',
          currentAmount: worldInfo.artifacts,
          gatherAmount: artifactsAmount
        });
      } else {
        logger.success(`钱包 [${this.index}] artifacts数量已足够(${worldInfo.artifacts})，跳过收集`);
      }

      // 4.2 检查stardusts数量
      if (worldInfo.stardusts < 2) {
        const stardustsAmount = Math.floor(Math.random() * 3) + 2; // 2-4随机数
        gatherTasks.push({
          type: 'stardusts',
          method: () => this.#gatherStardusts(stardustsAmount),
          name: 'gatherStardusts',
          currentAmount: worldInfo.stardusts,
          gatherAmount: stardustsAmount
        });
      } else {
        logger.success(`钱包 [${this.index}] stardusts数量已足够(${worldInfo.stardusts})，跳过收集`);
      }

      // 随机打乱任务顺序
      for (let i = gatherTasks.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [gatherTasks[i], gatherTasks[j]] = [gatherTasks[j], gatherTasks[i]];
      }

      // 执行收集任务
      for (let i = 0; i < gatherTasks.length; i++) {
        const task = gatherTasks[i];
        logger.info(`钱包 [${this.index}] 步骤 4.${i + 1}: ${task.name}(${task.gatherAmount}个)`);

        const gatherResult = await task.method();
        results.push(gatherResult);

        if (gatherResult.success) {
          logger.success(`钱包 [${this.index}] ${task.name}成功: ${gatherResult.msg}`);

          // 智能合约成功后，更新世界数据
          const newAmount = task.currentAmount + task.gatherAmount;
          const updateData = {};

          if (task.type === 'artifacts') {
            updateData.artifacts = newAmount;
            updateData.last_artifact_gather = new Date().toISOString();
          } else if (task.type === 'stardusts') {
            updateData.stardusts = newAmount;
            updateData.last_stardust_gather = new Date().toISOString();
          }

          logger.info(`钱包 [${this.index}] 开始更新世界数据: ${task.type}=${newAmount} (收集了${task.gatherAmount}个)`);
          const updateResult = await this.#updateWorldInDB(worldInfo.id, updateData);

          if (updateResult.success) {
            logger.info(`钱包 [${this.index}] 世界数据更新成功: ${task.type}=${newAmount}`);
            // 更新本地worldInfo以便后续任务使用最新数据
            worldInfo[task.type] = newAmount;
          } else {
            logger.warn(`钱包 [${this.index}] 世界数据更新失败: ${updateResult.msg}`);
          }
        } else {
          logger.warn(`钱包 [${this.index}] ${task.name}失败: ${gatherResult.msg}`);
          // 注意：这里不终止执行，继续其他任务
        }

        await randomSleep(1, 3);
      }

      logger.info(`钱包 [${this.index}] execute完整流程执行成功！`);

      const successfulTransactions = results.filter(r => r.success).length;
      const failedTransactions = results.filter(r => !r.success).length;

      return {
        success: true,
        msg: `execute完整流程执行成功 (成功: ${successfulTransactions}, 失败: ${failedTransactions})`,
        results: results,
        totalSteps: 3 + gatherTasks.length,
        totalTransactions: successfulTransactions,
        successfulTransactions: successfulTransactions,
        failedTransactions: failedTransactions
      };

    } catch (error) {
      return {
        success: false,
        msg: `execute执行时发生错误: ${error.message}`,
        results: results,
        error: error.message
      };
    }
  }
}

export default Onirya;
