import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { ethers } from 'ethers';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { getHeaders } from "../../base/tools/fake_useragent.js";


class Somnex {
    constructor(wallet, proxyString, captchaString) {
        this.wallet = wallet;
        this.proxyString = proxyString;
        this.captchaString = captchaString;

        // Token addresses from transaction data
        this.WSTT_ADDRESS = '******************************************'; // Wrapped STT
        this.USDT_X_ADDRESS = '******************************************'; // USDT.X
        this.ROUTER_ADDRESS = '******************************************'; // Router contract
        this.PERP_CONTRACT = '******************************************'; // Perp contract
        this.LANUCHPAD_CONTRACT = '******************************************'
        this.LANUCHPAD_ABI = [
            {
                "inputs": [
                    { "internalType": "string", "name": "name", "type": "string" },
                    { "internalType": "string", "name": "symbol", "type": "string" },
                    { "internalType": "string", "name": "url", "type": "string" }
                ],
                "name": "launchpad",
                "outputs": [
                    { "internalType": "address", "name": "token", "type": "address" },
                    { "internalType": "address", "name": "curve", "type": "address" }
                ],
                "stateMutability": "payable",
                "type": "function"
            }
            // Include the full ABI if needed for other functions
        ];
    }

    /**
     * 生成随机NIA交换金额 - 更新范围
     */
    generateRandomSwapAmount() {
        const min = 0.01;
        const max = 0.05;
        return (Math.random() * (max - min) + min).toFixed(2);
    }

    async executeSwap() {
        try {
            const randomAmount = this.generateRandomSwapAmount();
            const amountIn = ethers.parseEther(randomAmount.toString());

            const deadline = Math.floor(Date.now() / 1000) + 1800;
            const estimatedRate = 7614;
            const estimatedAmountOut = Math.floor(randomAmount * estimatedRate * 1e6);
            const minAmountOut = 0;

            const methodId = '0x04a5baf1';
            const params = [
                '0x00000000000000000000000000000000000000000000000000000000000000c0',
                this.USDT_X_ADDRESS.toLowerCase().replace('0x', '0x000000000000000000000000'),
                '0x0000000000000000000000000000000000000000000000000000000000000000',
                deadline.toString(16).padStart(64, '0'),
                this.wallet.address.toLowerCase().replace('0x', '0x000000000000000000000000'),
                deadline.toString(16).padStart(64, '0'),
                '0x0000000000000000000000000000000000000000000000000000000000000001',
                '0x0000000000000000000000000000000000000000000000000000000000000020',
                '0x0000000000000000000000000000000000000000000000000000000000000001',
                this.WSTT_ADDRESS.toLowerCase().replace('0x', '0x000000000000000000000000'),
                this.USDT_X_ADDRESS.toLowerCase().replace('0x', '0x000000000000000000000000'),
                amountIn.toString(16).padStart(64, '0'),
                '0x0000000000000000000000000000000000000000000000000000000000000000',
                '0x00000000000000000000000000000000000000000000000000000000000000c0',
                '0x0000000000000000000000000000000000000000000000000000000000000000'
            ];

            const transactionData = methodId + params.join('').replace(/0x/g, '');

            const balance = await this.wallet.provider.getBalance(this.wallet.address);
            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);
            const gasLimit = BigInt(600000);
            const gasEstimate = gasLimit * gasPrice;
            const totalRequired = amountIn + gasEstimate;

            if (balance < totalRequired) {
                return {
                    success: false,
                    msg: `余额不足 ｜ 当前余额: ${ethers.formatEther(balance)} STT, 需要: ${ethers.formatEther(totalRequired)} STT`,
                    error: 'Insufficient balance'
                };
            }

            const tx = {
                to: this.ROUTER_ADDRESS,
                data: transactionData,
                value: amountIn,
                gasLimit: gasLimit,
                gasPrice: gasPrice,
                chainId: 50312
            };

            const txResponse = await this.wallet.sendTransaction(tx);
            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                return {
                    success: true,
                    msg: `交易完成 ｜ 兑换 ${randomAmount} STT -> USDT.X ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    amountIn: randomAmount,
                    tokenIn: 'STT',
                    tokenOut: 'USDT.X'
                };
            } else {
                return {
                    success: false,
                    msg: `交易失败 ｜ 交易哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    error: 'Transaction failed on blockchain'
                };
            }

        } catch (error) {
            console.error('Error executing Somnex swap:', error);

            // Handle transaction revert FIRST (most specific)
            if (error.message && error.message.includes('transaction execution reverted')) {
                return {
                    success: false,
                    msg: '交易被拒绝 ｜ 可能是滑点过大或流动性不足，或者合约调用参数错误',
                    error: 'Transaction reverted'
                };
            }

            // Handle specific error types
            if (error.message && error.message.includes('INSUFFICIENT_BALANCE')) {
                return {
                    success: false,
                    msg: '余额不足 ｜ 请确保钱包有足够的 STT 代币',
                    error: 'Insufficient balance'
                };
            }

            // Handle gas-related errors (but not revert errors)
            if (error.message && !error.message.includes('transaction execution reverted') && (
                error.message.includes('out of gas') ||
                error.message.includes('gas required exceeds allowance') ||
                error.message.includes('intrinsic gas too low') ||
                error.message.includes('gas limit') ||
                error.code === 'UNPREDICTABLE_GAS_LIMIT'
            )) {
                return {
                    success: false,
                    msg: 'Gas 费用错误 ｜ 交易可能失败或网络拥堵，请稍后重试',
                    error: 'Gas estimation failed'
                };
            }

            // Handle network errors
            if (error.message && (
                error.message.includes('network') ||
                error.message.includes('timeout') ||
                error.message.includes('connection')
            )) {
                return {
                    success: false,
                    msg: '网络错误 ｜ 请检查网络连接',
                    error: 'Network error'
                };
            }

            return {
                success: false,
                msg: '交易失败 ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async approve() {
        try {
            // console.log('检查USDT.X授权状态...');

            // 使用标准ERC20 ABI检查授权状态
            const erc20Abi = [
                "function allowance(address owner, address spender) view returns (uint256)",
                "function approve(address spender, uint256 amount) returns (bool)"
            ];

            // 正确的授权目标地址 - 从成功的前端交易中获取
            const PERP_ROUTER_ADDRESS = '******************************************';

            const usdtContract = new ethers.Contract(this.USDT_X_ADDRESS, erc20Abi, this.wallet);

            // 检查当前授权额度
            const currentAllowance = await usdtContract.allowance(this.wallet.address, PERP_ROUTER_ADDRESS);
            const requiredAmount = ethers.parseUnits('100000', 6);

            // console.log('当前授权额度:', ethers.formatUnits(currentAllowance, 6), 'USDT.X');
            // console.log('需要授权额度:', ethers.formatUnits(requiredAmount, 6), 'USDT.X');
            // console.log('授权目标地址:', PERP_ROUTER_ADDRESS);

            if (currentAllowance >= requiredAmount) {
                console.log('已有足够的授权额度，无需重新授权');
                return {
                    success: true,
                    msg: `USDT.X 已授权 (当前额度: ${ethers.formatUnits(currentAllowance, 6)} USDT.X)`,
                    alreadyApproved: true,
                    currentAllowance: currentAllowance.toString()
                };
            }

            // console.log('开始执行Contract授权交易...');

            // 使用Contract接口进行授权，授权最大值
            const maxUint256 = ethers.MaxUint256;

            // console.log('Contract授权参数:');
            // console.log('- Perp Router地址:', PERP_ROUTER_ADDRESS);
            // console.log('- 授权数量:', maxUint256.toString());

            const txResponse = await usdtContract.approve(PERP_ROUTER_ADDRESS, maxUint256);
            console.log('授权交易已发送:', txResponse.hash);
            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                return {
                    success: true,
                    msg: `USDT.X 授权完成 (Contract方式) ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    approvedAmount: maxUint256.toString()
                };
            } else {
                return {
                    success: false,
                    msg: `授权失败 ｜ 哈希：${txResponse.hash}`,
                    error: 'Approval transaction failed'
                };
            }

        } catch (error) {
            console.error('授权错误详情:', error);

            // 如果是因为已经授权导致的回滚，再次检查授权状态
            if (error.message && error.message.includes('transaction execution reverted')) {
                console.log('交易回滚，再次检查授权状态...');

                try {
                    const PERP_ROUTER_ADDRESS = '******************************************';
                    const erc20Abi = [
                        "function allowance(address owner, address spender) view returns (uint256)"
                    ];
                    const usdtContract = new ethers.Contract(this.USDT_X_ADDRESS, erc20Abi, this.wallet.provider);
                    const currentAllowance = await usdtContract.allowance(this.wallet.address, PERP_ROUTER_ADDRESS);
                    const requiredAmount = ethers.parseUnits('50', 6);

                    if (currentAllowance >= requiredAmount) {
                        return {
                            success: true,
                            msg: `USDT.X 已授权 (检测到足够额度: ${ethers.formatUnits(currentAllowance, 6)} USDT.X)`,
                            alreadyApproved: true,
                            currentAllowance: currentAllowance.toString()
                        };
                    }
                } catch (recheckError) {
                    console.log('重新检查授权失败:', recheckError.message);
                }
            }

            return {
                success: false,
                msg: 'Contract授权失败 ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async executePerpSwap() {
        try {
            // 使用随机的10-20 USDT.X金额
            const minAmount = 10;
            const maxAmount = 20;
            const randomAmount = Math.floor(Math.random() * (maxAmount - minAmount + 1)) + minAmount;
            const formattedAmount = randomAmount; // 直接使用整数，不需要parseFloat
            const amountInUSDT = ethers.parseUnits(formattedAmount.toString(), 6);

            // Calculate STT value to send (based on reference: ~0.2 STT for perp trade)
            const sttValue = ethers.parseEther('0.2'); // Fixed 0.2 STT value

            // console.log('Perp交易参数:');
            // console.log('USDT.X数量:', formattedAmount, '(随机10-30)');
            // console.log('STT数量:', ethers.formatEther(sttValue));
            // console.log('钱包地址:', this.wallet.address);

            // 使用你提供的成功交易hex数据作为模板
            let transactionData = '0xe8abf379000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000020000000000000000000000000aa8ce34f12dc3c3daa9304ab451f0a247a9a8c2500000000000000000000000000000000000000000000000000000000000000**********000000000000000000000000000000000000000002c68af0bb14000000000000000000000000000000000000000000000000000000000000000001e4ee6eed9f00000000000000000000000000000000000000000000000000000000000000200000000000000000000000008c8b11c235ad831130282ada03ded2014040c2ac000000000000000000000000f2099ddb6c928d81dcbf9235e15f83fea248047300000000000000000000000000000000000000000000000000000000000001800000000000000000000000002260fac5e5542a773aa44fbcfedf7c193bc2c5990000000000000000000000000000000000000000000000000000000000b71b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000012e56d1c583d9a653526bb68000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001412eb3e2b86e0ea7b725f6ed0000000000000000000000000000000000000000000000000000002c68af0bb140000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000061687474703a2f2f636f6d756e696f6e2d617661746172732e73332e61702d6e6f727468656173742d312e616d617a6f6e6177732e636f6d2f38613862653665652d653536632d343665322d623536322d3536653536653536653536652e6a706567000000000000000000000000000000000000000000000000000000';

            // 替换代币名称 "ojjh" (6f6a6a68) 为新的代币名称
            const originalNameHex = '6f6a6a68'; // "ojjh" 的hex编码
            const newNameHex = Buffer.from(tokenName, 'utf8').toString('hex');
            const newNamePadded = newNameHex.padEnd(8, '0'); // 确保长度一致

            console.log('替换代币名称:');
            console.log('- 原始名称hex:', originalNameHex);
            console.log('- 新名称hex:', newNameHex);
            console.log('- 新名称填充后:', newNamePadded);

            // 替换两个位置的代币名称（name和symbol）
            templateData = templateData.replace(new RegExp(originalNameHex, 'g'), newNamePadded);

            // 替换UUID部分
            const originalUUID = '38613862653665652d653536632d343665322d623536322d3536653536653536653536652e6a706567'; // 原始UUID的hex
            const newUUIDHex = Buffer.from(`${uuid}.jpeg`, 'utf8').toString('hex');

            console.log('替换UUID:');
            console.log('- 原始UUID hex长度:', originalUUID.length);
            console.log('- 新UUID hex长度:', newUUIDHex.length);

            // 如果新UUID长度不同，需要调整
            if (newUUIDHex.length !== originalUUID.length) {
                console.log('UUID长度不同，使用完整重构方法...');
                // 如果UUID长度不同，回退到原始方法
                return await this.createtoken();
            }

            templateData = templateData.replace(originalUUID, newUUIDHex);

            console.log('模板替换完成:');
            console.log('- 最终数据长度:', templateData.length);
            console.log('- 数据预览:', templateData.substring(0, 100) + '...');

            // 发送创建代币交易
            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);
            const gasLimit = BigInt(543577);

            const tx = {
                to: '******************************************',
                data: templateData,
                gasLimit: gasLimit,
                gasPrice: gasPrice,
                chainId: 50312
            };

            console.log('发送代币创建交易（模板方法）...');
            console.log('交易目标:', tx.to);
            console.log('交易数据长度:', tx.data.length);
            console.log('Gas限制:', tx.gasLimit.toString());

            const txResponse = await this.wallet.sendTransaction(tx);
            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                console.log('✅ 代币创建成功（模板方法）！');
                return {
                    success: true,
                    msg: `代币创建成功 ｜ 名称: ${tokenName} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    tokenName: tokenName,
                    tokenSymbol: tokenName,
                    imageUrl: imageUrl,
                    uuid: uuid,
                    method: 'template'
                };
            } else {
                return {
                    success: false,
                    msg: `代币创建失败（模板方法） ｜ 交易状态: ${receipt.status} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    error: 'Transaction failed'
                };
            }

        } catch (error) {
            console.error('Error creating token with template:', error);

            if (error.message && error.message.includes('transaction execution reverted')) {
                return {
                    success: false,
                    msg: '代币创建被拒绝（模板方法） ｜ 可能是参数错误或合约状态问题',
                    error: 'Transaction reverted'
                };
            }

            return {
                success: false,
                msg: '代币创建失败（模板方法） ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async createTokenRaw() {
        try {
            console.log('开始创建代币流程（原始方法）...');

            // 1. 先进行USDT.X授权
            console.log('步骤1: 执行USDT.X授权...');
            const approveResult = await this.approveForTokenCreation();
            if (!approveResult.success) {
                return {
                    success: false,
                    msg: `授权失败: ${approveResult.msg}`,
                    error: 'Approval failed'
                };
            }
            console.log('授权成功:', approveResult.msg);

            // 2. 生成代币信息
            const tokenName = this.generateRandomTokenName();
            console.log('生成代币名称:', tokenName);

            // 随机生成UUID并构建图片URL
            const uuid = this.generateUUID();
            const imageUrl = `http://comunion-avatars.s3.ap-northeast-1.amazonaws.com/${uuid}.jpeg`;
            console.log('生成UUID:', uuid);
            console.log('图片URL:', imageUrl);

            // 使用成功的交易数据作为模板，但确保名称长度匹配
            // 原始模板是4字符的"ojjh"，我们也生成4字符的名称
            const shortTokenName = tokenName.substring(0, 4); // 确保4字符
            console.log('截取代币名称为4字符:', shortTokenName);

            let templateData = '0xc63f8cd90000000000000000000000000621c8c9fc239980650487d2b36d35fe6be1b7270000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000046f6a6a6800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000046f6a6a6800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000061687474703a2f2f636f6d756e696f6e2d617661746172732e73332e61702d6e6f727468656173742d312e616d617a6f6e6177732e636f6d2f38613862653665652d653536632d343665322d623536322d3536653536653536653536652e6a706567000000000000000000000000000000000000000000000000000000';

            // 替换代币名称 "ojjh" (6f6a6a68) 为新的4字符代币名称
            const originalNameHex = '6f6a6a68'; // "ojjh" 的hex编码
            const newNameHex = Buffer.from(shortTokenName, 'utf8').toString('hex');

            console.log('替换代币名称:');
            console.log('- 原始名称: ojjh');
            console.log('- 原始名称hex:', originalNameHex);
            console.log('- 新名称:', shortTokenName);
            console.log('- 新名称hex:', newNameHex);
            console.log('- 长度匹配:', originalNameHex.length === newNameHex.length);

            if (originalNameHex.length !== newNameHex.length) {
                console.error('名称长度不匹配，无法使用模板方法');
                return {
                    success: false,
                    msg: '代币名称长度不匹配，无法创建',
                    error: 'Name length mismatch'
                };
            }

            // 替换两个位置的代币名称（name和symbol）
            templateData = templateData.replace(new RegExp(originalNameHex, 'g'), newNameHex);

            // 替换UUID部分 - 使用固定长度的UUID
            const originalUUID = '38613862653665652d653536632d343665322d623536322d3536653536653536653536652e6a706567'; // 原始UUID的hex
            const newUUIDHex = Buffer.from(`${uuid}.jpeg`, 'utf8').toString('hex');

            console.log('替换UUID:');
            console.log('- 原始UUID hex长度:', originalUUID.length);
            console.log('- 新UUID hex长度:', newUUIDHex.length);

            if (newUUIDHex.length === originalUUID.length) {
                templateData = templateData.replace(originalUUID, newUUIDHex);
                console.log('UUID替换成功');
            } else {
                console.log('UUID长度不匹配，保持原始UUID');
            }

            console.log('最终交易数据:');
            console.log('- 数据长度:', templateData.length);
            console.log('- 数据预览:', templateData.substring(0, 100) + '...');

            // 获取交易参数
            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);
            const gasLimit = BigInt(2000000);
            const nonce = await this.wallet.provider.getTransactionCount(this.wallet.address);

            // 手动构建交易对象
            const rawTx = {
                to: '******************************************',
                value: '0x0', // 0 ETH
                data: templateData,
                gasLimit: '0x' + gasLimit.toString(16),
                gasPrice: '0x' + gasPrice.toString(16),
                nonce: '0x' + nonce.toString(16),
                chainId: 50312
            };

            console.log('原始交易对象:');
            console.log('- to:', rawTx.to);
            console.log('- data长度:', rawTx.data.length);
            console.log('- data开头:', rawTx.data.substring(0, 50));
            console.log('- gasLimit:', rawTx.gasLimit);
            console.log('- gasPrice:', rawTx.gasPrice);
            console.log('- nonce:', rawTx.nonce);

            // 使用wallet.sendTransaction发送
            console.log('发送原始交易...');
            const txResponse = await this.wallet.sendTransaction(rawTx);
            console.log('交易已发送:', txResponse.hash);

            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                console.log('✅ 代币创建成功（原始方法）！');
                return {
                    success: true,
                    msg: `代币创建成功 ｜ 名称: ${shortTokenName} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    tokenName: shortTokenName,
                    tokenSymbol: shortTokenName,
                    imageUrl: imageUrl,
                    uuid: uuid,
                    method: 'raw'
                };
            } else {
                return {
                    success: false,
                    msg: `代币创建失败（原始方法） ｜ 交易状态: ${receipt.status} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    error: 'Transaction failed'
                };
            }

        } catch (error) {
            console.error('Error creating token with raw method:', error);

            if (error.message && error.message.includes('transaction execution reverted')) {
                return {
                    success: false,
                    msg: '代币创建被拒绝（原始方法） ｜ 可能是参数错误或合约状态问题',
                    error: 'Transaction reverted'
                };
            }

            return {
                success: false,
                msg: '代币创建失败（原始方法） ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async createTokenExact() {
        try {
            console.log('开始创建代币流程（精确复制方法）...');

            // 1. 先进行USDT.X授权
            console.log('步骤1: 执行USDT.X授权...');
            const approveResult = await this.approveForTokenCreation();
            if (!approveResult.success) {
                return {
                    success: false,
                    msg: `授权失败: ${approveResult.msg}`,
                    error: 'Approval failed'
                };
            }
            console.log('授权成功:', approveResult.msg);

            // 2. 使用完全相同的成功交易数据，创建代币"ojjh"
            console.log('使用精确的成功交易数据...');

            const exactData = '0xc63f8cd90000000000000000000000000621c8c9fc239980650487d2b36d35fe6be1b727000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000046f6a6a6800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000046f6a6a6800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000061687474703a2f2f636f6d756e696f6e2d617661746172732e73332e61702d6e6f727468656173742d312e616d617a6f6e6177732e636f6d2f38613862653665652d653536632d343665322d623536322d3536653536653536653536652e6a706567000000000000000000000000000000000000000000000000000000';

            console.log('精确交易数据:');
            console.log('- 数据长度:', exactData.length);
            console.log('- 数据预览:', exactData.substring(0, 100) + '...');
            console.log('- 将创建代币: ojjh');
            console.log('- 图片URL: http://comunion-avatars.s3.ap-northeast-1.amazonaws.com/8a8be6ee-e56c-46e2-b562-56e56e56e56e.jpeg');

            // 获取交易参数
            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);
            const gasLimit = BigInt(543577);

            const tx = {
                to: '******************************************',
                data: exactData,
                gasLimit: gasLimit,
                gasPrice: gasPrice,
                chainId: 50312
            };

            console.log('发送精确复制交易...');
            console.log('- 目标合约:', tx.to);
            console.log('- 数据长度:', tx.data.length);
            console.log('- Gas限制:', tx.gasLimit.toString());

            const txResponse = await this.wallet.sendTransaction(tx);
            console.log('交易已发送:', txResponse.hash);

            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                console.log('✅ 代币创建成功（精确复制）！');
                return {
                    success: true,
                    msg: `代币创建成功 ｜ 名称: ojjh ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    tokenName: 'ojjh',
                    tokenSymbol: 'ojjh',
                    imageUrl: 'http://comunion-avatars.s3.ap-northeast-1.amazonaws.com/8a8be6ee-e56c-46e2-b562-56e56e56e56e.jpeg',
                    uuid: '8a8be6ee-e56c-46e2-b562-56e56e56e56e',
                    method: 'exact'
                };
            } else {
                return {
                    success: false,
                    msg: `代币创建失败（精确复制） ｜ 交易状态: ${receipt.status} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    error: 'Transaction failed'
                };
            }

        } catch (error) {
            console.error('Error creating token with exact method:', error);

            if (error.message && error.message.includes('transaction execution reverted')) {
                return {
                    success: false,
                    msg: '代币创建被拒绝（精确复制） ｜ 可能是代币名称已存在或其他合约问题',
                    error: 'Transaction reverted'
                };
            }

            return {
                success: false,
                msg: '代币创建失败（精确复制） ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async createTokenFixed() {
        try {
            console.log('开始创建代币流程（固定16进制方法）...');

            // 1. 先进行USDT.X授权
            console.log('步骤1: 执行USDT.X授权...');
            const approveResult = await this.approveForTokenCreation();
            if (!approveResult.success) {
                return {
                    success: false,
                    msg: `授权失败: ${approveResult.msg}`,
                    error: 'Approval failed'
                };
            }
            console.log('授权成功:', approveResult.msg);

            // 2. 使用用户提供的固定16进制数据
            console.log('使用用户提供的固定16进制数据...');

            const fixedData = '0xc63f8cd90000000000000000000000000621c8c9fc239980650487d2b36d35fe6be1b727000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000047373777500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000473737775000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000061687474703a2f2f636f6d756e696f6e2d617661746172732e73332e61702d6e6f727468656173742d312e616d617a6f6e6177732e636f6d2f35663938336138332d383832352d343638302d626334342d3265386163346565646132342e6a70656700000000000000000000000000000000000000000000000000000000000000';

            console.log('固定16进制数据信息:');
            console.log('- 数据长度:', fixedData.length);
            console.log('- 数据预览:', fixedData.substring(0, 100) + '...');

            // 解析数据中的代币信息（用于日志显示）
            const tokenName = 'sswu';
            const uuid = '5f983a83-8825-4680-bc44-2e8ac4eeda24';
            const imageUrl = `http://comunion-avatars.s3.ap-northeast-1.amazonaws.com/${uuid}.jpeg`;

            console.log('解析的代币信息:');
            console.log('- 代币名称:', tokenName);
            console.log('- UUID:', uuid);
            console.log('- 图片URL:', imageUrl);

            // 获取交易参数
            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);
            const gasLimit = BigInt(543577);

            const tx = {
                to: '******************************************',
                data: fixedData,
                gasLimit: gasLimit,
                gasPrice: gasPrice,
                chainId: 50312
            };

            console.log('发送固定16进制交易...');
            console.log('- 目标合约:', tx.to);
            console.log('- 数据长度:', tx.data.length);
            console.log('- Gas限制:', tx.gasLimit.toString());

            const txResponse = await this.wallet.sendTransaction(tx);
            console.log('交易已发送:', txResponse.hash);

            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                console.log('✅ 代币创建成功（固定16进制）！');
                return {
                    success: true,
                    msg: `代币创建成功 ｜ 名称: ${tokenName} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    tokenName: tokenName,
                    tokenSymbol: tokenName,
                    imageUrl: imageUrl,
                    uuid: uuid,
                    method: 'fixed'
                };
            } else {
                return {
                    success: false,
                    msg: `代币创建失败（固定16进制） ｜ 交易状态: ${receipt.status} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    error: 'Transaction failed'
                };
            }

        } catch (error) {
            console.error('Error creating token with fixed hex:', error);

            if (error.message && error.message.includes('transaction execution reverted')) {
                return {
                    success: false,
                    msg: '代币创建被拒绝（固定16进制） ｜ 可能是代币名称已存在或授权问题',
                    error: 'Transaction reverted'
                };
            }

            return {
                success: false,
                msg: '代币创建失败（固定16进制） ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async getTokenlist() {
        try {
            const url = 'https://testnet.somnex.xyz/api/Launchpadv2/50312/launchpad/list?pageNum=1&pageSize=48&isListed=false&orderBy=totalHoldersSupply&filterProgressMin=10';
            const headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            };

            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                },
                timeout: 30000,
                ...(this.proxyString && { httpsAgent: new HttpsProxyAgent(this.proxyString) })
            };

            console.log('获取代币列表...');
            const response = await axios.get(url, config);

            if (response.data.code === 0) {
                const data = response.data.data;
                // console.log('获取到代币列表，总数:', data.length);

                // 检查是否有代币数据
                if (!data || data.length === 0) {
                    return {
                        success: false,
                        msg: '代币列表为空',
                        error: 'Empty token list'
                    };
                }

                // 随机选择一个代币
                const randomIndex = Math.floor(Math.random() * data.length);
                const selectedToken = data[randomIndex];
                const contractAddress = selectedToken.address;

                // console.log('随机选择的代币信息:');
                // console.log('- 索引:', randomIndex + 1, '/', data.length);
                // console.log('- 名称:', selectedToken.name);
                // console.log('- 符号:', selectedToken.symbol);
                // console.log('- 合约地址:', contractAddress);
                // console.log('- 市值:', selectedToken.marketCap);
                // console.log('- 进度:', selectedToken.progress + '%');
                // console.log('- 创建者:', selectedToken.creator);

                // // 详细记录所有字段，寻找可能的listingId
                // console.log('=== 完整代币数据分析 ===');
                // console.log('所有字段:', Object.keys(selectedToken));
                // console.log('完整数据:', JSON.stringify(selectedToken, null, 2));

                // 检查是否有ID相关字段
                const possibleIdFields = ['id', 'listingId', 'tokenId', 'launchpadId', 'contractId'];
                possibleIdFields.forEach(field => {
                    if (selectedToken[field] !== undefined) {
                        console.log(`发现可能的ID字段 ${field}:`, selectedToken[field]);
                    }
                });

                return {
                    success: true,
                    msg: `随机获取代币成功 ｜ ${selectedToken.name} (${selectedToken.symbol})`,
                    contractAddress: contractAddress,
                    tokenInfo: {
                        address: contractAddress,
                        name: selectedToken.name,
                        symbol: selectedToken.symbol,
                        url: selectedToken.url,
                        marketCap: selectedToken.marketCap,
                        progress: selectedToken.progress,
                        creator: selectedToken.creator,
                        fundraising: selectedToken.fundraising,
                        curve: selectedToken.curve,
                        createTime: selectedToken.createTime,
                        // 添加所有原始字段以便分析
                        rawData: selectedToken
                    },
                    totalTokens: data.length,
                    selectedIndex: randomIndex
                };
            } else {
                return {
                    success: false,
                    msg: '获取代币列表失败 ｜ API返回错误: ' + (response.data.message || '未知错误'),
                    error: 'API error',
                    code: response.data.code
                };
            }
        } catch (error) {
            console.error('Error getting token list:', error);
            return {
                success: false,
                msg: '获取代币列表失败 ｜ ' + (error.message || '网络错误'),
                error: error.message || 'Network error'
            };
        }
    }

    // 测试获取代币列表的方法
    async testGetTokenlist() {
        console.log('=== 测试获取代币列表 ===');

        try {
            const result = await this.getTokenlist();

            if (result.success) {
                console.log('✅ 获取成功!');
                console.log('返回的合约地址:', result.contractAddress);
                console.log('代币信息:', result.tokenInfo);
                console.log('总代币数:', result.totalTokens);
            } else {
                console.log('❌ 获取失败:', result.msg);
            }

            return result;
        } catch (error) {
            console.error('测试失败:', error);
            return { success: false, error: error.message };
        }
    }

    async swapMemeToken(usdtAmount = null) {
        try {
            // console.log('开始Meme代币交换流程...');

            // 1. 获取随机代币信息
            // console.log('步骤1: 获取代币信息...');
            const tokenResult = await this.getTokenlist();
            if (!tokenResult.success) {
                return {
                    success: false,
                    msg: `获取代币信息失败: ${tokenResult.msg}`,
                    error: 'Failed to get token info'
                };
            }

            const tokenInfo = tokenResult.tokenInfo;
            // console.log('选中的代币:', tokenInfo.name, '(' + tokenInfo.symbol + ')');
            // console.log('代币地址:', tokenInfo.address);
            // console.log('Curve地址:', tokenInfo.curve);

            // 2. 获取池子数据
            console.log('步骤2: 获取池子数据...');
            const poolResult = await this.getPoolData(tokenInfo.address);
            if (!poolResult.success) {
                return {
                    success: false,
                    msg: `获取池子数据失败: ${poolResult.msg}`,
                    error: 'Failed to get pool data'
                };
            }

            // 3. 设置支付数量 (默认10 USDT.X)
            const buyAmount = usdtAmount || 10;
            const amountIn = ethers.parseUnits(buyAmount.toString(), 18); // 支付的USDT.X数量 (用于交易)

            // 4. 根据池子数据计算期望获得的代币数量
            console.log('步骤3: 计算期望代币数量...');
            const amountOut = this.calculateExpectedTokenAmount(
                poolResult.tokenAmount,
                poolResult.usdtAmount,
                buyAmount
            );

            // console.log('交易参数:');
            // console.log('- 支付数量 (amountIn):', buyAmount, 'USDT.X =', amountIn.toString(), 'wei');
            // console.log('- 期望获得 (amountOut):', amountOut.toString(), 'wei');
            // console.log('- 池子代币数量:', poolResult.tokenAmount);
            // console.log('- 池子USDT数量:', poolResult.usdtAmount);

            // 5. 构建交易数据
            const methodId = '0xd6febde8'; // buy函数的方法ID
            const amountOutHex = amountOut.toString(16).padStart(64, '0');
            const amountInHex = amountIn.toString(16).padStart(64, '0');

            const transactionData = methodId + amountOutHex + amountInHex;

            // console.log('交易数据构建:');
            // console.log('- 方法ID:', methodId);
            // console.log('- amountOut hex:', amountOutHex);
            // console.log('- amountIn hex:', amountInHex);
            // console.log('- 完整数据:', transactionData);
            // console.log('- 数据长度:', transactionData.length);

            // 6. 检查余额
            const usdtBalance = await this.getUSDTBalance();
            const requiredAmount = ethers.parseUnits(buyAmount.toString(), 6); // USDT.X实际是6位小数

            console.log('余额检查:');
            console.log('- USDT.X余额:', ethers.formatUnits(usdtBalance, 6));
            console.log('- 需要数量:', buyAmount);

            if (usdtBalance < requiredAmount) {
                return {
                    success: false,
                    msg: `USDT.X余额不足 ｜ 当前: ${ethers.formatUnits(usdtBalance, 6)} USDT.X, 需要: ${buyAmount} USDT.X`,
                    error: 'Insufficient USDT.X balance'
                };
            }

            // 7. 授权USDT.X给Curve合约
            console.log('步骤4: 授权USDT.X给Curve合约...');

            // 先尝试Contract方式授权
            let approveResult = await this.approveUSDTForCurveWithContract(tokenInfo.curve, buyAmount);

            // 如果Contract方式失败，尝试原始hex方式
            if (!approveResult.success) {
                console.log('Contract方式授权失败，尝试原始hex方式...');
                approveResult = await this.approveUSDTForCurve(tokenInfo.curve, buyAmount);
            }

            if (!approveResult.success) {
                return {
                    success: false,
                    msg: `USDT.X授权失败: ${approveResult.msg}`,
                    error: 'Approval failed'
                };
            }
            console.log('授权成功:', approveResult.msg);

            // 8. 发送交易到curve合约
            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);
            const gasLimit = BigInt(500000);

            const tx = {
                to: tokenInfo.curve, // 发送到curve合约地址
                data: transactionData,
                gasLimit: gasLimit,
                gasPrice: gasPrice,
                chainId: 50312
            };

            // console.log('发送Meme代币购买交易...');
            // console.log('- 目标合约 (curve):', tx.to);
            // console.log('- 代币合约地址:', tokenInfo.address);
            // console.log('- 数据长度:', tx.data.length);
            // console.log('- Gas限制:', tx.gasLimit.toString());
            // console.log('说明: 交易发送到curve合约，购买代币合约地址的代币');

            const txResponse = await this.wallet.sendTransaction(tx);
            console.log('交易已发送:', txResponse.hash);

            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                console.log('✅ Meme代币购买成功！');
                return {
                    success: true,
                    msg: `Meme代币购买成功 ｜ ${tokenInfo.name} ｜ ${buyAmount} USDT.X ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed.toString(),
                    tokenInfo: tokenInfo,
                    buyAmount: buyAmount,
                    amountIn: amountIn.toString(),
                    amountOut: amountOut.toString(),
                    curveContract: tokenInfo.curve,
                    tokenContract: tokenInfo.address,
                    poolData: {
                        tokenAmount: poolResult.tokenAmount,
                        usdtAmount: poolResult.usdtAmount
                    }
                };
            } else {
                return {
                    success: false,
                    msg: `Meme代币购买失败 ｜ 交易状态: ${receipt.status} ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    error: 'Transaction failed'
                };
            }

        } catch (error) {
            console.error('Error swapping meme token:', error);

            if (error.message && error.message.includes('transaction execution reverted')) {
                return {
                    success: false,
                    msg: 'Meme代币购买被拒绝 ｜ 可能是余额不足、滑点过大或合约状态问题',
                    error: 'Transaction reverted'
                };
            }

            return {
                success: false,
                msg: 'Meme代币购买失败 ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async getPoolData(tokenAddress) {
        try {
            const url = `https://testnet.somnex.xyz/api/Launchpadv2/50312/launchpad/kline/${tokenAddress}/volume_24h`;
            const headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            };

            const config = {
                headers: headers,
                timeout: 30000,
            };
            if (this.proxyString) {
                config.httpsAgent = new HttpsProxyAgent(this.proxyString);
            }
            const response = await axios.get(url, config);
            console.log('获取池子数据:', tokenAddress);

            if (response.data.code === 0) {
                const data = response.data.data;
                console.log('池子数据:');
                console.log('- Token0 (代币):', data.volumeToken0);
                console.log('- Token1 (USDT.X):', data.volumeToken1);

                return {
                    success: true,
                    tokenAmount: data.volumeToken0,
                    usdtAmount: data.volumeToken1
                };
            } else {
                return {
                    success: false,
                    msg: '获取池子数据失败: ' + (response.data.message || '未知错误'),
                    error: 'API error'
                };
            }
        } catch (error) {
            console.error('Error getting pool data:', error);
            return {
                success: false,
                msg: '获取池子数据失败: ' + (error.message || '网络错误'),
                error: error.message || 'Network error'
            };
        }
    }

    calculateExpectedTokenAmount(poolTokenAmount, poolUsdtAmount, usdtInput) {
        const poolTokenBigInt = ethers.parseUnits(poolTokenAmount, 18);
        const poolUsdtBigInt = ethers.parseUnits(poolUsdtAmount, 6);
        const usdtInputBigInt = ethers.parseUnits(usdtInput.toString(), 6);

        const ratio = (usdtInputBigInt * BigInt(10000)) / poolUsdtBigInt; // 放大10000倍避免精度丢失
        const tokenOutput = (poolTokenBigInt * ratio * BigInt(95)) / (BigInt(10000) * BigInt(100)); // 95%折扣

        return tokenOutput;
    }

    async approveUSDTForCurve(curveAddress, amount) {
        try {
            console.log('检查USDT.X授权状态 (用于Curve合约)...');

            // 使用标准ERC20 ABI检查授权状态
            const erc20Abi = [
                "function allowance(address owner, address spender) view returns (uint256)",
                "function approve(address spender, uint256 amount) returns (bool)"
            ];

            const usdtContract = new ethers.Contract(this.USDT_X_ADDRESS, erc20Abi, this.wallet.provider);

            // 检查当前授权额度
            const currentAllowance = await usdtContract.allowance(this.wallet.address, curveAddress);
            const requiredAmount = ethers.parseUnits(amount.toString(), 6); // USDT.X是6位小数

            console.log('当前授权额度:', ethers.formatUnits(currentAllowance, 6), 'USDT.X');
            console.log('需要授权额度:', ethers.formatUnits(requiredAmount, 6), 'USDT.X');

            if (currentAllowance >= requiredAmount) {
                console.log('已有足够的授权额度，无需重新授权');
                return {
                    success: true,
                    msg: `USDT.X 已授权给Curve合约 (当前额度: ${ethers.formatUnits(currentAllowance, 6)} USDT.X)`,
                    alreadyApproved: true,
                    currentAllowance: currentAllowance.toString()
                };
            }

            console.log('开始执行USDT.X授权给Curve合约...');

            const maxUint256 = 'ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff';

            const spenderHex = curveAddress.slice(2).toLowerCase().padStart(64, '0');
            const approveData = '0x095ea7b3' + spenderHex + maxUint256;

            if (!approveData || approveData.length !== 138) {
                console.error('授权数据格式错误!');
                return {
                    success: false,
                    msg: '授权数据构建失败',
                    error: 'Invalid approval data format'
                };
            }

            const feeData = await this.wallet.provider.getFeeData();
            const gasPrice = feeData.gasPrice || BigInt(**********);

            const tx = {
                to: this.USDT_X_ADDRESS,
                data: approveData,
                gasLimit: BigInt(100000),
                gasPrice: gasPrice,
                chainId: 50312
            };

            if (!tx.data || tx.data === '' || tx.data === '0x') {
                console.error('致命错误: 交易数据为空!');
                return {
                    success: false,
                    msg: '交易数据为空，无法发送授权交易',
                    error: 'Empty transaction data'
                };
            }

            const txResponse = await this.wallet.sendTransaction(tx);
            console.log('授权交易已发送:', txResponse.hash);
            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                return {
                    success: true,
                    msg: `USDT.X 授权完成 (给Curve合约) ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    approvedAmount: maxUint256
                };
            } else {
                return {
                    success: false,
                    msg: `授权失败 ｜ 哈希：${txResponse.hash}`,
                    error: 'Approval transaction failed'
                };
            }

        } catch (error) {
            console.error('授权错误详情:', error);

            // 如果是因为已经授权导致的回滚，再次检查授权状态
            if (error.message && error.message.includes('transaction execution reverted')) {
                console.log('交易回滚，再次检查授权状态...');

                try {
                    const erc20Abi = [
                        "function allowance(address owner, address spender) view returns (uint256)"
                    ];
                    const usdtContract = new ethers.Contract(this.USDT_X_ADDRESS, erc20Abi, this.wallet.provider);
                    const currentAllowance = await usdtContract.allowance(this.wallet.address, curveAddress);
                    const requiredAmount = ethers.parseUnits(amount.toString(), 6);

                    if (currentAllowance >= requiredAmount) {
                        return {
                            success: true,
                            msg: `USDT.X 已授权给Curve合约 (检测到足够额度: ${ethers.formatUnits(currentAllowance, 6)} USDT.X)`,
                            alreadyApproved: true,
                            currentAllowance: currentAllowance.toString()
                        };
                    }
                } catch (recheckError) {
                    console.log('重新检查授权失败:', recheckError.message);
                }
            }

            return {
                success: false,
                msg: '授权失败 ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async approveUSDTForCurveWithContract(curveAddress, amount) {
        try {
            console.log('使用Contract接口进行USDT.X授权...');

            // 使用标准ERC20 ABI
            const erc20Abi = [
                "function allowance(address owner, address spender) view returns (uint256)",
                "function approve(address spender, uint256 amount) returns (bool)"
            ];

            const usdtContract = new ethers.Contract(this.USDT_X_ADDRESS, erc20Abi, this.wallet);

            // 检查当前授权额度
            const currentAllowance = await usdtContract.allowance(this.wallet.address, curveAddress);
            const requiredAmount = ethers.parseUnits(amount.toString(), 6);

            console.log('当前授权额度:', ethers.formatUnits(currentAllowance, 6), 'USDT.X');
            console.log('需要授权额度:', ethers.formatUnits(requiredAmount, 6), 'USDT.X');

            if (currentAllowance >= requiredAmount) {
                console.log('已有足够的授权额度，无需重新授权');
                return {
                    success: true,
                    msg: `USDT.X 已授权给Curve合约 (当前额度: ${ethers.formatUnits(currentAllowance, 6)} USDT.X)`,
                    alreadyApproved: true,
                    currentAllowance: currentAllowance.toString()
                };
            }

            console.log('开始执行Contract授权...');

            // 使用最大值授权
            const maxUint256 = ethers.MaxUint256;

            console.log('Contract授权参数:');
            console.log('- Curve合约地址:', curveAddress);
            console.log('- 授权数量:', maxUint256.toString());

            const txResponse = await usdtContract.approve(curveAddress, maxUint256);
            console.log('Contract授权交易已发送:', txResponse.hash);

            const receipt = await txResponse.wait();

            if (receipt.status === 1) {
                return {
                    success: true,
                    msg: `USDT.X 授权完成 (Contract方式) ｜ 哈希：${txResponse.hash}`,
                    transactionHash: txResponse.hash,
                    approvedAmount: maxUint256.toString()
                };
            } else {
                return {
                    success: false,
                    msg: `Contract授权失败 ｜ 哈希：${txResponse.hash}`,
                    error: 'Contract approval transaction failed'
                };
            }

        } catch (error) {
            console.error('Contract授权错误:', error);
            return {
                success: false,
                msg: 'Contract授权失败 ｜ ' + (error.message || '未知错误'),
                error: error.message || 'Unknown error'
            };
        }
    }

    async is_created(index) {
        try {
            const url = `https://testnet.somnex.xyz/api/Launchpadv2/50312/launchpad/list/user/${this.wallet.address}/created`;

            const headers = getHeaders("https://testnet.somnex.xyz/", index);
            const config = {
                headers: headers,
                timeout: 30000,
                ...(this.proxyString && { httpsAgent: new HttpsProxyAgent(this.proxyString) })
            };

            const response = await axios.get(url, config);

            if (response.data.code === 0) {
                const tokens = response.data.data;

                if (tokens && tokens.length > 0) {
                    console.log(`钱包 [${index}] 已经创建过Token`)
                    return {
                        success: true,
                        code: 'CREATED',
                        msg: `已创建过代币 ｜ 数量: ${tokens.length}`,
                        data: {
                            count: tokens.length,
                            tokens: tokens.map(token => ({
                                name: token.name,
                                symbol: token.symbol,
                                address: token.address,
                                createTime: token.createTime
                            }))
                        }
                    };
                } else {
                    return {
                        success: true,
                        code: 'UNCREATED',
                        msg: '未创建过代币',
                        data: {
                            count: 0,
                            tokens: []
                        }
                    };
                }
            } else {
                return {
                    success: false,
                    code: 'API_ERROR',
                    msg: '检查代币创建状态失败 ｜ API返回错误',
                    data: {
                        apiCode: response.data.code,
                        apiMessage: response.data.message || '未知错误'
                    }
                };
            }

        } catch (error) {
            console.error('Error checking token creation status:', error);
            return {
                success: false,
                code: 'REQUEST_ERROR',
                msg: '检查代币创建状态失败 ｜ ' + (error.message || '网络错误'),
                data: {
                    error: error.message || 'Network error'
                }
            };
        }
    }

    async uploadFile(filepath, index) {
        try {
            console.log('开始上传文件流程...');

            // 检查文件是否存在
            if (!fs.existsSync(filepath)) {
                return {
                    success: false,
                    code: 'FILE_NOT_FOUND',
                    msg: '文件不存在',
                    data: null
                };
            }

            // 检查文件扩展名
            const ext = path.extname(filepath).toLowerCase();
            const allowedExtensions = ['.jpg', '.jpeg', '.png'];

            if (!allowedExtensions.includes(ext)) {
                return {
                    success: false,
                    code: 'INVALID_FILE_TYPE',
                    msg: '不支持的文件类型，仅支持 jpg、jpeg、png 格式',
                    data: {
                        allowedTypes: allowedExtensions,
                        providedType: ext
                    }
                };
            }

            // 读取文件内容
            const fileContent = fs.readFileSync(filepath);
            const fileSize = fileContent.length;

            // 检查文件大小（限制为4MB）
            const MAX_FILE_SIZE = 4 * 1024 * 1024;
            if (fileSize > MAX_FILE_SIZE) {
                return {
                    success: false,
                    code: 'FILE_TOO_LARGE',
                    msg: `文件大小超过限制，最大允许 ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
                    data: {
                        maxSize: MAX_FILE_SIZE / (1024 * 1024),
                        currentSize: (fileSize / (1024 * 1024)).toFixed(2)
                    }
                };
            }

            // 构建请求头
            const headers = getHeaders("https://testnet.somnex.xyz", index);
            headers["accept"] = "*/*";
            headers["accept-encoding"] = "identity";
            headers["content-type"] = "text/plain;charset=UTF-8";
            headers["sec-fetch-site"] = "same-origin"

            const config = {
                headers: headers,
                timeout: 30000,
                decompress: false, // 禁用自动解压缩
                responseType: 'text', // 改为text类型
                transformResponse: [(data) => {
                    // 尝试解析响应数据
                    if (typeof data === 'string') {
                        try {
                            return JSON.parse(data);
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            return data;
                        }
                    }
                    return data;
                }],
                ...(this.proxyString && { httpsAgent: new HttpsProxyAgent(this.proxyString) })
            };

            // 将文件内容转换为base64，但不添加MIME类型前缀
            const base64Content = fileContent.toString('base64');

            console.log(`准备上传文件: ${path.basename(filepath)}`);
            console.log(`- 大小: ${(fileSize / (1024 * 1024)).toFixed(2)}MB`);

            // 发送请求
            const response = await axios.post(
                'https://testnet.somnex.xyz/api/v1/share/upload',
                base64Content,  // 直接发送base64内容，不带MIME类型前缀
                config
            );

            // 检查响应数据格式
            if (response.data && typeof response.data === 'object') {
                if (response.data.code === 0 && response.data.data?.url) {
                    console.log(`钱包 [${index}] 文件上传成功: ${response.data.data.url}`);
                    return {
                        success: true,
                        code: 'UPLOAD_SUCCESS',
                        msg: '文件上传成功',
                        data: response.data.data
                    };
                } else {
                    return {
                        success: false,
                        code: 'UPLOAD_FAILED',
                        msg: '上传失败: ' + (response.data.message || '服务器响应异常'),
                        data: response.data
                    };
                }
            } else {
                return {
                    success: false,
                    code: 'INVALID_RESPONSE',
                    msg: '上传失败: 服务器响应格式异常',
                    data: {
                        responseType: typeof response.data,
                        response: response.data
                    }
                };
            }

        } catch (error) {
            console.error('上传错误:', error);
            return {
                success: false,
                code: 'UPLOAD_ERROR',
                msg: '上传失败: ' + (error.message || '未知错误'),
                data: {
                    error: error.message || 'Unknown error',
                    filename: path.basename(filepath || '')
                }
            };
        }
    }

    async launchpad(name, symbol, url) {
        try {
            console.log('开始创建代币...');
            console.log('- 名称:', name);
            console.log('- 符号:', symbol);
            console.log('- 图片URL:', url);

            const abi = [
                {
                    "inputs": [
                        { "internalType": "string", "name": "name", "type": "string" },
                        { "internalType": "string", "name": "symbol", "type": "string" },
                        { "internalType": "string", "name": "url", "type": "string" }
                    ],
                    "name": "launchpad",
                    "outputs": [
                        { "internalType": "address", "name": "token", "type": "address" },
                        { "internalType": "address", "name": "curve", "type": "address" }
                    ],
                    "stateMutability": "payable",
                    "type": "function"
                },
                {
                    "anonymous": false,
                    "inputs": [
                        { "indexed": true, "internalType": "address", "name": "curve", "type": "address" },
                        { "indexed": true, "internalType": "address", "name": "token", "type": "address" },
                        { "indexed": true, "internalType": "address", "name": "fundingToken", "type": "address" },
                        { "indexed": false, "internalType": "address", "name": "user", "type": "address" },
                        { "indexed": false, "internalType": "string", "name": "name", "type": "string" },
                        { "indexed": false, "internalType": "string", "name": "symbol", "type": "string" },
                        { "indexed": false, "internalType": "string", "name": "url", "type": "string" }
                    ],
                    "name": "TokenLaunchpad",
                    "type": "event"
                }
            ];

            const contract = new ethers.Contract(this.LANUCHPAD_CONTRACT, abi, this.wallet);
            const tx = await contract.launchpad(name, symbol, url);
            console.log('代币创建交易已发送:', tx.hash);

            const receipt = await tx.wait();
            const event = receipt.events.find((e) => e.event === "TokenLaunchpad");

            if (event) {
                const result = {
                    token: event.args.token,
                    curve: event.args.curve,
                };

                console.log('代币创建成功:', result);
                return {
                    success: true,
                    code: 'LAUNCHPAD_SUCCESS',
                    msg: `代币创建成功 ｜ ${name} (${symbol}) ｜ 哈希：${tx.hash}`,
                    data: {
                        transactionHash: tx.hash,
                        blockNumber: receipt.blockNumber,
                        gasUsed: receipt.gasUsed.toString(),
                        ...result
                    }
                };
            } else {
                return {
                    success: false,
                    code: 'EVENT_NOT_FOUND',
                    msg: '代币创建失败 ｜ 未找到创建事件',
                    data: {
                        transactionHash: tx.hash,
                        blockNumber: receipt.blockNumber,
                        gasUsed: receipt.gasUsed.toString()
                    }
                };
            }

        } catch (error) {
            console.error('代币创建错误:', error);
            return {
                success: false,
                code: 'LAUNCHPAD_ERROR',
                msg: '代币创建失败: ' + (error.message || '未知错误'),
                data: {
                    error: error.message || 'Unknown error'
                }
            };
        }
    }

    async launchToken(filepath, index) {
        try {
            console.log(`开始创建代币流程 [${index}]...`);

            // 1. 检查是否已创建代币
            const createdCheck = await this.is_created(index);
            if (createdCheck.success) {
                return {
                    success: false,
                    code: 'ALREADY_CREATED',
                    msg: '该钱包已创建过代币',
                    data: createdCheck.data
                };
            }

            // 2. 上传图片文件
            const uploadResult = await this.uploadFile(filepath, index);
            if (!uploadResult.success) {
                return {
                    success: false,
                    code: 'FILE_UPLOAD_ERROR',
                    msg: uploadResult.msg
                }; // 返回上传错误信息
            }
            const imageUrl = uploadResult.data.url;

            // 3. 生成代币名称和符号
            const { faker } = await import('@faker-js/faker');
            const name = faker.company.name().replace(/[^a-zA-Z0-9]/g, '');
            const symbol = name.substring(0, 4).toUpperCase();

            // 4. 调用智能合约创建代币
            const launchpadResult = await this.launchpad(name, symbol, imageUrl);
            if (!launchpadResult.success) {
                return launchpadResult; // 返回创建错误信息
            }

            // 5. 调用API更新代币信息
            const headers = getHeaders("https://testnet.somnex.xyz", index);
            headers["accept"] = "*/*";
            headers["content-type"] = "application/json";
            headers["sec-fetch-site"] = "same-origin";
            headers["Referrer-Policy"] = "strict-origin-when-cross-origin";

            const apiData = {
                id: launchpadResult.data.token,
                curve: launchpadResult.data.curve,
                name: name,
                symbol: symbol,
                url: imageUrl,
                description: faker.company.catchPhrase(),
                website: "",
                twitter: "",
                telegram: "",
                discord: "",
                youtube: "",
                initBuyDescription: []
            };

            const config = {
                headers: headers,
                timeout: 30000,
                ...(this.proxyString && { httpsAgent: new HttpsProxyAgent(this.proxyString) })
            };

            const response = await axios.post(
                'https://testnet.somnex.xyz/api/Launchpadv2/50312/launchpad/create',
                apiData,
                config
            );

            if (response.data && response.data.code === 200) {
                return {
                    success: true,
                    code: 'TOKEN_CREATED',
                    msg: `代币创建完成 ｜ ${name} (${symbol})`,
                    data: {
                        name: name,
                        symbol: symbol,
                        token: launchpadResult.data.token,
                        curve: launchpadResult.data.curve,
                        imageUrl: imageUrl,
                        transactionHash: launchpadResult.data.transactionHash,
                        blockNumber: launchpadResult.data.blockNumber,
                        gasUsed: launchpadResult.data.gasUsed
                    }
                };
            } else {
                return {
                    success: false,
                    code: 'API_UPDATE_FAILED',
                    msg: '代币信息更新失败',
                    data: {
                        apiResponse: response.data,
                        token: launchpadResult.data.token,
                        curve: launchpadResult.data.curve
                    }
                };
            }

        } catch (error) {
            console.error('代币创建流程错误:', error);
            return {
                success: false,
                code: 'LAUNCH_TOKEN_ERROR',
                msg: '代币创建流程失败: ' + (error.message || '未知错误'),
                data: {
                    error: error.message || 'Unknown error'
                }
            };
        }
    }
}

export default Somnex;
