import { Wallet, ethers } from "ethers";
import {
  filterWalletsByIndex,
  getRandomValue,
  parseTOMLConfig,
  sleep,
} from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import Somnex from "./somnex.js";
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const PROXY_URL = config.PROXY_URL;

const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

const rpc = 'https://dream-rpc.somnia.network';

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  // const indexArg = "2";

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
  let successCount = 0;
  let failedCount = 0;


  let failedCountIDS = [];


  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const provider = new ethers.JsonRpcProvider(rpc);
    const _wallet = new Wallet(private_key, provider);
    const somnex = new Somnex(_wallet, PROXY_URL);


    // 执行meme交易
    logger.info(`index:${index} 执行Lanuch Token`);
    const filepath = "/Users/<USER>/Downloads/image.jpg"
    const result = await somnex.launchToken(filepath, index)
    if (result.success && result.code === 'TOKEN_CREATED') {
        logger.success(`index:${index} Token创建成功, name: ${result.data.name}, address: ${result.data.token}`);
        successCount++;
        sleep(getRandomValue(5, 10));
    } else {
        logger.error(`index:${index} Token创建失败, msg: ${result.msg}`);
        failedCount++;
        failedCountIDS.push(index);
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印统计信息
  logger.info("------------------------");
  logger.info(`总计处理钱包: ${shuffledWallets.length}`);
  logger.info(`交易成功: ${successCount}`);
  logger.info(`交易失败IDS: ${failedCountIDS.join(',')}`);


}

// 执行任务
// node example/monad/aprio/task.js 1-10
main();
