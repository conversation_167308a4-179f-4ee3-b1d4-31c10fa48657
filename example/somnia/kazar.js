import { ethers } from "ethers";


class Kazar {
  constructor(index, wallet, proxyString) {
    this.index = index;
    this.wallet = wallet;
    this.proxyString = proxyString;

    this.SNK_CONTRACT = '******************************************'; // SNK contract
    this.LIFE_CONTRACT = '******************************************'; // LIFE contract
    this.GHIBLI_CONTRACT = '******************************************'; // GHIBLI contract
  }


  async #getTokenBalance(tokenAddress) {
    try {
      const erc20Abi = [
        "function balanceOf(address account) view returns (uint256)"
      ];

      const usdtContract = new ethers.Contract(tokenAddress, erc20Abi, this.wallet.provider);
      const balance = await usdtContract.balanceOf(this.wallet.address);

      return balance;
    } catch (error) {
      console.error(`Error getting ${tokenAddress} balance:`, error);
      return BigInt(0);
    }
  }


  async getLIFEBalance() {
    try {
      const balance = await this.#getTokenBalance(this.LIFE_CONTRACT);
      return balance;
    } catch (error) {
      console.error('Error getting LIFE balance:', error);
      return BigInt(0);
    }
  }

  async getCBBalance() {
    try {
      const balance = await this.#getTokenBalance(this.GHIBLI_CONTRACT);
      return balance;
    } catch (error) {
      console.error('Error getting CBBalance:', error);
      return BigInt(0);
    }
  }

  async getSNKBalance() {
    try {
      const balance = await this.#getTokenBalance(this.SNK_CONTRACT);
      return balance;
    } catch (error) {
      console.error('Error getting SNK balance:', error);
      return BigInt(0);
    }
  }


  async mintLIFE() {
    try {
      const lifeBalance = await this.getLIFEBalance();
      console.log('LIFE余额:', ethers.formatEther(lifeBalance), 'LIFE');
      if (ethers.formatEther(lifeBalance) >= 10) {
        return {
          success: true,
          msg: `LIFE余额超过10 ｜ 当前: ${ethers.formatEther(lifeBalance)} LIFE`,
          error: 'LIFE balance exceeds 10'
        };
      }

      let tempData = "0x84bb1e42********************0000e25fda0a2c4921479afe40d412d6fe4e2e2b1845****************************************000000008ac7230489e8********************00000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee************************************************************************************************************************000000c************************************************************0016************************************************************0008********************************************************************************************************************************************************************************************************************************************************************************************************************************0"

      const userAddressHex = this.wallet.address.slice(2).toLowerCase();
      tempData = tempData.replace("e25fda0a2c4921479afe40d412d6fe4e2e2b1845", userAddressHex);
      console.log('tempData:', tempData);

      const sttBalance = await this.wallet.provider.getBalance(this.wallet.address);
      const feeData = await this.wallet.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(**********);
      const gasLimit = BigInt(7760000);
      const gasEstimate = gasLimit * gasPrice;
      const totalSTTRequired = gasEstimate;
      console.log('STT余额:', ethers.formatEther(sttBalance));
      if (sttBalance < totalSTTRequired) {
        return {
          success: false,
          msg: `STT余额不足 ｜ 当前: ${ethers.formatEther(sttBalance)} STT, 需要: ${ethers.formatEther(totalSTTRequired)} STT`,
          error: 'Insufficient STT balance'
        };
      }

      const tx = {
        to: this.LIFE_CONTRACT,
        data: tempData,
        value: 0,
        gasLimit: gasLimit,
        gasPrice: gasPrice,
        chainId: 50312
      };

      let txResponse = await this.wallet.sendTransaction(tx);
      const receipt = await txResponse.wait();
      if (receipt.status === 1) {
        return {
          success: true,
          msg: `LIFE交易完成 ｜ 哈希：${txResponse.hash}`,
          transactionHash: txResponse.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          amountIn: 10,
          tokenIn: 'LIFE'
        };
      }

      return {
        success: false,
        msg: `LIFE交易失败 ｜ 哈希：${txResponse.hash}`,
        transactionHash: txResponse.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      return {
        success: false,
        msg: `LIFE交易失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }


  async mintSNK(snkAmount = 66) {
    try {
      const balance = await this.getSNKBalance();
      console.log('SNK余额:', ethers.formatEther(balance), 'SNK, 本次mint:', snkAmount, 'SNK');
      if (ethers.formatEther(balance) > 3500) {
        return {
          success: true,
          msg: `SNK余额超过3500 ｜ 当前: ${ethers.formatEther(balance)} SNK`,
          error: 'SNK balance exceeds 3500'
        };
      }

      let tempData = "0x84bb1e42********************0000eeeacb6e5a3028a5b61e28bd8a112acb50d8e975****************************************000000004563918244f4********************00000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee************************************************************************************************************************000000c************************************************************0016************************************************************0008********************************************************************************************************************************************************************************************************************************************************************************************************************************0"

      const userAddressHex = this.wallet.address.slice(2).toLowerCase();
      tempData = tempData.replace("eeeacb6e5a3028a5b61e28bd8a112acb50d8e975", userAddressHex);

      const amountInWei = ethers.parseEther(snkAmount.toString());
      let hexValue = amountInWei.toString(16);
      const amountHex = hexValue.padStart(64, '0');

      let inputData = tempData.replace("****************************************000000004563918244f40000", amountHex);

      const sttBalance = await this.wallet.provider.getBalance(this.wallet.address);
      const feeData = await this.wallet.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(**********);
      const gasLimit = BigInt(7760000);
      const gasEstimate = gasLimit * gasPrice;
      const totalSTTRequired = gasEstimate;
      console.log('STT余额:', ethers.formatEther(sttBalance));
      if (sttBalance < totalSTTRequired) {
        return {
          success: false,
          msg: `STT余额不足 ｜ 当前: ${ethers.formatEther(sttBalance)} STT, 需要: ${ethers.formatEther(totalSTTRequired)} STT`,
          error: 'Insufficient STT balance'
        };
      }

      const tx = {
        to: this.SNK_CONTRACT,
        data: inputData,
        value: 0,
        gasLimit: gasLimit,
        gasPrice: gasPrice,
        chainId: 50312
      };

      let txResponse = await this.wallet.sendTransaction(tx);
      const receipt = await txResponse.wait();

      if (receipt.status === 1) {
        return {
          success: true,
          msg: `SNK交易完成 ｜ ${snkAmount} SNK ｜ 哈希：${txResponse.hash}`,
          transactionHash: txResponse.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          amountIn: snkAmount,
          tokenIn: 'SNK'
        };
      }

      return {
        success: false,
        msg: `SNK交易失败 ｜ 哈希：${txResponse.hash}`,
        transactionHash: txResponse.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      return {
        success: false,
        msg: `SNK交易失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }


  async mintCB() {
    try {
      const cbBalance = await this.getCBBalance();
      console.log('CB余额:', ethers.formatEther(cbBalance), 'CB');
      if (ethers.formatEther(cbBalance) >= 3000) {
        return {
          success: true,
          msg: `CB余额超过3000 ｜ 当前: ${ethers.formatEther(cbBalance)} CB`,
          error: 'CB balance exceeds 3000'
        };
      }

      let tempData = "0x84bb1e42********************0000e25fda0a2c4921479afe40d412d6fe4e2e2b1845****************************************0000002086ac3510526********************000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee************************************************************************************************************************000000c************************************************************0016************************************************************0008********************************************************************************************************************************************************************************************************************************************************************************************************************************0"

      const userAddressHex = this.wallet.address.slice(2).toLowerCase();
      tempData = tempData.replace("e25fda0a2c4921479afe40d412d6fe4e2e2b1845", userAddressHex);

      const sttBalance = await this.wallet.provider.getBalance(this.wallet.address);
      const feeData = await this.wallet.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(**********);
      const gasLimit = BigInt(7760000);
      const gasEstimate = gasLimit * gasPrice;
      const totalSTTRequired = gasEstimate;
      console.log('STT余额:', ethers.formatEther(sttBalance));
      if (sttBalance < totalSTTRequired) {
        return {
          success: false,
          msg: `STT余额不足 ｜ 当前: ${ethers.formatEther(sttBalance)} STT, 需要: ${ethers.formatEther(totalSTTRequired)} STT`,
          error: 'Insufficient STT balance'
        };
      }

      const tx = {
        to: this.GHIBLI_CONTRACT,
        data: tempData,
        value: 0,
        gasLimit: gasLimit,
        gasPrice: gasPrice,
        chainId: 50312
      };

      let txResponse = await this.wallet.sendTransaction(tx);
      const receipt = await txResponse.wait();
      if (receipt.status === 1) {
        return {
          success: true,
          msg: `CB交易完成 ｜ 哈希：${txResponse.hash}`,
          transactionHash: txResponse.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          amountIn: 3000,
          tokenIn: 'CB'
        };
      }

      return {
        success: false,
        msg: `CB交易失败 ｜ 哈希：${txResponse.hash}`,
        transactionHash: txResponse.hash,
        error: 'Transaction failed'
      };

    } catch (error) {
      return {
        success: false,
        msg: `CB交易失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }

}

export default Kazar;
