import { ethers, formatEther, parseEther, formatUnits } from 'ethers';
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import { parseTOMLConfig } from "../../base/tools/common.js";
import { Wallet } from "../../base/evm/wallet.js";
import {
  getRandomValue,
  sleep,
} from "../../base/tools/common.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const MAX_RETRIES = 3;

// 合约地址
const QUICKSWAP_ROUTER_ADDRESS = "******************************************";

const USDC_CONTRACT_ADDRESS = "******************************************";

const LP_MANAGER_ADDRESS = "******************************************";

// USDC合约ABI - 用于检查余额
const USDC_ABI = [
  {
    "inputs": [{ "internalType": "address", "name": "account", "type": "address" }],
    "name": "balanceOf",
    "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }],
    "stateMutability": "view",
    "type": "function"
  }
];

const rpc = "https://dream-rpc.somnia.network"

/**
 * 带重试的操作执行
 */
async function retryOperation(operation, maxRetries = MAX_RETRIES) {
  let lastError;
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt > 0) {
        await randomDelay();
      }
      return await operation();
    } catch (error) {
      lastError = error;
      if (attempt < maxRetries) {
        continue;
      }
      throw error;
    }
  }
  throw lastError;
}

/**
 * 生成随机交换金额
 */
function generateRandomSwapAmount() {
  const min = 0.001;
  const max = 0.003;
  return (Math.random() * (max - min) + min).toFixed(3);
}

/**
 * QuickSwap
 */
class QuickSwap {
  /**
   * 创建 QuickSwap 实例
   * @param {number} index - 账户索引
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpcUrl - RPC 节点地址
   */
  constructor(index = 0, privateKey = "", proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.index = index;
    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.logger = logger;
    this.config = config;
    this.privateKey = privateKey;
    this.address = this.wallet.getAddress();

  }

  // 执行交换
  async executeSwap() {
    try {
      logger.info(`[${this.index}] 开始执行STT到USDC交换`);

      const sttBalance = await this.balanceOf(this.address);
      const usdcBalance = await this.balanceOfUSDC(this.address);
      if (sttBalance === 0n) {
        this.logger.warn(`[${this.index}] STT余额为0，跳过交换`);
        return { success: false, message: "STT余额为0" };
      }

      this.logger.info(`[${this.index}] STT余额: ${formatEther(sttBalance)}`);
      this.logger.info(`[${this.index}] USDC余额: ${formatUnits(usdcBalance, 6)}`);

      // if (usdcBalance > 0) {
      //   this.logger.info(`[${this.index}] USDC余额大于0，跳过交换`);
      //   return { success: true, message: "USDC余额大于0" };
      // }

      // 生成随机交换金额
      const randomAmount = generateRandomSwapAmount();
      const amountIn = parseEther(randomAmount);

      // 获取gas参数
      const gasParams = await this.#getGasParams();

      // 设置交易截止时间 (当前时间 + 20分钟)
      const deadline = Math.floor(Date.now() / 1000) + 1200;

      // 使用您提供的原始交易数据作为模板，但替换金额、接收地址和截止时间
      const recipientPadded = this.wallet.wallet.address.slice(2).toLowerCase().padStart(64, '0');
      const deadlinePadded = deadline.toString(16).padStart(64, '0');
      const amountInHex = amountIn.toString(16).padStart(64, '0');

      // 构造完整的交易数据 - 使用您提供的参数结构
      const data = "0x1679c792" + // exactInputSingle函数选择器
        "0000000000000000000000004a3bc48c156384f9564fd65a53a2f3d534d8f2b7" + // tokenIn (WSTT)
        "000000000000000000000000e9cc37904875b459fa5d0fe37680d36f1ed55e38" + // tokenOut (USDC)
        "0000000000000000000000000000000000000000000000000000000000000000" + // fee (0)
        recipientPadded + // recipient (钱包地址)
        deadlinePadded + // deadline
        amountInHex + // amountIn
        "0000000000000000000000000000000000000000000000000000000000000000" + // amountOutMinimum (0)
        "0000000000000000000000000000000000000000000000000000000000000000"; // sqrtPriceLimitX96 (0)

      this.logger.info(
        `[${this.index}] 使用原始数据交换 ${randomAmount} STT 到 USDC，发送value: ${randomAmount} STT`);

      // 动态估算gas限制
      let estimatedGas;
      try {
        estimatedGas = await this.provider.estimateGas({
          to: QUICKSWAP_ROUTER_ADDRESS,
          data: data,
          value: amountIn,
          from: this.wallet.address
        });

        estimatedGas = (estimatedGas * 15n) / 10n;
        this.logger.info(
          `[${this.index}] 估算的gas限制: ${estimatedGas.toString()}`);
      } catch (error) {
        this.logger.warn(
          `[${this.index}] 无法估算gas，使用默认值: ${error.message}`);
        estimatedGas = BigInt(500000);
      }

      // 执行交换 - 发送STT作为value
      const tx = await this.wallet.sendTransaction({
        to: QUICKSWAP_ROUTER_ADDRESS,
        data: data,
        value: amountIn, // 发送STT作为原生代币
        maxFeePerGas: gasParams.maxFeePerGas,
        maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas,
        gasLimit: estimatedGas
      });

      this.logger.info(
        `[${this.index}] 交换交易已发送，交易哈希: ${tx.hash}`);

      const receipt = await tx.wait();
      if (receipt.status === 0) {
        throw new Error('交易执行失败，智能合约返回错误');
      }

      const usdcBalanceAfter = await this.balanceOfUSDC(this.address);
      const usdcBalanceAfterFormatted = formatUnits(usdcBalanceAfter, 6);

      this.logger.success(`[${this.index}] 交换成功，区块号: ${receipt.blockNumber}, 获得USDC: ${usdcBalanceAfterFormatted}`);

      return {
        success: true,
        message: 'swap success',
        txHash: tx.hash,
        amountIn: randomAmount,
        amountOut: usdcBalanceAfterFormatted
      };
    } catch (error) {
      this.logger.error(
        `[${this.index}] 交换失败: ${error.message}`);

      if (error.transaction) {
        this.logger.error(
          `[${this.index}] 交易详情: ${error.transaction.hash}`);
      }

      return { success: false, message: error.message };
    }
  }

  // 执行任务
  async executeLP() {
    try {
      this.logger.info(`[${this.index}] 开始处理钱包`);

      // 检测lp是否存在
      const lpExist = await this.#checkLP();
      if (lpExist) {
        this.logger.info(`[${this.index}] LP已存在，跳过添加流动性操作`);
        return {
          success: true,
          message: "LP已存在"
        };
      }
      // 检查USDC余额
      const usdcBalance = await this.balanceOfUSDC(this.address);
      if (usdcBalance === 0n) {
        this.logger.warn(`[${this.index}] 【${this.address}】 USDC余额为0，跳过添加流动性操作`);
        return {
          success: false,
          message: "USDC余额为0"
        };
      }

      const checkUSDCAllowanceResult = await this.approveUSDC(usdcBalance);
      if (!checkUSDCAllowanceResult.success) {
        return {
          success: false,
          message: checkUSDCAllowanceResult.message
        };
      }


      // 添加流动性
      const lpResult = await retryOperation(async () => {
        return await this.addLiquidity();
      });

      if (!lpResult.success) {
        return {
          success: false,
          wallet: this.wallet,
          reason: lpResult.reason
        };
      }

      return {
        success: true,
        wallet: this.wallet,
        txHash: lpResult.txHash,
        usdcAmount: lpResult.usdcAmount,
        sttAmount: lpResult.sttAmount
      };
    } catch (error) {
      this.logger.error({ index: this.wallet.index, wallet: this.wallet.address },
        `处理失败: ${error.message}`);
      return {
        success: false,
        wallet: this.wallet,
        error: error.message
      };
    }
  }

  async #checkLP() {
    const nftPositionManager = new ethers.Contract(
      LP_MANAGER_ADDRESS,
      [
        "function balanceOf(address owner) view returns (uint256)",
        "function tokenOfOwnerByIndex(address owner, uint256 index) view returns (uint256)"
      ],
      this.provider
    );

    const balance = await nftPositionManager.balanceOf(this.address);

    if (balance > 0n) {
      this.logger.info(`[${this.index}] 找到 ${balance} 个 LP 仓位`);
      return true;
    }

    return false;
  }


  /**
   * 授权代币
   * @param {*} amountWei 授权数量
   * @returns
   */
  async approveUSDC(amountWei) {
    try {
      const tokenContract = new ethers.Contract(
        USDC_CONTRACT_ADDRESS,
        [
          "function approve(address spender, uint256 amount) public returns (bool)",
          "function allowance(address owner, address spender) public view returns (uint256)"
        ],
        this.wallet.wallet
      );

      // 检查当前授权额度
      const currentAllowance = await tokenContract.allowance(
        this.wallet.wallet.address,
        LP_MANAGER_ADDRESS
      );

      if (currentAllowance >= amountWei) {
        return {
          success: true,
          code: 0,
          message: `[${this.index}] USDC授权成功`,
        };
      }

      // 准备授权交易
      const nonce = await this.provider.getTransactionCount(this.address);
      const gasParams = await retryOperation(async () => {
        return await this.#getGasParams();
      });

      const tx = await tokenContract.approve(
        LP_MANAGER_ADDRESS,
        amountWei,
        {
          nonce,
          type: 2,
          ...gasParams,
        }
      );

      try {
        const receipt = await tx.wait();
        await sleep(getRandomValue(3, 10));
        if (receipt.status === 1) {
          this.logger.success(`[${this.index}] USDC 授权成功! Hash: ${tx.hash}`);
          return {
            success: true,
            data: {
              txHash: tx.hash,
            },
            code: "SUCCESS",
            message: `[${this.index}] USDC 授权成功!`,
          };
        } else {
          return {
            success: false,
            message: `[${this.index}] USDC 授权失败`,
            code: "APPROVE_FAILED",
          };
        }
      } catch (error) {
        this.logger.error(`授权 USDC 失败: ${error.message}`);
        return {
          success: false,
          message: `[${this.index}] USDC 授权失败`,
          code: "APPROVE_FAILED",
        };
      }

    } catch (error) {
      this.logger.error(`授权 USDC 失败: ${error.message}`);
      return {
        success: false,
        message: `[${this.index}] USDC 授权失败`,
        code: "APPROVE_FAILED",
      };
    }
  }

  // 添加流动性
  async addLiquidity() {
    try {
      this.logger.info(`[${this.index}] 开始添加流动性`);

      // 检查USDC余额
      const usdcBalance = await this.balanceOfUSDC(this.address);
      if (usdcBalance === 0n) {
        throw new Error('USDC余额为0，无法添加流动性');
      }

      const formattedUSDCAmount = formatUnits(usdcBalance, 6);
      const sttBalance = await this.balanceOf(this.address);
      const sttAmount = parseEther("0.001");
      const formattedSTTAmount = formatEther(sttAmount);

      // 检查STT余额是否足够
      if (sttBalance < sttAmount) {
        throw new Error(`STT余额不足，需要 ${formattedSTTAmount} STT，当前余额 ${formatEther(sttBalance)} STT`);
      }

      this.logger.info(`[${this.index}] 准备添加流动性: ${formattedUSDCAmount} USDC + ${formattedSTTAmount} STT`);

      const gasParams = await this.#getGasParams();

      // 构造参数
      const deadline = Math.floor(Date.now() / 1000) + 1200;
      const recipientPadded = this.address.slice(2).toLowerCase().padStart(64, '0');
      const deadlinePadded = deadline.toString(16).padStart(64, '0');
      const usdcAmountHex = usdcBalance.toString(16).padStart(64, '0');
      const sttAmountHex = sttAmount.toString(16).padStart(64, '0');

      // 第一部分：mint函数调用数据
      const mintCallData = "fe3f3be7" + // mint function selector (不带0x)
        "0000000000000000000000004a3bc48c156384f9564fd65a53a2f3d534d8f2b7" + // token0 (WSTT)
        "000000000000000000000000e9cc37904875b459fa5d0fe37680d36f1ed55e38" + // token1 (USDC)
        "0000000000000000000000000000000000000000000000000000000000000000" + // fee
        "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff2764c" + // tickLower
        "00000000000000000000000000000000000000000000000000000000000d89b4" + // tickUpper
        sttAmountHex + // amount0Desired (STT数量)
        usdcAmountHex + // amount1Desired (USDC数量)
        "0000000000000000000000000000000000000000000000000000000000000000" + // amount0Min
        "0000000000000000000000000000000000000000000000000000000000000000" + // amount1Min
        recipientPadded + // recipient
        deadlinePadded; // deadline

      // 第二部分：refundETH函数调用数据
      const refundCallData = "41865270"; // refundETH function selector (不带0x)

      // 计算偏移量
      const mintCallDataLength = mintCallData.length / 2; // 字节长度
      const refundCallDataLength = refundCallData.length / 2; // 字节长度

      // 构造正确的multicall数据
      const multicallData = "0xac9650d8" + // multicall function selector
        "0000000000000000000000000000000000000000000000000000000000000020" + // offset to bytes[] array
        "0000000000000000000000000000000000000000000000000000000000000002" + // array length (2 elements)
        "0000000000000000000000000000000000000000000000000000000000000040" + // offset to first bytes element
        // 计算第二个元素的偏移量：0x40 + 0x20 + mintCallDataLength (rounded to 32 bytes)
        (0x40 + 0x20 + Math.ceil(mintCallDataLength / 32) * 32).toString(16).padStart(64, '0') + // offset to second bytes element
        // 第一个bytes元素
        mintCallDataLength.toString(16).padStart(64, '0') + // length of mint call data
        mintCallData + // mint call data
        // 填充到32字节边界
        "0".repeat((32 - (mintCallDataLength % 32)) % 32 * 2) +
        // 第二个bytes元素
        refundCallDataLength.toString(16).padStart(64, '0') + // length of refund call data
        refundCallData + // refund call data
        // 填充到32字节边界
        "0".repeat((32 - (refundCallDataLength % 32)) % 32 * 2);

      this.logger.info(
        `[${this.index}] 构造的multicall数据长度: ${multicallData.length}, mint长度: ${mintCallDataLength}, refund长度: ${refundCallDataLength}`);

      // 执行添加流动性
      const signer = new ethers.Wallet(this.privateKey, this.provider);
      const tx = await signer.sendTransaction({
        to: LP_MANAGER_ADDRESS,
        data: multicallData,
        value: sttAmount, // 发送STT作为原生代币
        maxFeePerGas: gasParams.maxFeePerGas,
        maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
      });

      this.logger.info(
        `[${this.index}] 添加流动性交易已发送，交易哈希: ${tx.hash}`);

      const receipt = await tx.wait();
      if (receipt.status === 0) {
        throw new Error('添加流动性交易失败');
      }

      this.logger.success(
        `[${this.index}] 添加流动性成功，区块号: ${receipt.blockNumber}`);

      return {
        success: true,
        txHash: tx.hash,
        usdcAmount: formattedUSDCAmount,
        sttAmount: formattedSTTAmount
      };
    } catch (error) {
      this.logger.error(
        `[${this.index}] 添加流动性失败: ${error.message}`);

      if (error.transaction) {
        this.logger.error(
          `[${this.index}] 交易详情: ${error.transaction.hash}`);
      }

      return { success: false, reason: error.message };
    }
  }

  /**
   * 获取当前网络的 gas 参数
   */
  async #getGasParams() {
    try {
      const latestBlock = await this.wallet.provider.getBlock("latest");
      const baseFee = latestBlock.baseFeePerGas;
      const maxPriorityFee = await this.wallet.provider.send("eth_maxPriorityFeePerGas", []);
      const maxPriorityFeePerGas = BigInt(maxPriorityFee);
      const maxFeePerGas = baseFee + maxPriorityFeePerGas;

      return {
        maxFeePerGas,
        maxPriorityFeePerGas,
      };
    } catch (error) {
      this.logger.error(`[${this.index}] 获取 gas 参数失败: ${error.message}`);
      throw error;
    }
  }


  async balanceOf(address) {
    return await this.provider.getBalance(address);
  }

  async balanceOfUSDC(address) {
    const usdcContract = new ethers.Contract(USDC_CONTRACT_ADDRESS, USDC_ABI, this.provider);
    return await usdcContract.balanceOf(address);
  }

}

export default QuickSwap;
