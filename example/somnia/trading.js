import { ethers } from "ethers";
import logger from "../../base/tools/logger.js";
import { randomSleep, getRandomValue } from "../../base/tools/common.js";

class SomniaTrading {
  constructor(index, wallet, proxy) {
    this.index = index;
    this.wallet = wallet;
    this.proxy = proxy;
    this.address = this.wallet.getAddress();
    
    // 合约地址
    this.TRADING_CONTRACT = '******************************************';
    
    // 代币地址
    this.TOKENS = {
      STT: '******************************************',
      USDC: '******************************************',
      SOL: '******************************************',
      BTC: '******************************************'
    };
    
    // 固定交易参数
    this.TRADE_PARAMS = {
      isMaker: true,
      n: 20,
      slippageLimit: 10000000
    };
    
    // 交易对定义
    this.TRADING_PAIRS = [
      { pair: 'STT/USDC', base: 'STT', quote: 'USDC', operations: ['buy', 'sell'] },
      { pair: 'SOL/USDC', base: 'SOL', quote: 'USDC', operations: ['buy', 'sell'] },
      { pair: 'BTC/USDC', base: 'BTC', quote: 'USDC', operations: ['buy', 'sell'] }
    ];
    
    // 合约ABI
    this.TRADING_ABI = [
      "function marketSellETH(address quote, bool isMaker, uint32 n, address recipient, uint32 slippageLimit) external payable",
      "function marketBuy(address base, address quote, uint256 quoteAmount, bool isMaker, uint32 n, address recipient, uint32 slippageLimit) external",
      "function marketSell(address base, address quote, uint256 baseAmount, bool isMaker, uint32 n, address recipient, uint32 slippageLimit) external"
    ];
    
    // ERC20 ABI
    this.ERC20_ABI = [
      "function balanceOf(address owner) view returns (uint256)",
      "function approve(address spender, uint256 amount) returns (bool)",
      "function allowance(address owner, address spender) view returns (uint256)",
      "function decimals() view returns (uint8)"
    ];
    
    // 创建合约实例
    this.tradingContract = new ethers.Contract(this.TRADING_CONTRACT, this.TRADING_ABI, this.wallet);
  }

  // 获取代币合约实例
  #getTokenContract(tokenSymbol) {
    const tokenAddress = this.TOKENS[tokenSymbol];
    return new ethers.Contract(tokenAddress, this.ERC20_ABI, this.wallet);
  }

  // 获取代币余额
  async #getTokenBalance(tokenSymbol) {
    try {
      const tokenContract = this.#getTokenContract(tokenSymbol);
      const balance = await tokenContract.balanceOf(this.address);
      const decimals = await tokenContract.decimals();
      const formattedBalance = ethers.formatUnits(balance, decimals);
      
      logger.info(`钱包 [${this.index}] ${tokenSymbol} 余额: ${formattedBalance}`);
      return {
        raw: balance,
        formatted: parseFloat(formattedBalance),
        decimals: decimals
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 获取 ${tokenSymbol} 余额失败: ${error.message}`);
      return { raw: 0n, formatted: 0, decimals: 18 };
    }
  }

  // 检查并approve代币
  async #ensureApproval(tokenSymbol, amount) {
    try {
      const tokenContract = this.#getTokenContract(tokenSymbol);
      const currentAllowance = await tokenContract.allowance(this.address, this.TRADING_CONTRACT);
      
      if (currentAllowance < amount) {
        logger.info(`钱包 [${this.index}] 需要approve ${tokenSymbol}, 数量: ${ethers.formatUnits(amount, 18)}`);
        
        const approveTx = await tokenContract.approve(this.TRADING_CONTRACT, amount);
        await approveTx.wait();
        
        logger.success(`钱包 [${this.index}] ${tokenSymbol} approve成功`);
        return true;
      }
      
      return true;
    } catch (error) {
      logger.error(`钱包 [${this.index}] ${tokenSymbol} approve失败: ${error.message}`);
      return false;
    }
  }

  // 生成随机交易序列
  #generateRandomTrades() {
    const maxRetries = 10;
    let retries = 0;
    
    while (retries < maxRetries) {
      const trades = [];
      const usedPairs = new Set();
      
      // 随机生成10次交易
      for (let i = 0; i < 10; i++) {
        const randomPair = this.TRADING_PAIRS[Math.floor(Math.random() * this.TRADING_PAIRS.length)];
        const randomOperation = randomPair.operations[Math.floor(Math.random() * randomPair.operations.length)];
        
        trades.push({
          pair: randomPair.pair,
          base: randomPair.base,
          quote: randomPair.quote,
          operation: randomOperation
        });
        
        usedPairs.add(randomPair.pair);
      }
      
      // 检查是否满足至少2个不同交易对的条件
      if (usedPairs.size >= 2) {
        logger.info(`钱包 [${this.index}] 生成随机交易序列成功，涉及交易对: ${Array.from(usedPairs).join(', ')}`);
        return trades;
      }
      
      retries++;
      logger.warn(`钱包 [${this.index}] 交易序列不满足条件，重新生成 (${retries}/${maxRetries})`);
    }
    
    // 如果随机生成失败，使用保底方案
    logger.warn(`钱包 [${this.index}] 随机生成失败，使用保底方案`);
    return this.#getFallbackTrades();
  }

  // 保底交易方案
  #getFallbackTrades() {
    return [
      { pair: 'STT/USDC', base: 'STT', quote: 'USDC', operation: 'sell' },
      { pair: 'SOL/USDC', base: 'SOL', quote: 'USDC', operation: 'buy' },
      { pair: 'BTC/USDC', base: 'BTC', quote: 'USDC', operation: 'buy' },
      { pair: 'STT/USDC', base: 'STT', quote: 'USDC', operation: 'buy' },
      { pair: 'SOL/USDC', base: 'SOL', quote: 'USDC', operation: 'sell' },
      { pair: 'BTC/USDC', base: 'BTC', quote: 'USDC', operation: 'sell' },
      { pair: 'STT/USDC', base: 'STT', quote: 'USDC', operation: 'sell' },
      { pair: 'SOL/USDC', base: 'SOL', quote: 'USDC', operation: 'buy' },
      { pair: 'BTC/USDC', base: 'BTC', quote: 'USDC', operation: 'buy' },
      { pair: 'STT/USDC', base: 'STT', quote: 'USDC', operation: 'buy' }
    ];
  }

  // 确保有USDC可用
  async #ensureUSDCAvailable() {
    try {
      const usdcBalance = await this.#getTokenBalance('USDC');
      
      if (usdcBalance.formatted < 1) {
        logger.info(`钱包 [${this.index}] USDC余额不足，需要先卖出STT获得USDC`);
        
        // 随机卖出0.05-0.1 STT
        const sellAmount = getRandomValue(0.05, 0.1);
        const sellAmountFormatted = parseFloat(sellAmount.toFixed(2));
        
        const success = await this.#sellSTTForUSDC(sellAmountFormatted);
        if (!success) {
          return false;
        }
        
        // 等待一下再检查余额
        await randomSleep(2, 3);
      }
      
      return true;
    } catch (error) {
      logger.error(`钱包 [${this.index}] 确保USDC可用失败: ${error.message}`);
      return false;
    }
  }

  // 卖出STT获得USDC
  async #sellSTTForUSDC(amount) {
    try {
      logger.info(`钱包 [${this.index}] 卖出 ${amount} STT 获得 USDC`);

      const sttBalance = await this.#getTokenBalance('STT');
      if (sttBalance.formatted < amount) {
        logger.error(`钱包 [${this.index}] STT余额不足: ${sttBalance.formatted} < ${amount}`);
        return false;
      }

      // 使用marketSellETH方法卖出STT
      const tx = await this.tradingContract.marketSellETH(
        this.TOKENS.USDC,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit,
        {
          value: ethers.parseEther(amount.toString())
        }
      );

      const receipt = await tx.wait();
      logger.success(`钱包 [${this.index}] 卖出STT成功，交易哈希: ${receipt.hash}`);
      return true;

    } catch (error) {
      logger.error(`钱包 [${this.index}] 卖出STT失败: ${error.message}`);
      return false;
    }
  }

  // 执行买入操作
  async #executeBuy(base, quote, usdcAmount) {
    try {
      logger.info(`钱包 [${this.index}] 用 ${usdcAmount} USDC 买入 ${base}`);

      // 检查USDC余额
      const usdcBalance = await this.#getTokenBalance('USDC');
      if (usdcBalance.formatted < usdcAmount) {
        logger.warn(`钱包 [${this.index}] USDC余额不足: ${usdcBalance.formatted} < ${usdcAmount}`);
        return false;
      }

      // 转换金额为wei
      const quoteAmountWei = ethers.parseUnits(usdcAmount.toString(), 6); // USDC是6位小数

      // approve USDC
      const approveSuccess = await this.#ensureApproval('USDC', quoteAmountWei);
      if (!approveSuccess) {
        return false;
      }

      // 执行买入交易
      const tx = await this.tradingContract.marketBuy(
        this.TOKENS[base],
        this.TOKENS[quote],
        quoteAmountWei,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit
      );

      const receipt = await tx.wait();
      logger.success(`钱包 [${this.index}] 买入 ${base} 成功，交易哈希: ${receipt.hash}`);
      return true;

    } catch (error) {
      logger.error(`钱包 [${this.index}] 买入 ${base} 失败: ${error.message}`);
      return false;
    }
  }

  // 执行卖出操作
  async #executeSell(base, quote, sellRatio) {
    try {
      logger.info(`钱包 [${this.index}] 卖出 ${(sellRatio * 100).toFixed(1)}% 的 ${base} 获得 ${quote}`);

      // 检查代币余额
      const baseBalance = await this.#getTokenBalance(base);
      if (baseBalance.formatted <= 0) {
        logger.warn(`钱包 [${this.index}] ${base} 余额为0，跳过卖出`);
        return false;
      }

      // 计算卖出数量
      const sellAmount = baseBalance.raw * BigInt(Math.floor(sellRatio * 1000)) / 1000n;
      const sellAmountFormatted = ethers.formatUnits(sellAmount, baseBalance.decimals);

      logger.info(`钱包 [${this.index}] 准备卖出 ${sellAmountFormatted} ${base}`);

      // 特殊处理STT卖出
      if (base === 'STT') {
        return await this.#sellSTTForUSDC(parseFloat(sellAmountFormatted));
      }

      // approve代币
      const approveSuccess = await this.#ensureApproval(base, sellAmount);
      if (!approveSuccess) {
        return false;
      }

      // 执行卖出交易
      const tx = await this.tradingContract.marketSell(
        this.TOKENS[base],
        this.TOKENS[quote],
        sellAmount,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit
      );

      const receipt = await tx.wait();
      logger.success(`钱包 [${this.index}] 卖出 ${base} 成功，交易哈希: ${receipt.hash}`);
      return true;

    } catch (error) {
      logger.error(`钱包 [${this.index}] 卖出 ${base} 失败: ${error.message}`);
      return false;
    }
  }

  // 执行单个交易
  async #executeTrade(trade, tradeIndex) {
    try {
      logger.info(`钱包 [${this.index}] 执行第 ${tradeIndex + 1} 个交易: ${trade.operation} ${trade.pair}`);

      let success = false;

      if (trade.operation === 'buy') {
        // 随机买入金额 5-20 USDC
        const buyAmount = getRandomValue(5, 20);
        const buyAmountFormatted = parseFloat(buyAmount.toFixed(2));
        success = await this.#executeBuy(trade.base, trade.quote, buyAmountFormatted);
      } else {
        // 随机卖出比例 10%-80%
        const sellRatio = getRandomValue(0.1, 0.8);
        success = await this.#executeSell(trade.base, trade.quote, sellRatio);
      }

      if (success) {
        logger.success(`钱包 [${this.index}] 第 ${tradeIndex + 1} 个交易执行成功`);
      } else {
        logger.warn(`钱包 [${this.index}] 第 ${tradeIndex + 1} 个交易执行失败，继续下一个`);
      }

      return success;

    } catch (error) {
      logger.error(`钱包 [${this.index}] 执行交易失败: ${error.message}`);
      return false;
    }
  }

  // 执行完整的交易流程
  async executeRandomTrades() {
    try {
      logger.info(`钱包 [${this.index}] ${this.address} 开始执行随机交易流程`);

      // 1. 确保有USDC可用
      const usdcReady = await this.#ensureUSDCAvailable();
      if (!usdcReady) {
        return {
          success: false,
          msg: 'USDC准备失败',
          error: 'Failed to prepare USDC'
        };
      }

      // 2. 生成随机交易序列
      const trades = this.#generateRandomTrades();

      logger.info(`钱包 [${this.index}] 交易序列:`);
      trades.forEach((trade, index) => {
        logger.info(`  ${index + 1}. ${trade.operation} ${trade.pair}`);
      });

      // 3. 执行交易
      let successCount = 0;
      let failedCount = 0;

      for (let i = 0; i < trades.length; i++) {
        const trade = trades[i];

        const success = await this.#executeTrade(trade, i);
        if (success) {
          successCount++;
        } else {
          failedCount++;
        }

        // 交易间延迟
        if (i < trades.length - 1) {
          const delay = getRandomValue(1, 3);
          logger.info(`钱包 [${this.index}] 等待 ${delay.toFixed(1)} 秒后执行下一个交易...`);
          await randomSleep(delay, delay + 0.5);
        }
      }

      logger.success(`钱包 [${this.index}] 随机交易流程完成`);
      logger.info(`钱包 [${this.index}] 交易统计: 成功 ${successCount}, 失败 ${failedCount}`);

      return {
        success: true,
        msg: `随机交易完成 | 成功: ${successCount}, 失败: ${failedCount}`,
        data: {
          totalTrades: trades.length,
          successCount: successCount,
          failedCount: failedCount,
          trades: trades
        }
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] 随机交易流程执行失败: ${error.message}`);
      return {
        success: false,
        msg: `随机交易流程执行失败: ${error.message}`,
        error: error.message
      };
    }
  }
}

export default SomniaTrading;
