import { ethers } from "ethers";
import { Wallet } from "../../base/evm/wallet.js";
import StandardWeb3 from "./standardweb3.js";
import logger from "../../base/tools/logger.js";

// Somnia 测试网配置
const RPC_URL = "https://dream-rpc.somnia.network";

/**
 * 测试动态请求头生成功能
 */
async function testDynamicHeaders() {
  try {
    logger.info("=== 测试动态请求头生成 ===");
    
    // 示例私钥（请替换为你的实际私钥）
    const privateKey = "0x你的私钥";
    
    // 1. 创建钱包实例
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const address = wallet.getAddress();
    logger.info(`钱包地址: ${address}`);
    
    // 2. 创建多个StandardWeb3实例，测试每次生成的参数是否不同
    const instances = [];
    for (let i = 1; i <= 3; i++) {
      logger.info(`\n--- 创建实例 ${i} ---`);
      const standardWeb3 = new StandardWeb3(i, wallet, null);
      instances.push(standardWeb3);
      
      // 显示生成的动态参数
      logger.info(`实例 ${i} 动态参数:`);
      logger.info(`  SDK Request ID: ${standardWeb3.SDK_REQUEST_ID}`);
      logger.info(`  Device Fingerprint: ${standardWeb3.DYNAMIC_HEADERS['x-dyn-device-fingerprint']}`);
      logger.info(`  Session Public Key: ${standardWeb3.DYNAMIC_HEADERS['x-dyn-session-public-key']}`);
      logger.info(`  Dynamic Request ID: ${standardWeb3.DYNAMIC_HEADERS['x-dyn-request-id']}`);
    }
    
    // 3. 验证参数唯一性
    logger.info("\n=== 参数唯一性验证 ===");
    
    const sdkRequestIds = instances.map(inst => inst.SDK_REQUEST_ID);
    const deviceFingerprints = instances.map(inst => inst.DYNAMIC_HEADERS['x-dyn-device-fingerprint']);
    const sessionPublicKeys = instances.map(inst => inst.DYNAMIC_HEADERS['x-dyn-session-public-key']);
    const dynamicRequestIds = instances.map(inst => inst.DYNAMIC_HEADERS['x-dyn-request-id']);
    
    logger.info(`SDK Request IDs 唯一性: ${new Set(sdkRequestIds).size}/${sdkRequestIds.length}`);
    logger.info(`Device Fingerprints 唯一性: ${new Set(deviceFingerprints).size}/${deviceFingerprints.length}`);
    logger.info(`Session Public Keys 唯一性: ${new Set(sessionPublicKeys).size}/${sessionPublicKeys.length}`);
    logger.info(`Dynamic Request IDs 唯一性: ${new Set(dynamicRequestIds).size}/${dynamicRequestIds.length}`);
    
    // 4. 验证参数格式
    logger.info("\n=== 参数格式验证 ===");
    
    const instance = instances[0];
    const headers = instance.DYNAMIC_HEADERS;
    
    // UUID格式验证
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const isValidUUID = uuidRegex.test(instance.SDK_REQUEST_ID);
    logger.info(`SDK Request ID 格式: ${isValidUUID ? '✅ 有效UUID' : '❌ 无效UUID'}`);
    
    // 设备指纹格式验证（32位十六进制）
    const fingerprintRegex = /^[a-f0-9]{32}$/i;
    const isValidFingerprint = fingerprintRegex.test(headers['x-dyn-device-fingerprint']);
    logger.info(`Device Fingerprint 格式: ${isValidFingerprint ? '✅ 有效MD5' : '❌ 无效MD5'}`);
    
    // 会话公钥格式验证（66位十六进制，以03或02开头）
    const publicKeyRegex = /^0[23][a-f0-9]{64}$/i;
    const isValidPublicKey = publicKeyRegex.test(headers['x-dyn-session-public-key']);
    logger.info(`Session Public Key 格式: ${isValidPublicKey ? '✅ 有效压缩公钥' : '❌ 无效公钥'}`);
    
    // 动态请求ID格式验证（base64url格式）
    const base64urlRegex = /^[A-Za-z0-9_-]+$/;
    const isValidDynamicId = base64urlRegex.test(headers['x-dyn-request-id']);
    logger.info(`Dynamic Request ID 格式: ${isValidDynamicId ? '✅ 有效base64url' : '❌ 无效格式'}`);
    
    return {
      success: true,
      instances: instances.length,
      uniqueParams: {
        sdkRequestIds: new Set(sdkRequestIds).size,
        deviceFingerprints: new Set(deviceFingerprints).size,
        sessionPublicKeys: new Set(sessionPublicKeys).size,
        dynamicRequestIds: new Set(dynamicRequestIds).size
      },
      formatValidation: {
        uuidValid: isValidUUID,
        fingerprintValid: isValidFingerprint,
        publicKeyValid: isValidPublicKey,
        dynamicIdValid: isValidDynamicId
      }
    };
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试使用动态参数的nonce API请求
 */
async function testDynamicNonceAPI() {
  try {
    logger.info("=== 测试使用动态参数的nonce API请求 ===");
    
    const privateKey = "0x你的私钥";
    
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const standardWeb3 = new StandardWeb3(1, wallet, null);
    
    logger.info("使用的动态参数:");
    logger.info(`  SDK Request ID: ${standardWeb3.SDK_REQUEST_ID}`);
    logger.info(`  Device Fingerprint: ${standardWeb3.DYNAMIC_HEADERS['x-dyn-device-fingerprint']}`);
    logger.info(`  Session Public Key: ${standardWeb3.DYNAMIC_HEADERS['x-dyn-session-public-key']}`);
    logger.info(`  Dynamic Request ID: ${standardWeb3.DYNAMIC_HEADERS['x-dyn-request-id']}`);
    
    // 执行签名（会使用动态生成的参数请求nonce）
    const result = await standardWeb3.signIn();
    
    if (result.success) {
      logger.success("使用动态参数的签名成功！");
      logger.info(`Nonce来源: ${result.data.nonceSource}`);
      logger.info(`使用的nonce: ${result.data.messageData.nonce}`);
      
      // 验证签名
      const verifyResult = await standardWeb3.verifySignature(result.data);
      if (verifyResult.success && verifyResult.isValid) {
        logger.success("签名验证通过！");
      } else {
        logger.error("签名验证失败！");
      }
      
      return {
        success: true,
        nonceSource: result.data.nonceSource,
        nonce: result.data.messageData.nonce,
        signatureValid: verifyResult.isValid
      };
      
    } else {
      logger.error("签名失败:", result.msg);
      return {
        success: false,
        error: result.msg
      };
    }
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试同一钱包多次生成的参数差异
 */
async function testParameterConsistency() {
  try {
    logger.info("=== 测试同一钱包的参数一致性 ===");
    
    const privateKey = "0x你的私钥";
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    // 创建5个实例
    const results = [];
    for (let i = 1; i <= 5; i++) {
      const standardWeb3 = new StandardWeb3(i, wallet, null);
      results.push({
        index: i,
        sdkRequestId: standardWeb3.SDK_REQUEST_ID,
        deviceFingerprint: standardWeb3.DYNAMIC_HEADERS['x-dyn-device-fingerprint'],
        sessionPublicKey: standardWeb3.DYNAMIC_HEADERS['x-dyn-session-public-key'],
        dynamicRequestId: standardWeb3.DYNAMIC_HEADERS['x-dyn-request-id']
      });
    }
    
    // 分析结果
    logger.info("\n生成的参数:");
    results.forEach(result => {
      logger.info(`实例 ${result.index}:`);
      logger.info(`  SDK Request ID: ${result.sdkRequestId}`);
      logger.info(`  Device Fingerprint: ${result.deviceFingerprint}`);
      logger.info(`  Session Public Key: ${result.sessionPublicKey.substring(0, 20)}...`);
      logger.info(`  Dynamic Request ID: ${result.dynamicRequestId.substring(0, 20)}...`);
    });
    
    // 检查设备指纹的一致性（应该基于钱包地址有一定相似性）
    const fingerprints = results.map(r => r.deviceFingerprint);
    const uniqueFingerprints = new Set(fingerprints);
    
    logger.info(`\n设备指纹唯一性: ${uniqueFingerprints.size}/${fingerprints.length}`);
    
    return {
      success: true,
      totalInstances: results.length,
      uniqueFingerprints: uniqueFingerprints.size,
      results: results
    };
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 主程序入口
if (import.meta.url === `file://${process.argv[1]}`) {
  // 测试动态请求头生成
  testDynamicHeaders();
  
  // 测试使用动态参数的API请求（取消注释使用）
  // testDynamicNonceAPI();
  
  // 测试参数一致性（取消注释使用）
  // testParameterConsistency();
}
