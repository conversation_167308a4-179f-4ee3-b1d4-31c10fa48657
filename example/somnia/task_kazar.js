import { JsonRp<PERSON><PERSON><PERSON><PERSON>, Wallet } from "ethers";
import {
  filterWalletsByIndex,
  getRandomValue,
  parseTOMLConfig,
  sleep,
} from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import Kazar from "./kazar.js";
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const rpcUrl = "https://dream-rpc.somnia.network";
const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

const proxyString = config.PROXY_URL;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  // const indexArg = "0";

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
  let lifeSuccessCount = 0;
  let snkSuccessCount = 0;
  let cbSuccessCount = 0;
  let lifeFailedIDS = [];
  let snkFailedIDS = [];
  let cbFailedIDS = [];

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const provider = new JsonRpcProvider(rpcUrl); // 替换为实际的 Somnia RPC 端点
    const _wallet = new Wallet(private_key, provider);
    const swap = new Kazar(index, _wallet, proxyString);

    try {
      logger.info("index: " + index + " 开始mint LIFE");
      const lifeResult = await swap.mintLIFE();
      logger.info(`LIFE 结果: ${lifeResult.msg}`);
      if (lifeResult.success) {
        lifeSuccessCount++;
        if (!lifeResult.msg.includes("LIFE余额超过10")) {
          await sleep(getRandomValue(5, 10));
        }
      } else {
        lifeFailedIDS.push(index);
      }
    } catch (error) {
      lifeFailedIDS.push(index);
    }


    try {
      logger.info("index: " + index + " 开始mint SNK");
      const iterations = 30;
      const attempts = Array.from({ length: iterations }, (_, i) => i);
      for (const i of attempts) {
        const amount = getRandomValue(20, 200);
        const result = await swap.mintSNK(amount);
        logger.info(`SNK 结果: ${result.msg}`);
        if (result.success) {
          if (result.msg.includes("SNK余额超过3500")) {
            snkSuccessCount++;
            break;
          }
          const delay = getRandomValue(30, 90);
          await sleep(delay);
        }
      }
    } catch (error) {
      logger.error(`任务执行失败: ${error}`);
      snkFailedIDS.push(index);
    }

    try {
      logger.info("index: " + index + " 开始mint CB");
      const iterations = 10;
      const attempts = Array.from({ length: iterations }, (_, i) => i);
      for (const i of attempts) {
        const result = await swap.mintCB();
        logger.info(`CB 结果: ${result.msg}`);
        if (result.success) {
          if (result.msg.includes("CB余额超过3000")) {
            cbSuccessCount++;
            break;
          }
          const delay = getRandomValue(20, 40);
          await sleep(delay);
        }
      }
    } catch (error) {
      logger.error(`任务执行失败: ${error}`);
      cbFailedIDS.push(index);
    }


    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印统计信息
  logger.info("------------------------");
  logger.info(`总计处理钱包: ${shuffledWallets.length}`);
  logger.info(`mint LIFE 成功: ${lifeSuccessCount}`);
  logger.info(`mint SNK 成功: ${snkSuccessCount}`);
  logger.info(`mint CB 成功: ${cbSuccessCount}`);
  logger.info(`mint LIFE 失败: ${lifeFailedIDS.join(",")}`);
  logger.info(`mint SNK 失败: ${snkFailedIDS.join(",")}`);
  logger.info(`mint CB 失败: ${cbFailedIDS.join(",")}`);

}

// 执行任务
// node example/somnia/task_kazar.js 1-10
main();
