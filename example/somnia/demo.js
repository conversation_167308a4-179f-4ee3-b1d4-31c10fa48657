import { test, expect } from '@playwright/test';
import { Synpress } from '@synthetixio/synpress';

// MetaMask 配置（使用测试钱包）
const METAMASK_SEED_PHRASE = 'inherit prepare reunion kiwi job frown cannon foam diet mimic nation grass'; // 替换为测试钱包助记词
const METAMASK_PASSWORD = '123456'; // 替换为测试钱包密码
const WEBSITE_URL = 'https://testnet.standardweb3.com/somnia-testnet/trade'; // 替换为你的 Web3 网站 URL

// 初始化 Synpress
const synpress = new Synpress({
  browser: 'chromium', // 使用 Chromium 浏览器
  metamask: {
    seed: METAMASK_SEED_PHRASE,
    password: METAMASK_PASSWORD,
  },
});

test.describe('Web3 Website Wallet Connection and Signing with Built-in MetaMask', () => {
  let page;

  test.beforeAll(async () => {
    // 启动浏览器并配置内置 MetaMask
    const browser = await synpress.launch();
    page = await browser.newPage();
    await synpress.setupMetamask(page); // 使用内置 MetaMask，导入助记词
  });

  test.afterAll(async () => {
    // 关闭浏览器
    await synpress.close();
  });

  test('should connect MetaMask and sign a transaction', async () => {
    // 访问你的 Web3 网站
    await page.goto(WEBSITE_URL);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 点击“Login or Sign up”按钮
    // 使用你提供的按钮 class
    await page.click('button.w-full.py-4.bg-green-400.text-white.rounded-xl.mb-6');

    // 等待弹框出现并选择 MetaMask
    // 使用 MetaMask 选项的 data-testid
    await page.waitForSelector('[data-testid="ListTile"]', { state: 'visible' });
    await page.click('[data-testid="ListTile"]');

    // 等待 MetaMask 弹窗并确认连接
    await synpress.confirmMetamaskConnection(page);

    // 验证钱包已连接（检查页面显示的钱包地址）
    // 替换 '#wallet-address' 为显示钱包地址的元素 selector
    await page.waitForSelector('#wallet-address', { state: 'visible' });
    const walletAddress = await page.textContent('#wallet-address');
    expect(walletAddress).toMatch(/^0x[a-fA-F0-9]{40}$/); // 验证地址格式

    // 模拟触发签名操作
    // 替换 '#sign-message' 为你网站上触发签名的按钮 selector
    await page.click('#sign-message');

    // 等待 MetaMask 签名弹窗并确认
    await synpress.confirmMetamaskSignature(page);

    // 验证签名完成
    // 替换 '#signature-result' 为显示签名结果的元素 selector
    await page.waitForSelector('#signature-result', { state: 'visible' });
    const signatureResult = await page.textContent('#signature-result');
    expect(signatureResult).toBeTruthy(); // 验证签名结果存在
  });
});