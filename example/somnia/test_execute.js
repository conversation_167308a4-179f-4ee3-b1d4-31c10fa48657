import { ethers } from "ethers";
import { Wallet } from "../../base/evm/wallet.js";
import StandardWeb3 from "./standardweb3.js";
import logger from "../../base/tools/logger.js";
import CSV from "../../base/tools/csv.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import { 
  filterWalletsByIndex, 
  getRandomValue, 
  sleep 
} from "../../base/tools/common.js";
import { resolveFromModule } from "../../base/tools/path.js";

// Somnia 测试网配置
const RPC_URL = "https://dream-rpc.somnia.network";

/**
 * 测试StandardWeb3完整流程（登录+交易）
 */
async function testStandardWeb3Execute() {
  try {
    // 示例私钥（请替换为你的实际私钥）
    const privateKey = "0x你的私钥";
    const proxyUrl = null; // 如果需要代理，设置为 "******************************:port"
    
    logger.info("=== StandardWeb3 完整流程测试开始 ===");
    
    // 1. 创建钱包实例
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const address = wallet.getAddress();
    logger.info(`钱包地址: ${address}`);
    
    // 2. 创建StandardWeb3实例
    const standardWeb3 = new StandardWeb3(1, wallet, proxyUrl);
    
    // 3. 执行完整流程（登录验证 + 随机交易）
    const result = await standardWeb3.execute();
    
    if (result.success) {
      logger.success("完整流程执行成功！");
      logger.info("执行结果:", JSON.stringify(result, null, 2));
    } else {
      logger.error("完整流程执行失败:", result.msg);
    }
    
    return result;
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
    throw error;
  }
}

/**
 * 测试单独的签名功能
 */
async function testStandardWeb3Sign() {
  try {
    // 示例私钥（请替换为你的实际私钥）
    const privateKey = "0x你的私钥";
    const proxyUrl = null; // 如果需要代理，设置为 "******************************:port"
    
    logger.info("=== StandardWeb3 签名测试开始 ===");
    
    // 1. 创建钱包实例
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const address = wallet.getAddress();
    logger.info(`钱包地址: ${address}`);
    
    // 2. 创建StandardWeb3实例
    const standardWeb3 = new StandardWeb3(1, wallet, proxyUrl);
    
    // 3. 执行签名（会自动从API获取nonce）
    const signResult = await standardWeb3.sign();
    
    if (signResult.success) {
      logger.success("签名成功！");
      logger.info(`Nonce来源: ${signResult.data.nonceSource}`);
      logger.info("签名数据:", JSON.stringify(signResult.data, null, 2));
      
      // 4. 验证签名（可选）
      const verifyResult = await standardWeb3.verifySignature(signResult.data);
      if (verifyResult.success && verifyResult.isValid) {
        logger.success("签名验证通过！");
      } else {
        logger.error("签名验证失败！");
      }
      
    } else {
      logger.error("签名失败:", signResult.msg);
    }
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
  }
}

/**
 * 测试单独的交易功能
 */
async function testStandardWeb3Trade() {
  try {
    // 示例私钥（请替换为你的实际私钥）
    const privateKey = "0x你的私钥";
    const proxyUrl = null; // 如果需要代理，设置为 "******************************:port"
    
    logger.info("=== StandardWeb3 交易测试开始 ===");
    
    // 1. 创建钱包实例
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const address = wallet.getAddress();
    logger.info(`钱包地址: ${address}`);
    
    // 2. 创建StandardWeb3实例
    const standardWeb3 = new StandardWeb3(1, wallet, proxyUrl);
    
    // 3. 执行随机交易
    const tradeResult = await standardWeb3.trade();
    
    if (tradeResult.success) {
      logger.success("随机交易成功！");
      logger.info("交易结果:", JSON.stringify(tradeResult.data, null, 2));
    } else {
      logger.error("随机交易失败:", tradeResult.msg);
    }
    
    return tradeResult;
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
    throw error;
  }
}

/**
 * 批量钱包执行任务
 */
async function executeBatchTask() {
  try {
    logger.info("=== 批量钱包执行任务开始 ===");

    // 1. 读取钱包数据
    const csvPath = resolveFromModule(import.meta.url, "../../data/wallets.csv");
    const csv = new CSV(csvPath);
    const wallets = await csv.readCSV();

    if (!wallets || wallets.length === 0) {
      logger.error("未找到钱包数据，请检查 wallets.csv 文件");
      return;
    }

    logger.info(`读取到 ${wallets.length} 个钱包`);

    // 2. 过滤钱包（如果需要）
    const filteredWallets = filterWalletsByIndex(wallets, "1-3"); // 只处理前3个钱包
    logger.info(`过滤后处理 ${filteredWallets.length} 个钱包`);

    // 3. 打乱钱包顺序
    const shuffledWallets = filteredWallets.sort(() => Math.random() - 0.5);

    // 4. 统计变量
    let successCount = 0;
    let failedCount = 0;
    const failedWallets = [];
    const secureEncryption = new SecureEncryption();

    // 5. 逐个处理钱包
    for (let i = 0; i < shuffledWallets.length; i++) {
      const walletData = shuffledWallets[i];
      const { index, address, proxy } = walletData;
      let { private_key } = walletData;

      logger.info(`\n--- 处理钱包 ${i + 1}/${shuffledWallets.length}: [${index}] ${address} ---`);

      try {
        // 解密私钥（如果已加密）
        const isEncrypted = await secureEncryption.isEncrypted(private_key);
        if (isEncrypted) {
          private_key = await secureEncryption.decrypt(private_key);
        }

        if (!private_key || private_key.trim() === "") {
          logger.warn(`钱包 [${index}] 私钥为空，跳过处理`);
          failedCount++;
          failedWallets.push({ index, reason: "私钥为空" });
          continue;
        }

        // 创建钱包实例
        const wallet = new Wallet(private_key, RPC_URL);
        await wallet.connect();

        // 验证钱包地址
        const walletAddress = wallet.getAddress();
        if (walletAddress.toLowerCase() !== address.toLowerCase()) {
          logger.error(`钱包 [${index}] 地址不匹配: 期望 ${address}, 实际 ${walletAddress}`);
          failedCount++;
          failedWallets.push({ index, reason: "地址不匹配" });
          continue;
        }

        // 创建StandardWeb3实例并执行完整流程
        const standardWeb3 = new StandardWeb3(index, wallet, proxy);
        const result = await standardWeb3.execute();

        if (result.success) {
          logger.success(`钱包 [${index}] ${address} 完整流程执行成功`);
          successCount++;

          // 可以在这里保存执行结果
          await saveExecuteResult(index, address, result);

        } else {
          logger.error(`钱包 [${index}] ${address} 完整流程执行失败: ${result.msg}`);
          failedCount++;
          failedWallets.push({ 
            index, 
            address, 
            reason: result.msg,
            error: result.error 
          });
        }

      } catch (error) {
        logger.error(`钱包 [${index}] 处理过程中发生错误: ${error.message}`);
        failedCount++;
        failedWallets.push({ 
          index, 
          address, 
          reason: `处理错误: ${error.message}`,
          error: error.message 
        });
      }

      // 钱包间延迟（最后一个钱包不延迟）
      if (i < shuffledWallets.length - 1) {
        const delay = getRandomValue(5, 10);
        logger.info(`等待 ${delay.toFixed(1)} 秒后处理下一个钱包...`);
        await sleep(delay);
      }
    }

    // 6. 输出统计结果
    logger.info("\n=== 批量执行任务完成 ===");
    logger.info(`总处理钱包: ${shuffledWallets.length}`);
    logger.info(`执行成功: ${successCount}`);
    logger.info(`执行失败: ${failedCount}`);

    if (failedWallets.length > 0) {
      logger.info("\n失败钱包详情:");
      failedWallets.forEach(wallet => {
        logger.error(`  [${wallet.index}] ${wallet.address || 'N/A'}: ${wallet.reason}`);
      });
    }

  } catch (error) {
    logger.error("批量执行任务失败:", error.message);
    throw error;
  }
}

/**
 * 保存执行结果
 */
async function saveExecuteResult(index, address, result) {
  try {
    const resultData = {
      index,
      address,
      timestamp: new Date().toISOString(),
      success: result.success,
      loginData: result.loginData,
      tradeData: result.tradeData
    };

    logger.info(`钱包 [${index}] 执行结果已记录`);
    
    // 示例：保存到JSON文件
    // const fs = await import('fs/promises');
    // const resultPath = `./results/execute_${index}_${Date.now()}.json`;
    // await fs.writeFile(resultPath, JSON.stringify(resultData, null, 2));

  } catch (error) {
    logger.error(`保存钱包 [${index}] 执行结果失败: ${error.message}`);
  }
}

// 主程序入口
if (import.meta.url === `file://${process.argv[1]}`) {
  // 批量钱包执行任务
  executeBatchTask().catch(error => {
    logger.error("程序执行失败:", error.message);
    process.exit(1);
  });

  // 单个测试（取消注释使用）
  // testStandardWeb3Execute();
  // testStandardWeb3Sign();
  // testStandardWeb3Trade();
}
