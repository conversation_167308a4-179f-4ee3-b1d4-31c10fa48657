import { ethers } from "ethers";
import { Wallet } from "../../base/evm/wallet.js";
import StandardWeb3 from "./standardweb3.js";
import logger from "../../base/tools/logger.js";
import CSV from "../../base/tools/csv.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import { 
  filterWalletsByIndex, 
  getRandomValue, 
  sleep, 
  parseTOMLConfig 
} from "../../base/tools/common.js";
import { resolveFromModule } from "../../base/tools/path.js";
import { HttpsProxyAgent } from "https-proxy-agent";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "./standardweb3_config.toml");
const config = parseTOMLConfig(configPath);

const RPC_URL = config.network.rpc_url;
const MIN_DELAY = config.delays.min_delay_between_requests;
const MAX_DELAY = config.delays.max_delay_between_requests;

/**
 * StandardWeb3 登录任务执行器
 */
async function executeStandardWeb3LoginTask() {
  try {
    logger.info("=== StandardWeb3 登录任务开始 ===");

    // 1. 读取钱包数据
    const csvPath = resolveFromModule(import.meta.url, "../../data/wallets.csv");
    const csv = new CSV(csvPath);
    const wallets = await csv.readCSV();

    if (!wallets || wallets.length === 0) {
      logger.error("未找到钱包数据，请检查 wallets.csv 文件");
      return;
    }

    logger.info(`读取到 ${wallets.length} 个钱包`);

    // 2. 过滤钱包（如果需要）
    const filteredWallets = filterWalletsByIndex(wallets, "1-5"); // 只处理前5个钱包
    logger.info(`过滤后处理 ${filteredWallets.length} 个钱包`);

    // 3. 打乱钱包顺序
    const shuffledWallets = filteredWallets.sort(() => Math.random() - 0.5);

    // 4. 统计变量
    let successCount = 0;
    let failedCount = 0;
    const failedWallets = [];
    const secureEncryption = new SecureEncryption();

    // 5. 逐个处理钱包
    for (let i = 0; i < shuffledWallets.length; i++) {
      const walletData = shuffledWallets[i];
      const { index, address, proxy } = walletData;
      let { private_key } = walletData;

      logger.info(`\n--- 处理钱包 ${i + 1}/${shuffledWallets.length}: [${index}] ${address} ---`);

      try {
        // 解密私钥（如果已加密）
        const isEncrypted = await secureEncryption.isEncrypted(private_key);
        if (isEncrypted) {
          private_key = await secureEncryption.decrypt(private_key);
        }

        if (!private_key || private_key.trim() === "") {
          logger.warn(`钱包 [${index}] 私钥为空，跳过处理`);
          failedCount++;
          failedWallets.push({ index, reason: "私钥为空" });
          continue;
        }

        // 创建代理配置
        let httpsAgent = null;
        if (proxy && proxy.trim() !== "") {
          httpsAgent = new HttpsProxyAgent(proxy);
          logger.info(`钱包 [${index}] 使用代理: ${proxy.substring(0, 20)}...`);
        }

        // 创建钱包实例
        const wallet = new Wallet(private_key, RPC_URL);
        await wallet.connect();

        // 验证钱包地址
        const walletAddress = wallet.getAddress();
        if (walletAddress.toLowerCase() !== address.toLowerCase()) {
          logger.error(`钱包 [${index}] 地址不匹配: 期望 ${address}, 实际 ${walletAddress}`);
          failedCount++;
          failedWallets.push({ index, reason: "地址不匹配" });
          continue;
        }

        // 创建StandardWeb3实例并执行登录
        const standardWeb3 = new StandardWeb3(index, wallet, proxy);
        const loginResult = await standardWeb3.login(httpsAgent);

        if (loginResult.success) {
          logger.success(`钱包 [${index}] ${address} StandardWeb3登录成功`);
          successCount++;

          // 可以在这里保存登录结果或执行后续操作
          await saveLoginResult(index, address, loginResult);

        } else {
          logger.error(`钱包 [${index}] ${address} StandardWeb3登录失败: ${loginResult.msg}`);
          failedCount++;
          failedWallets.push({ 
            index, 
            address, 
            reason: loginResult.msg,
            error: loginResult.error 
          });
        }

      } catch (error) {
        logger.error(`钱包 [${index}] 处理过程中发生错误: ${error.message}`);
        failedCount++;
        failedWallets.push({ 
          index, 
          address, 
          reason: `处理错误: ${error.message}`,
          error: error.message 
        });
      }

      // 任务间延迟（最后一个任务不延迟）
      if (i < shuffledWallets.length - 1) {
        const delay = getRandomValue(MIN_DELAY, MAX_DELAY);
        logger.info(`等待 ${delay} 秒后处理下一个钱包...`);
        await sleep(delay);
      }
    }

    // 6. 输出统计结果
    logger.info("\n=== StandardWeb3 登录任务完成 ===");
    logger.info(`总处理钱包: ${shuffledWallets.length}`);
    logger.info(`登录成功: ${successCount}`);
    logger.info(`登录失败: ${failedCount}`);

    if (failedWallets.length > 0) {
      logger.info("\n失败钱包详情:");
      failedWallets.forEach(wallet => {
        logger.error(`  [${wallet.index}] ${wallet.address || 'N/A'}: ${wallet.reason}`);
      });
    }

    // 7. 保存失败记录
    if (failedWallets.length > 0) {
      await saveFailedWallets(failedWallets);
    }

  } catch (error) {
    logger.error("StandardWeb3登录任务执行失败:", error.message);
    throw error;
  }
}

/**
 * 保存登录结果
 */
async function saveLoginResult(index, address, loginResult) {
  try {
    const resultData = {
      index,
      address,
      timestamp: new Date().toISOString(),
      success: loginResult.success,
      nonce: loginResult.signData?.messageData?.nonce,
      requestId: loginResult.signData?.messageData?.requestId,
      signature: loginResult.signData?.signature?.substring(0, 20) + "...", // 只保存签名前缀
      serverResponse: loginResult.serverResponse
    };

    // 这里可以保存到文件或数据库
    logger.info(`钱包 [${index}] 登录结果已记录`);
    
    // 示例：保存到JSON文件
    // const fs = await import('fs/promises');
    // const resultPath = `./results/standardweb3_login_${index}_${Date.now()}.json`;
    // await fs.writeFile(resultPath, JSON.stringify(resultData, null, 2));

  } catch (error) {
    logger.error(`保存钱包 [${index}] 登录结果失败: ${error.message}`);
  }
}

/**
 * 保存失败钱包记录
 */
async function saveFailedWallets(failedWallets) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const failedData = {
      timestamp: new Date().toISOString(),
      totalFailed: failedWallets.length,
      wallets: failedWallets
    };

    logger.info(`保存 ${failedWallets.length} 个失败钱包记录`);
    
    // 示例：保存到JSON文件
    // const fs = await import('fs/promises');
    // const failedPath = `./results/standardweb3_failed_${timestamp}.json`;
    // await fs.writeFile(failedPath, JSON.stringify(failedData, null, 2));

  } catch (error) {
    logger.error("保存失败钱包记录时发生错误:", error.message);
  }
}

/**
 * 单个钱包登录测试
 */
async function testSingleWallet(privateKey, proxyUrl = null) {
  try {
    logger.info("=== 单个钱包登录测试 ===");

    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();

    const address = wallet.getAddress();
    logger.info(`测试钱包地址: ${address}`);

    let httpsAgent = null;
    if (proxyUrl) {
      httpsAgent = new HttpsProxyAgent(proxyUrl);
      logger.info(`使用代理: ${proxyUrl}`);
    }

    const standardWeb3 = new StandardWeb3(0, wallet, proxyUrl);
    const result = await standardWeb3.login(httpsAgent);

    if (result.success) {
      logger.success("单个钱包登录测试成功！");
      logger.info("登录数据:", JSON.stringify(result, null, 2));
    } else {
      logger.error("单个钱包登录测试失败:", result.msg);
    }

    return result;

  } catch (error) {
    logger.error("单个钱包登录测试过程中发生错误:", error.message);
    throw error;
  }
}

// 主程序入口
if (import.meta.url === `file://${process.argv[1]}`) {
  // 批量钱包登录任务
  executeStandardWeb3LoginTask().catch(error => {
    logger.error("程序执行失败:", error.message);
    process.exit(1);
  });

  // 单个钱包测试（取消注释使用）
  // const testPrivateKey = "0x你的测试私钥";
  // const testProxy = "******************************:port"; // 可选
  // testSingleWallet(testPrivateKey, testProxy);
}
