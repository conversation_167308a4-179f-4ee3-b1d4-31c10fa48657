import { JsonRp<PERSON><PERSON><PERSON><PERSON>, Wallet } from "ethers";
import {
  filterWalletsByIndex,
  getRandomValue,
  parseTOMLConfig,
  sleep,
} from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import Rarible from "./rarible.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const rpcUrl = "https://dream-rpc.somnia.network";
const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

const proxyString = config.PROXY_URL;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  // const indexArg = "0";

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  // 统计变量
  let executeSuccessCount = 0;
  let executeFailedIDS = [];
  let executeResults = [];
  let totalClaimedCount = 0;
  let totalSkippedCount = 0;

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const provider = new JsonRpcProvider(rpcUrl);
    const _wallet = new Wallet(private_key, provider);
    const rarible = new Rarible(index, _wallet, proxyString);

    // 执行 Rarible claim 流程
    try {
      const executeResult = await rarible.execute();

      if (executeResult.success) {
        logger.info(`Rarible claim 执行成功: ${executeResult.msg}`);
        executeSuccessCount++;

        // 统计 claim 和跳过的数量
        if (executeResult.alreadyOwned) {
          totalSkippedCount++;
        } else {
          totalClaimedCount++;
        }

        // 记录详细结果
        executeResults.push({
          index: index,
          address: address,
          success: true,
          action: executeResult.action || 'claim',
          alreadyOwned: executeResult.alreadyOwned || false,
          balance: executeResult.balance || '0',
          transactionHash: executeResult.transactionHash || null,
          cost: executeResult.cost || '0',
          gasUsed: executeResult.gasUsed || '0',
          quantity: executeResult.quantity || 0
        });

      } else {
        logger.error(`Rarible claim 执行失败: ${executeResult.msg}`);
        executeFailedIDS.push(index);

        // 记录失败结果
        executeResults.push({
          index: index,
          address: address,
          success: false,
          error: executeResult.msg,
          transactionHash: executeResult.transactionHash || null
        });
      }

    } catch (error) {
      logger.error(`执行 Rarible claim 时发生异常: ${error}`);
      executeFailedIDS.push(index);

      executeResults.push({
        index: index,
        address: address,
        success: false,
        error: error.message
      });
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印最终统计结果
  logger.info("========================");
  logger.info("Rarible Claim 执行结果统计:");
  logger.info(`总计钱包: ${shuffledWallets.length} | 成功: ${executeSuccessCount} | 失败: ${executeFailedIDS.length}`);
  
  if (totalClaimedCount > 0) {
    logger.info(`成功 Claim: ${totalClaimedCount} 个NFT`);
  }
  
  if (totalSkippedCount > 0) {
    logger.info(`跳过 (已拥有): ${totalSkippedCount} 个钱包`);
  }

  // 计算总花费
  const totalCost = executeResults
    .filter(r => r.success && !r.alreadyOwned)
    .reduce((sum, r) => sum + parseFloat(r.cost || 0), 0);

  if (totalCost > 0) {
    logger.info(`总花费: ${totalCost.toFixed(6)} STT`);
  }

  // 计算总 gas 使用量
  const totalGasUsed = executeResults
    .filter(r => r.success && r.gasUsed)
    .reduce((sum, r) => sum + parseInt(r.gasUsed || 0), 0);

  if (totalGasUsed > 0) {
    logger.info(`总 Gas 使用量: ${totalGasUsed.toLocaleString()}`);
  }

  if (executeFailedIDS.length > 0) {
    logger.info(`失败钱包序号: ${executeFailedIDS.join(",")}`);
  }
  logger.info("========================");
}

// 执行任务
// 使用方法: node example/somnia/task_rarible.js 1-10
main();
