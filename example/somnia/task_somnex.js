import { Wallet, ethers } from "ethers";
import {
  filterWalletsByIndex,
  getRandomValue,
  parseTOMLConfig,
  sleep,
} from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import Somnex from "./somnex.js";
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const PROXY_URL = config.PROXY_URL;

const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

const rpc = 'https://dream-rpc.somnia.network';

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  // const indexArg = "2";

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);
  let perpSwapSuccessCount = 0;
  let perpSwapFailedCount = 0;
  let usdtSwapFailedCount = 0;
  let usdtSwapSuccessCount = 0;
  let memeSwapSuccessCount = 0;
  let memeSwapFailedCount = 0;

  let perpSwapFailedCountIDS = [];
  let usdtSwapFailedCountIDS = [];
  let memeSwapFailedCountIDS = [];

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;
    const proxy = wallet.proxy;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const provider = new ethers.JsonRpcProvider(rpc);
    const _wallet = new Wallet(private_key, provider);
    const swap = new Somnex(_wallet, PROXY_URL);

    // 执行USDT.X交易
    const usdtBalance = await swap.getUSDTBalance()
    const formattedUSDCAmount = ethers.formatUnits(usdtBalance, 6);
    if (parseFloat(formattedUSDCAmount) < 20) {
      logger.info(`index:${index} USDT.X余额不足20，进行swap交易`);
      const result = await swap.executeSwap();
      logger.success(result.msg);
      if (!result.success) {
        usdtSwapFailedCount++;
        usdtSwapFailedCountIDS.push(index);
        continue;
      }
      usdtSwapSuccessCount++;
      sleep(getRandomValue(5, 10))
    }

    // // 执行PERP交易, 暂时校验不过，先注释
    // const approveResult = await swap.approve()
    // if (approveResult.success) {
    //   sleep(getRandomValue(2, 3))
    //   logger.info(`index:${index} 执行PERP交易`);
    //   const perpSwapResult = await swap.executePerpSwap();
    //   if (perpSwapResult.success) {
    //     perpSwapSuccessCount++;
    //     sleep(getRandomValue(5, 10))
    //   } else {
    //     perpSwapFailedCount++;
    //     perpSwapFailedCountIDS.push(index);
    //   }
    // } else {
    //   logger.info(`index:${index} USDT.X授权失败，跳过PERP交易`);
    //   usdtSwapFailedCount++;
    //   usdtSwapFailedCountIDS.push(index);
    //   continue;
    // }

    // 执行meme交易
    logger.info(`index:${index} 执行meme交易`);
    const memeSwapResult = await swap.swapMemeToken();
    if (memeSwapResult.success) {
      memeSwapSuccessCount++;
      sleep(getRandomValue(5, 10))
    } else {
      memeSwapFailedCount++;
      memeSwapFailedCountIDS.push(index);
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印统计信息
  logger.info("------------------------");
  logger.info(`总计处理钱包: ${shuffledWallets.length}`);
  logger.info(`PERP交易成功: ${perpSwapSuccessCount}`);
  logger.info(`PERP交易失败IDS: ${perpSwapFailedCountIDS.join(',')}`);
  logger.info(`USDT.X交易成功: ${usdtSwapSuccessCount}`);
  logger.info(`USDT.X交易失败IDS: ${usdtSwapFailedCountIDS.join(',')}`);
  logger.info(`meme交易成功: ${memeSwapSuccessCount}`);
  logger.info(`meme交易失败IDS: ${memeSwapFailedCountIDS.join(',')}`);

}

// 执行任务
// node example/monad/aprio/task.js 1-10
main();
