import { JsonRp<PERSON><PERSON><PERSON><PERSON>, Wallet } from "ethers";
import {
  filterWalletsByIndex,
  getRandomValue,
  parseTOMLConfig,
  sleep,
} from "../../base/tools/common.js";
import CSV from "../../base/tools/csv.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import Onirya from "./onirya.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const rpcUrl = "https://dream-rpc.somnia.network";
const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;

const proxyString = config.PROXY_URL;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./eth_wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0];
  // const indexArg = "0";

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  // 统计变量
  let executeSuccessCount = 0;
  let executeFailedIDS = [];
  let executeResults = [];

  const secureEncryption = new SecureEncryption();

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const index = wallet.index;
    const address = wallet.address;

    let private_key = wallet.private_key;
    const isEncrypted = await secureEncryption.isEncrypted(private_key);
    if (isEncrypted) {
      private_key = await secureEncryption.decrypt(private_key);
      wallet.private_key = private_key;
    }

    logger.info(
      `开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: 序号:${index} 钱包地址:${address}`,
    );

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    const provider = new JsonRpcProvider(rpcUrl);
    const _wallet = new Wallet(private_key, provider);
    const onirya = new Onirya(index, _wallet, proxyString);

    // 执行完整的游戏流程
    try {
      logger.info(`钱包 [${index}] 开始执行完整的 Onirya 游戏流程`);
      const executeResult = await onirya.execute();

      if (executeResult.success) {
        logger.info(`完整流程执行成功: ${executeResult.msg}`);
        logger.info(`总共完成 ${executeResult.totalTransactions} 个交易`);
        executeSuccessCount++;

        // 记录详细结果
        executeResults.push({
          index: index,
          address: address,
          success: true,
          totalSteps: executeResult.totalSteps,
          totalTransactions: executeResult.totalTransactions,
          results: executeResult.results
        });

      } else {
        logger.error(`完整流程执行失败: ${executeResult.msg}`);
        logger.error(`失败步骤: ${executeResult.failedStep || '未知'}`);
        executeFailedIDS.push(index);

        // 记录失败结果
        executeResults.push({
          index: index,
          address: address,
          success: false,
          failedStep: executeResult.failedStep,
          error: executeResult.msg,
          completedSteps: executeResult.results ? executeResult.results.length : 0
        });
      }

    } catch (error) {
      logger.error(`执行完整流程时发生异常: ${error}`);
      executeFailedIDS.push(index);

      executeResults.push({
        index: index,
        address: address,
        success: false,
        error: error.message,
        completedSteps: 0
      });
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(`任务完成，用时: ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }

  // 打印最终统计结果
  logger.info("========================");
  logger.info("Onirya 执行结果统计:");
  logger.info(`总计钱包: ${shuffledWallets.length} | 成功: ${executeSuccessCount} | 失败: ${executeFailedIDS.length}`);

  // 计算总交易数
  const totalTransactions = executeResults
    .filter(r => r.success)
    .reduce((sum, r) => sum + (r.totalTransactions || 0), 0);

  if (totalTransactions > 0) {
    logger.info(`总交易数: ${totalTransactions}`);
  }

  if (executeFailedIDS.length > 0) {
    logger.info(`失败钱包序号: ${executeFailedIDS.join(",")}`);
  }
  logger.info("========================");
}

// 执行任务
// 使用方法: node example/somnia/task_onirya.js 1-10
main();
