import { ethers, formatEther } from 'ethers';
import { Wallet } from "../../base/evm/wallet.js";
import { parseTOMLConfig } from "../../base/tools/common.js";
import { getRandomDomain } from "../../base/tools/domain.js";
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const DOMAIN_CONTRACT_ADDRESS = "******************************************";

const rpc = "https://dream-rpc.somnia.network"

const abi = [{
  inputs: [{
    internalType: "string",
    name: "name",
    type: "string"
  }],
  name: "claimName",
  outputs: [],
  stateMutability: "payable",
  type: "function"
}, {
  inputs: [{
    internalType: "string",
    name: "name",
    type: "string"
  }],
  name: "isAvailable",
  outputs: [{
    internalType: "bool",
    name: "",
    type: "bool"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "string",
    name: "name",
    type: "string"
  }],
  name: "resolveName",
  outputs: [{
    internalType: "address",
    name: "",
    type: "address"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "owner",
    type: "address"
  }],
  name: "reverseLookup",
  outputs: [{
    internalType: "string",
    name: "",
    type: "string"
  }],
  stateMutability: "view",
  type: "function"
}]

/**
 * Domain
 */
class Domain {
  /**
   * 创建 QuickSwap 实例
   * @param {number} index - 账户索引
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpcUrl - RPC 节点地址
   */
  constructor(index = 0, privateKey = "", proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.index = index;
    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.logger = logger;
    this.config = config;
    this.privateKey = privateKey;
    this.address = this.wallet.getAddress();
    this.domainContractAddress = DOMAIN_CONTRACT_ADDRESS;
    this.contract = new ethers.Contract(this.domainContractAddress, abi, this.wallet);

  }

  // 检查域名是否可用
  async isAvailable(domainName) {
    const isAvailable = await this.contract.isAvailable(domainName);
    return isAvailable;
  }


  generateRandomDomain() {
    return getRandomDomain();
  }

  encodeDomainToBytes32(domain) {
    const bytes = ethers.toUtf8Bytes(domain);
    const bytes32 = new Uint8Array(32);
    bytes32.set(bytes, 0);
    return ethers.hexlify(bytes32);
  }

  buildClaimNameCalldata(domain) {
    const methodSelector = '54c25e4b';
    const domainBytes = ethers.toUtf8Bytes(domain);
    const domainLength = domainBytes.length;
    const offset = '0000000000000000000000000000000000000000000000000000000000000020';
    const lengthHex = domainLength.toString(16).padStart(64, '0');
    const domainHex = ethers.hexlify(domainBytes).slice(2);
    const paddedDomainHex = domainHex.padEnd(64, '0');
    const calldata = '0x' + methodSelector + offset + lengthHex + paddedDomainHex;
    return calldata;
  }

  async getAvailableDomain(maxAttempts = 10) {
    try {
      // 尝试找到可用的域名
      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        // 生成随机域名
        let domainName = getRandomDomain();

        // 检查域名是否可用
        const isAvailable = await this.isAvailable(domainName);

        if (isAvailable) {
          this.logger.success(`[${this.index}] 找到可用域名: ${domainName}`);
          return {
            name: domainName,
            isAvailable: true,
            attempts: attempt + 1
          };
        }

        // 如果域名不可用，尝试添加随机后缀
        if (attempt < maxAttempts - 1) {
          const suffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          domainName = `${domainName}${suffix}`;

          const isAvailableWithSuffix = await this.isAvailable(domainName);
          if (isAvailableWithSuffix) {
            this.logger.success(`[${this.index}] 找到可用域名(带后缀): ${domainName}`);
            return {
              name: domainName,
              isAvailable: true,
              attempts: attempt + 1
            };
          }
        }

        this.logger.info(`[${this.index}] 尝试 ${attempt + 1}/${maxAttempts}: 域名 ${domainName} 已被注册`);
      }

      // 所有尝试都失败了
      this.logger.warn(`[${this.index}] 经过 ${maxAttempts} 次尝试仍未找到可用域名`);
      return {
        name: null,
        isAvailable: false,
        attempts: maxAttempts
      };

    } catch (error) {
      this.logger.error(`[${this.index}] 检查域名可用性时出错: ${error.message}`);
      return {
        name: null,
        isAvailable: false,
        error: error.message
      };
    }
  }


  async claimName() {
    try {
      // 检查余额
      const balance = await this.balanceOf(this.address);
      if (balance < ethers.parseEther("1")) {
        this.logger.warn(`[${this.index}] 余额不足1STT, 当前余额: ${formatEther(balance)}`);
        return {
          success: false,
          msg: '余额不足1STT, 当前余额: ' + formatEther(balance),
        };
      }

      // 获取可用域名
      const domainNameInfo = await this.getAvailableDomain();
      if (!domainNameInfo.isAvailable) {
        this.logger.warn(`[${this.index}] 无法找到可用域名，尝试次数: ${domainNameInfo.attempts}`);
        return {
          success: false,
          msg: '无法找到可用域名，尝试次数: ' + domainNameInfo.attempts,
        };
      }

      const domainName = domainNameInfo.name;
      this.logger.info(`[${this.index}] 开始注册域名: ${domainName}`);

      // // 动态估算 gasLimit
      // let gasLimit;
      // try {
      //   gasLimit = await this.contract.estimateGas.claimName(domainName, {
      //     value: ethers.parseEther("1"),
      //   });
      //   // 增加 20% 缓冲
      //   gasLimit = (gasLimit * 120n) / 100n;
      //   tx.gasLimit = gasLimit;
      // } catch (error) {
      //   this.logger.warn(`[${this.index}] Gas 估算失败: ${error.message}, 使用默认 gasLimit`);
      //   tx.gasLimit = 200000; // 备用 gasLimit，适当的值（如 20 万）
      // }

      // 构建交易数据
      const calldata = this.buildClaimNameCalldata(domainName);
      const tx = {
        to: this.domainContractAddress,
        data: calldata,
        value: ethers.parseEther("1"),
        // gasLimit: gasLimit
      };

      // 发送交易
      this.logger.info(`[${this.index}] 发送域名注册交易...`);
      const txResponse = await this.wallet.sendTransaction(tx);

      // 等待交易确认
      this.logger.info(`[${this.index}] 等待交易确认: ${txResponse.hash}`);
      const receipt = await txResponse.wait();

      if (receipt.status === 1) {
        this.logger.success(`[${this.index}] 域名注册成功: ${domainName}`);
        return {
          success: true,
          msg: '域名注册成功: ' + domainName,
          txHash: receipt.hash,
          domain: domainName,
          gasUsed: receipt.gasUsed?.toString(),
          attempts: domainNameInfo.attempts
        };
      } else {
        this.logger.error(`[${this.index}] 域名注册失败: 交易被revert`);
        return {
          success: false,
          msg: '域名注册失败: 交易被revert (域名: ' + domainName + ')',
          txHash: receipt.hash,
          domain: domainName,
          gasUsed: receipt.gasUsed?.toString()
        };
      }

    } catch (error) {
      this.logger.error(`[${this.index}] 域名注册失败: ${error.message}`);
      return {
        success: false,
        msg: '域名注册失败: ' + error.message,
        error: error.message
      };
    }
  }


  async balanceOf(address) {
    return await this.provider.getBalance(address);
  }

}

export default Domain;
