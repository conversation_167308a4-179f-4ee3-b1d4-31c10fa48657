import { Contract, formatEther, parseEther } from 'ethers';
import logger from '../../base/tools/logger.js';

export class SomniaExchange {
  constructor(index, wallet) {
    this.index = index;
    this.wallet = wallet; // 假设 wallet 已正确初始化为 ethers.Wallet 实例

    this.routerAddress = '******************************************';

    this.ROUTER_ABI = [
      'function swapExactETHForTokens(uint256 amountOutMin, address[] path, address to, uint256 deadline) public payable returns (uint256[])',
      'function swapExactTokensForETH(uint256 amountIn, uint256 amountOutMin, address[] path, address to, uint256 deadline) public returns (uint256[])',
      'function getAmountsOut(uint256 amountIn, address[] path) view returns (uint256[])',
    ];

    this.ERC20_ABI = [
      'function decimals() view returns (uint8)',
      'function balanceOf(address owner) view returns (uint256)',
      'function symbol() view returns (string)',
      'function approve(address spender, uint256 amount) returns (bool)',
      'function allowance(address owner, address spender) view returns (uint256)',
    ];
  }

  async executeSwapWithNonceRetry(txFn, returnTx = false, maxRetries = 3) {
    for (let retry = 0; retry < maxRetries; retry++) {
      try {
        const nonce = await this.wallet.provider.getTransactionCount(
          this.wallet.address,
          'pending'
        );
        const tx = await txFn(nonce);
        if (returnTx) return tx;
        const receipt = await tx.wait();
        if (receipt.status === 1) {
          return receipt;
        }
        throw new Error('交易被回滚');
      } catch (error) {
        if (
          error.message.includes('nonce too low') ||
          error.message.includes('nonce has already been used') ||
          error.message.includes('reverted')
        ) {
          logger.info(
            `交易失败 (尝试 ${retry + 1}): ${error.message}. 获取新的nonce...`
          );
          if (retry === maxRetries - 1) {
            throw new Error(`失败 ${maxRetries} 次尝试: ${error.message}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }
        throw error;
      }
    }
  }

  /**
   * 生成随机交换金额
   */
  generateRandomSwapAmount() {
    const min = 0.001;
    const max = 0.005;
    return (Math.random() * (max - min) + min).toFixed(3);
  }

  async swapNiaToken() {
    try {
      logger.info('开始STT到NIA代币兑换...');
      logger.info('使用Router地址:', this.routerAddress);

      const formattedAmount = this.generateRandomSwapAmount();
      const amountIn = parseEther(formattedAmount.toString());

      logger.info(`准备兑换金额: ${formattedAmount} STT`);

      const NIA_TOKEN_ADDRESS = '******************************************';
      const WETH_ADDRESS = '******************************************';

      const path = [WETH_ADDRESS, NIA_TOKEN_ADDRESS];
      logger.info(`交易路径: ${path.join(' -> ')}`);

      const routerContract = new Contract(
        this.routerAddress,
        this.ROUTER_ABI,
        this.wallet
      );

      let amountOutMin;
      try {
        logger.info('正在获取预期输出金额...');
        logger.info(`输入金额: ${formatEther(amountIn)} STT`);

        const amountsOut = await routerContract.getAmountsOut(amountIn, path);

        logger.info(`获取到的预期输出: ${formatEther(amountsOut[amountsOut.length - 1])} NIA`);

        amountOutMin = (amountsOut[amountsOut.length - 1] * 85n) / 100n;
        logger.info(`设置最小输出金额(85%): ${formatEther(amountOutMin)} NIA`);

        const expectedOutput = parseFloat(
          formatEther(amountsOut[amountsOut.length - 1])
        );
        if (expectedOutput < 5) {
          amountOutMin = (amountsOut[amountsOut.length - 1] * 70n) / 100n;
          logger.info(`预期输出小于5 NIA，调整最小输出金额(70%): ${formatEther(amountOutMin)} NIA`);
        }
      } catch (error) {
        logger.error('获取预期输出失败，详细错误:', error.message);
        if (error.code === 'CALL_EXCEPTION') {
          logger.error('合约调用异常，可能是合约地址或ABI有问题');
        } else if (error.code === 'NETWORK_ERROR') {
          logger.error('网络错误，请检查网络连接');
        }
        logger.info('使用保守的最小值: 1 NIA');
        amountOutMin = 1n;
      }

      const deadline = Math.floor(Date.now() / 1000) + 600;

      const sttBalance = await this.wallet.provider.getBalance(
        this.wallet.address
      );

      const feeData = await this.wallet.provider.getFeeData();
      const gasLimit = 300000n;
      // 支持 EIP-1559，优先使用 maxFeePerGas 和 maxPriorityFeePerGas
      const gasEstimate = feeData.maxFeePerGas
        ? gasLimit * feeData.maxFeePerGas
        : gasLimit * 6000000000n; // 6 Gwei 作为默认值
      const totalRequired = amountIn + gasEstimate;

      if (sttBalance < totalRequired) {
        return {
          success: false,
          msg: `STT余额不足 ｜ 当前: ${formatEther(
            sttBalance
          )} STT, 需要: ${formatEther(totalRequired)} STT`,
          error: 'Insufficient STT balance',
        };
      }

      try {
        const currentAmountsOut = await routerContract.getAmountsOut(
          amountIn,
          path
        );
        const currentExpected = currentAmountsOut[currentAmountsOut.length - 1];
        const originalExpected = (amountOutMin * 100n) / 85n;

        const priceChange = Number(
          ((currentExpected - originalExpected) * 100n) / originalExpected
        );
        if (priceChange < -10) {
          amountOutMin = (currentExpected * 70n) / 100n;
          logger.info(
            '价格大幅下跌，调整为30%滑点:',
            formatEther(amountOutMin),
            'NIA'
          );
        }
      } catch (error) {
        logger.error('无法进行实时价格检查:', error.message);
      }

      const receipt = await this.executeSwapWithNonceRetry(async nonce => {
        return await routerContract.swapExactETHForTokens(
          amountOutMin,
          path,
          this.wallet.address,
          deadline,
          {
            value: amountIn,
            gasLimit,
            maxFeePerGas: feeData.maxFeePerGas || 6000000000n,
            maxPriorityFeePerGas: feeData.maxPriorityFeePerGas || 1000000000n, // 1 Gwei
            nonce,
          }
        );
      });

      if (receipt.status === 1) {
        return {
          success: true,
          msg: `STT到NIA代币兑换成功，哈希 ${receipt.hash}`,
          error: null,
        };
      }
      return {
        success: false,
        msg: 'STT到NIA代币兑换失败',
        error: '交易被回滚',
      };
    } catch (error) {
      logger.error('STT到NIA代币兑换失败，详细错误:', error.message);
      return {
        success: false,
        msg: 'STT到NIA代币兑换失败',
        error: error.message,
      };
    }
  }
}
