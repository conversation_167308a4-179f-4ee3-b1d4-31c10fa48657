import axios from "axios";
import logger from "../../base/tools/logger.js";
import { HttpsProxyAgent } from "https-proxy-agent";

/**
 * 测试Dynamic Auth API的nonce获取功能
 */
async function testNonceAPI() {
  try {
    logger.info("=== 测试Dynamic Auth API nonce获取 ===");
    
    const REQUEST_ID = 'b5cf2c59-2ef6-4689-9a98-5b239d1149f4';
    const url = `https://app.dynamicauth.com/api/v0/sdk/${REQUEST_ID}/nonce`;
    
    const headers = {
      'accept': '*/*',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'origin': 'https://testnet.standardweb3.com',
      'referer': 'https://testnet.standardweb3.com/',
      'sec-ch-ua': '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'cross-site',
      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'x-dyn-api-version': 'API/0.0.728',
      'x-dyn-device-fingerprint': 'b6ba03e044269309879cf517ab8f9108',
      'x-dyn-is-global-wallet-popup': 'false',
      'x-dyn-request-id': 'cIxGiJ0rc2TduFjIWJ5jqbpQzWLbI-5ixUh7gQP_G6Df8iw7H6',
      'x-dyn-session-public-key': '03e451cb936b04dd9c37c363b9cfa1831b8c0981bd93d5dad26ced83cdc62d76e9',
      'x-dyn-version': 'WalletKit/4.25.5'
    };

    logger.info(`请求URL: ${url}`);
    logger.info("请求头信息:");
    Object.entries(headers).forEach(([key, value]) => {
      if (key.startsWith('x-dyn-')) {
        logger.info(`  ${key}: ${value}`);
      }
    });

    const config = {
      method: 'GET',
      url: url,
      headers: headers,
      timeout: 10000
    };

    const response = await axios(config);
    
    logger.success(`请求成功! 状态码: ${response.status}`);
    logger.info("响应数据:", response.data);
    
    if (response.data && response.data.nonce) {
      const nonce = response.data.nonce;
      logger.success(`获取到nonce: ${nonce}`);
      logger.info(`nonce长度: ${nonce.length} 字符`);
      
      // 验证nonce格式（应该是32位十六进制）
      const isValidHex = /^[a-f0-9]{32}$/i.test(nonce);
      logger.info(`nonce格式验证: ${isValidHex ? '✅ 有效' : '❌ 无效'}`);
      
      return {
        success: true,
        nonce: nonce,
        isValidFormat: isValidHex
      };
    } else {
      logger.error("响应数据中没有找到nonce字段");
      return {
        success: false,
        error: "No nonce in response"
      };
    }

  } catch (error) {
    logger.error("请求失败:", error.message);
    
    if (error.response) {
      logger.error(`HTTP状态码: ${error.response.status}`);
      logger.error("响应数据:", error.response.data);
      logger.error("响应头:", error.response.headers);
    } else if (error.request) {
      logger.error("网络请求失败，没有收到响应");
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试使用代理的nonce API请求
 */
async function testNonceAPIWithProxy(proxyUrl) {
  try {
    logger.info("=== 测试使用代理的nonce API请求 ===");
    logger.info(`代理地址: ${proxyUrl}`);
    
    const REQUEST_ID = 'b5cf2c59-2ef6-4689-9a98-5b239d1149f4';
    const url = `https://app.dynamicauth.com/api/v0/sdk/${REQUEST_ID}/nonce`;
    
    const headers = {
      'accept': '*/*',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'origin': 'https://testnet.standardweb3.com',
      'referer': 'https://testnet.standardweb3.com/',
      'sec-ch-ua': '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'cross-site',
      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'x-dyn-api-version': 'API/0.0.728',
      'x-dyn-device-fingerprint': 'b6ba03e044269309879cf517ab8f9108',
      'x-dyn-is-global-wallet-popup': 'false',
      'x-dyn-request-id': 'cIxGiJ0rc2TduFjIWJ5jqbpQzWLbI-5ixUh7gQP_G6Df8iw7H6',
      'x-dyn-session-public-key': '03e451cb936b04dd9c37c363b9cfa1831b8c0981bd93d5dad26ced83cdc62d76e9',
      'x-dyn-version': 'WalletKit/4.25.5'
    };

    const httpsAgent = new HttpsProxyAgent(proxyUrl);
    
    const config = {
      method: 'GET',
      url: url,
      headers: headers,
      httpsAgent: httpsAgent,
      proxy: false,
      timeout: 15000
    };

    const response = await axios(config);
    
    logger.success(`代理请求成功! 状态码: ${response.status}`);
    logger.info("响应数据:", response.data);
    
    if (response.data && response.data.nonce) {
      const nonce = response.data.nonce;
      logger.success(`通过代理获取到nonce: ${nonce}`);
      return {
        success: true,
        nonce: nonce
      };
    } else {
      logger.error("响应数据中没有找到nonce字段");
      return {
        success: false,
        error: "No nonce in response"
      };
    }

  } catch (error) {
    logger.error("代理请求失败:", error.message);
    
    if (error.response) {
      logger.error(`HTTP状态码: ${error.response.status}`);
      logger.error("响应数据:", error.response.data);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 多次测试nonce获取，验证每次返回的nonce是否不同
 */
async function testMultipleNonceRequests(count = 5) {
  logger.info(`=== 测试多次nonce请求 (${count}次) ===`);
  
  const nonces = [];
  
  for (let i = 1; i <= count; i++) {
    logger.info(`\n--- 第 ${i} 次请求 ---`);
    
    const result = await testNonceAPI();
    
    if (result.success) {
      nonces.push(result.nonce);
      logger.info(`第 ${i} 次nonce: ${result.nonce}`);
    } else {
      logger.error(`第 ${i} 次请求失败`);
    }
    
    // 请求间延迟
    if (i < count) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // 检查nonce唯一性
  const uniqueNonces = [...new Set(nonces)];
  logger.info(`\n=== nonce唯一性检查 ===`);
  logger.info(`总请求次数: ${count}`);
  logger.info(`成功获取: ${nonces.length}`);
  logger.info(`唯一nonce: ${uniqueNonces.length}`);
  
  if (nonces.length === uniqueNonces.length) {
    logger.success("✅ 所有nonce都是唯一的");
  } else {
    logger.warn("⚠️  发现重复的nonce");
    logger.info("所有nonce:", nonces);
  }
  
  return {
    totalRequests: count,
    successfulRequests: nonces.length,
    uniqueNonces: uniqueNonces.length,
    nonces: nonces
  };
}

// 主程序入口
if (import.meta.url === `file://${process.argv[1]}`) {
  // 单次测试
  testNonceAPI();
  
  // 多次测试（取消注释使用）
  // testMultipleNonceRequests(3);
  
  // 代理测试（取消注释并设置代理地址使用）
  // const proxyUrl = "******************************:port";
  // testNonceAPIWithProxy(proxyUrl);
}
