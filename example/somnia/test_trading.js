import { ethers } from "ethers";
import { Wallet } from "../../base/evm/wallet.js";
import SomniaTrading from "./trading.js";
import logger from "../../base/tools/logger.js";
import CSV from "../../base/tools/csv.js";
import SecureEncryption from "../../base/tools/secure-encryption.js";
import { 
  filterWalletsByIndex, 
  getRandomValue, 
  sleep, 
  parseTOMLConfig 
} from "../../base/tools/common.js";
import { resolveFromModule } from "../../base/tools/path.js";

// Somnia 测试网配置
const RPC_URL = "https://dream-rpc.somnia.network";

/**
 * 测试单个钱包的随机交易
 */
async function testSingleWalletTrading() {
  try {
    logger.info("=== 单个钱包随机交易测试 ===");
    
    // 示例私钥（请替换为你的实际私钥）
    const privateKey = "0x你的私钥";
    const proxyUrl = null; // 如果需要代理，设置为 "******************************:port"
    
    // 1. 创建钱包实例
    const wallet = new Wallet(privateKey, RPC_URL);
    await wallet.connect();
    
    const address = wallet.getAddress();
    logger.info(`测试钱包地址: ${address}`);
    
    // 2. 创建交易实例
    const trading = new SomniaTrading(1, wallet, proxyUrl);
    
    // 3. 执行随机交易
    const result = await trading.executeRandomTrades();
    
    if (result.success) {
      logger.success("随机交易测试成功！");
      logger.info("交易结果:", JSON.stringify(result.data, null, 2));
    } else {
      logger.error("随机交易测试失败:", result.msg);
    }
    
    return result;
    
  } catch (error) {
    logger.error("测试过程中发生错误:", error.message);
    throw error;
  }
}

/**
 * 批量钱包随机交易任务
 */
async function executeBatchTradingTask() {
  try {
    logger.info("=== 批量钱包随机交易任务开始 ===");

    // 1. 读取钱包数据
    const csvPath = resolveFromModule(import.meta.url, "../../data/wallets.csv");
    const csv = new CSV(csvPath);
    const wallets = await csv.readCSV();

    if (!wallets || wallets.length === 0) {
      logger.error("未找到钱包数据，请检查 wallets.csv 文件");
      return;
    }

    logger.info(`读取到 ${wallets.length} 个钱包`);

    // 2. 过滤钱包（如果需要）
    const filteredWallets = filterWalletsByIndex(wallets, "1-3"); // 只处理前3个钱包
    logger.info(`过滤后处理 ${filteredWallets.length} 个钱包`);

    // 3. 打乱钱包顺序
    const shuffledWallets = filteredWallets.sort(() => Math.random() - 0.5);

    // 4. 统计变量
    let successCount = 0;
    let failedCount = 0;
    const failedWallets = [];
    const secureEncryption = new SecureEncryption();

    // 5. 逐个处理钱包
    for (let i = 0; i < shuffledWallets.length; i++) {
      const walletData = shuffledWallets[i];
      const { index, address, proxy } = walletData;
      let { private_key } = walletData;

      logger.info(`\n--- 处理钱包 ${i + 1}/${shuffledWallets.length}: [${index}] ${address} ---`);

      try {
        // 解密私钥（如果已加密）
        const isEncrypted = await secureEncryption.isEncrypted(private_key);
        if (isEncrypted) {
          private_key = await secureEncryption.decrypt(private_key);
        }

        if (!private_key || private_key.trim() === "") {
          logger.warn(`钱包 [${index}] 私钥为空，跳过处理`);
          failedCount++;
          failedWallets.push({ index, reason: "私钥为空" });
          continue;
        }

        // 创建钱包实例
        const wallet = new Wallet(private_key, RPC_URL);
        await wallet.connect();

        // 验证钱包地址
        const walletAddress = wallet.getAddress();
        if (walletAddress.toLowerCase() !== address.toLowerCase()) {
          logger.error(`钱包 [${index}] 地址不匹配: 期望 ${address}, 实际 ${walletAddress}`);
          failedCount++;
          failedWallets.push({ index, reason: "地址不匹配" });
          continue;
        }

        // 创建交易实例并执行随机交易
        const trading = new SomniaTrading(index, wallet, proxy);
        const tradingResult = await trading.executeRandomTrades();

        if (tradingResult.success) {
          logger.success(`钱包 [${index}] ${address} 随机交易完成`);
          successCount++;

          // 可以在这里保存交易结果
          await saveTradingResult(index, address, tradingResult);

        } else {
          logger.error(`钱包 [${index}] ${address} 随机交易失败: ${tradingResult.msg}`);
          failedCount++;
          failedWallets.push({ 
            index, 
            address, 
            reason: tradingResult.msg,
            error: tradingResult.error 
          });
        }

      } catch (error) {
        logger.error(`钱包 [${index}] 处理过程中发生错误: ${error.message}`);
        failedCount++;
        failedWallets.push({ 
          index, 
          address, 
          reason: `处理错误: ${error.message}`,
          error: error.message 
        });
      }

      // 钱包间延迟（最后一个钱包不延迟）
      if (i < shuffledWallets.length - 1) {
        const delay = getRandomValue(3, 8);
        logger.info(`等待 ${delay.toFixed(1)} 秒后处理下一个钱包...`);
        await sleep(delay);
      }
    }

    // 6. 输出统计结果
    logger.info("\n=== 批量随机交易任务完成 ===");
    logger.info(`总处理钱包: ${shuffledWallets.length}`);
    logger.info(`交易成功: ${successCount}`);
    logger.info(`交易失败: ${failedCount}`);

    if (failedWallets.length > 0) {
      logger.info("\n失败钱包详情:");
      failedWallets.forEach(wallet => {
        logger.error(`  [${wallet.index}] ${wallet.address || 'N/A'}: ${wallet.reason}`);
      });
    }

    // 7. 保存失败记录
    if (failedWallets.length > 0) {
      await saveFailedWallets(failedWallets);
    }

  } catch (error) {
    logger.error("批量随机交易任务执行失败:", error.message);
    throw error;
  }
}

/**
 * 保存交易结果
 */
async function saveTradingResult(index, address, tradingResult) {
  try {
    const resultData = {
      index,
      address,
      timestamp: new Date().toISOString(),
      success: tradingResult.success,
      totalTrades: tradingResult.data?.totalTrades || 0,
      successCount: tradingResult.data?.successCount || 0,
      failedCount: tradingResult.data?.failedCount || 0,
      trades: tradingResult.data?.trades || []
    };

    logger.info(`钱包 [${index}] 交易结果已记录`);
    
    // 示例：保存到JSON文件
    // const fs = await import('fs/promises');
    // const resultPath = `./results/trading_${index}_${Date.now()}.json`;
    // await fs.writeFile(resultPath, JSON.stringify(resultData, null, 2));

  } catch (error) {
    logger.error(`保存钱包 [${index}] 交易结果失败: ${error.message}`);
  }
}

/**
 * 保存失败钱包记录
 */
async function saveFailedWallets(failedWallets) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const failedData = {
      timestamp: new Date().toISOString(),
      totalFailed: failedWallets.length,
      wallets: failedWallets
    };

    logger.info(`保存 ${failedWallets.length} 个失败钱包记录`);
    
    // 示例：保存到JSON文件
    // const fs = await import('fs/promises');
    // const failedPath = `./results/trading_failed_${timestamp}.json`;
    // await fs.writeFile(failedPath, JSON.stringify(failedData, null, 2));

  } catch (error) {
    logger.error("保存失败钱包记录时发生错误:", error.message);
  }
}

// 主程序入口
if (import.meta.url === `file://${process.argv[1]}`) {
  // 批量钱包交易任务
  executeBatchTradingTask().catch(error => {
    logger.error("程序执行失败:", error.message);
    process.exit(1);
  });

  // 单个钱包测试（取消注释使用）
  // testSingleWalletTrading();
}
