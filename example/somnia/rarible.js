import { ethers } from "ethers";
import logger from "../../base/tools/logger.js";
import { randomSleep } from "../../base/tools/common.js";

class Rarible {
  constructor(index, wallet, proxy) {
    this.index = index;
    this.wallet = wallet;
    this.proxy = proxy;

    this.RARIBLE_CONTRACT = '******************************************'; // Rarible contract
    
    // 合约ABI - claim函数和balanceOf函数
    this.CONTRACT_ABI = [
      "function claim(address _receiver, uint256 _quantity, address _currency, uint256 _pricePerToken, (bytes32[],uint256,uint256,address) _allowlistProof, bytes _data) external payable",
      "function balanceOf(address owner) external view returns (uint256)"
    ];

    // 固定参数
    this.FIXED_QUANTITY = 1;
    this.FIXED_CURRENCY = "******************************************";
    this.FIXED_PRICE_PER_TOKEN = ethers.parseEther("0.603"); // 0.603 ETH in wei
    this.FIXED_ALLOWLIST_PROOF = [
      [], // bytes32[] - empty array
      0, // uint256
      "115792089237316195423570985008687907853269984665640564039457584007913129639935", // uint256 - max uint256
      "******************************************" // address - zero address
    ];
    this.FIXED_DATA = "0x"; // empty bytes
  }

  // 获取合约实例
  #getContract() {
    return new ethers.Contract(this.RARIBLE_CONTRACT, this.CONTRACT_ABI, this.wallet);
  }

  // 检查钱包是否已经拥有该合约的NFT
  async #checkNFTBalance() {
    try {
      const contract = this.#getContract();
      const balance = await contract.balanceOf(this.wallet.address);

      logger.info(`钱包 [${this.index}] NFT余额检查: ${balance.toString()}`);

      return {
        success: true,
        balance: balance.toString(),
        hasNFT: balance > 0
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 检查NFT余额失败: ${error.message}`);
      return {
        success: false,
        msg: `检查NFT余额失败: ${error.message}`,
        error: error.message,
        hasNFT: false
      };
    }
  }

  // 检查STT余额是否足够
  async #checkSTTBalance(requiredAmount = BigInt(0), estimatedGas = BigInt(300000)) {
    try {
      const sttBalance = await this.wallet.provider.getBalance(this.wallet.address);
      const feeData = await this.wallet.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(**********);
      const gasEstimate = estimatedGas * gasPrice;
      const totalSTTRequired = gasEstimate + requiredAmount;

      logger.info(`钱包 [${this.index}] STT余额: ${ethers.formatEther(sttBalance)} 需要STT: ${ethers.formatEther(totalSTTRequired)} (gas: ${ethers.formatEther(gasEstimate)}, 费用: ${ethers.formatEther(requiredAmount)})`);

      if (sttBalance < totalSTTRequired) {
        return {
          success: false,
          msg: `STT余额不足 ｜ 当前: ${ethers.formatEther(sttBalance)} STT, 需要: ${ethers.formatEther(totalSTTRequired)} STT`,
          error: 'Insufficient STT balance'
        };
      }

      return { success: true, gasPrice, gasLimit: estimatedGas };
    } catch (error) {
      return {
        success: false,
        msg: `检查STT余额失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 执行claim合约调用
  async #claimContract() {
    try {

      const contract = this.#getContract();
      const receiver = this.wallet.address;

      // 计算需要发送的ETH (quantity * pricePerToken)
      const valueToSend = BigInt(this.FIXED_QUANTITY) * this.FIXED_PRICE_PER_TOKEN;

      // 先估算 gas
      let gasEstimate;
      try {
        gasEstimate = await contract.claim.estimateGas(
          receiver,
          this.FIXED_QUANTITY,
          this.FIXED_CURRENCY,
          this.FIXED_PRICE_PER_TOKEN,
          this.FIXED_ALLOWLIST_PROOF,
          this.FIXED_DATA,
          { value: valueToSend }
        );
        // 增加 20% 的 gas 余量
        gasEstimate = gasEstimate * BigInt(120) / BigInt(100);
        logger.info(`钱包 [${this.index}] claim gas估算成功: ${gasEstimate.toString()}`);
      } catch (estimateError) {
        logger.info(`钱包 [${this.index}] claim gas估算失败，使用默认值: ${estimateError.message}`);
        gasEstimate = BigInt(500000); // 默认值
      }

      // 检查余额（包含估算的gas费用和claim费用）
      const balanceCheck = await this.#checkSTTBalance(valueToSend, gasEstimate);
      if (!balanceCheck.success) {
        return balanceCheck;
      }

      const tx = await contract.claim(
        receiver,
        this.FIXED_QUANTITY,
        this.FIXED_CURRENCY,
        this.FIXED_PRICE_PER_TOKEN,
        this.FIXED_ALLOWLIST_PROOF,
        this.FIXED_DATA,
        {
          value: valueToSend,
          gasLimit: gasEstimate,
          gasPrice: balanceCheck.gasPrice
        }
      );

      logger.info(`钱包 [${this.index}] claim交易已发送，哈希: ${tx.hash}`);
      const receipt = await tx.wait();

      if (receipt.status === 1) {
        logger.success(`钱包 [${this.index}] claim成功，tx: ${tx.hash}`)
        return {
          success: true,
          msg: `Rarible claim成功 ｜ 数量：${this.FIXED_QUANTITY} ｜ 费用：${ethers.formatEther(valueToSend)} STT ｜ 哈希：${tx.hash}`,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          quantity: this.FIXED_QUANTITY,
          cost: ethers.formatEther(valueToSend),
          action: 'claim'
        };
      } else {
        logger.error(`钱包 [${this.index}] claim失败`)
        return {
          success: false,
          msg: `Rarible claim失败 ｜ 哈希：${tx.hash}`,
          transactionHash: tx.hash,
          error: 'Transaction failed'
        };
      }

    } catch (error) {
      // 检查是否是合约执行回滚错误
      let errorMsg = error.message;
      if (error.message.includes('transaction execution reverted')) {
        errorMsg = `合约执行回滚，可能原因：1)已经claim过 2)allowlist验证失败 3)合约状态检查失败 4)余额不足`;
      } else if (error.message.includes('insufficient funds')) {
        errorMsg = `余额不足，需要 ${ethers.formatEther(this.FIXED_PRICE_PER_TOKEN)} ETH 进行claim`;
      }

      return {
        success: false,
        msg: `Rarible claim失败, 错误信息: ${errorMsg}`,
        error: error.message
      };
    }
  }

  // 执行完整的claim流程
  async execute() {
    try {
      logger.info(`钱包 [${this.index}] 开始执行Rarible claim流程`);

      // 首先检查钱包是否已经拥有该NFT
      const balanceCheck = await this.#checkNFTBalance();
      if (!balanceCheck.success) {
        logger.warn(`钱包 [${this.index}] 无法检查NFT余额，继续执行claim`);
      } else if (balanceCheck.hasNFT) {
        logger.success(`钱包 [${this.index}] 已经拥有该NFT (余额: ${balanceCheck.balance})，跳过claim`);
        return {
          success: true,
          msg: `钱包已拥有NFT ｜ 余额：${balanceCheck.balance} ｜ 跳过claim操作`,
          balance: balanceCheck.balance,
          action: 'skip_already_owned',
          alreadyOwned: true
        };
      }

      logger.info(`钱包 [${this.index}] 未拥有该NFT，开始执行claim`);

      // 执行claim
      return await this.#claimContract();

    } catch (error) {
      return {
        success: false,
        msg: `Rarible claim流程执行时发生错误: ${error.message}`,
        error: error.message
      };
    }
  }
}

export default Rarible;
