import { ethers } from "ethers";
import logger from "../../base/tools/logger.js";
import { randomSleep } from "../../base/tools/common.js";

class StandardWeb3 {
  constructor(index, wallet, proxy) {
    this.index = index;
    this.wallet = wallet;
    this.proxy = proxy;
    this.address = this.wallet.getAddress();

    // StandardWeb3 固定配置
    this.DOMAIN = 'testnet.standardweb3.com';
    this.URI = 'https://testnet.standardweb3.com/somnia-testnet/trade';
    this.VERSION = '1';
    this.CHAIN_ID = '50312';
    this.STATEMENT = 'Welcome to Digital Native Standard ltd.. Signing is the only way we can truly know that you are the owner of the wallet you are connecting. Signing is a safe, gas-less transaction that does not in any way give Digital Native Standard ltd. permission to perform any transactions with your wallet.';
  }

  // 生成加密安全的32位十六进制nonce
  #generateSecureNonce() {
    try {
      // 浏览器环境
      if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        return Array.from(crypto.getRandomValues(new Uint8Array(16)))
          .map(b => b.toString(16).padStart(2, '0'))
          .join('');
      }
      
      // Node.js环境
      if (typeof require !== 'undefined') {
        const crypto = require('crypto');
        return crypto.randomBytes(16).toString('hex');
      }
      
      // 降级方案（不推荐用于生产环境）
      return Array.from({length: 32}, () => 
        Math.floor(Math.random() * 16).toString(16)
      ).join('');
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成nonce失败: ${error.message}`);
      // 最后的降级方案
      return Array.from({length: 32}, () => 
        Math.floor(Math.random() * 16).toString(16)
      ).join('');
    }
  }

  // 生成随机请求ID (UUID格式)
  #generateRequestId() {
    try {
      // 浏览器环境
      if (typeof crypto !== 'undefined' && crypto.randomUUID) {
        return crypto.randomUUID();
      }
      
      // Node.js环境
      if (typeof require !== 'undefined') {
        const crypto = require('crypto');
        return crypto.randomUUID();
      }
      
      // 手动生成UUID v4格式
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成请求ID失败: ${error.message}`);
      // 降级方案
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
  }

  // 构造SIWE格式的签名消息
  #buildSignMessage(nonce, issuedAt, requestId) {
    return `${this.DOMAIN} wants you to sign in with your Ethereum account:
${this.address}

${this.STATEMENT}

URI: ${this.URI}
Version: ${this.VERSION}
Chain ID: ${this.CHAIN_ID}
Nonce: ${nonce}
Issued At: ${issuedAt}
Request ID: ${requestId}`;
  }

  // 验证钱包连接状态
  async #validateWallet() {
    try {
      if (!this.wallet) {
        return {
          success: false,
          msg: '钱包实例未初始化',
          error: 'Wallet not initialized'
        };
      }

      if (!this.address) {
        return {
          success: false,
          msg: '无法获取钱包地址',
          error: 'Cannot get wallet address'
        };
      }

      // 检查钱包是否连接到正确的网络
      const network = await this.wallet.provider.getNetwork();
      if (network.chainId.toString() !== this.CHAIN_ID) {
        logger.warn(`钱包 [${this.index}] 当前网络链ID: ${network.chainId}, 期望链ID: ${this.CHAIN_ID}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        msg: `钱包验证失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 执行签名操作
  async #performSign(message) {
    try {
      logger.info(`钱包 [${this.index}] 开始签名消息`);
      
      const signature = await this.wallet.signMessage(message);
      
      logger.info(`钱包 [${this.index}] 签名成功: ${signature.substring(0, 10)}...${signature.substring(signature.length - 10)}`);
      
      return {
        success: true,
        signature: signature
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 签名失败: ${error.message}`);
      
      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.message.includes('user rejected')) {
        errorMsg = '用户拒绝签名';
      } else if (error.message.includes('invalid private key')) {
        errorMsg = '私钥无效';
      } else if (error.message.includes('network')) {
        errorMsg = '网络连接错误';
      }
      
      return {
        success: false,
        msg: `签名失败: ${errorMsg}`,
        error: error.message
      };
    }
  }

  // 执行完整的登录签名流程
  async signIn() {
    try {
      logger.info(`钱包 [${this.index}] ${this.address} 开始StandardWeb3登录签名流程`);

      // 1. 验证钱包状态
      const walletValidation = await this.#validateWallet();
      if (!walletValidation.success) {
        return walletValidation;
      }

      // 2. 生成签名所需的参数
      const nonce = this.#generateSecureNonce();
      const requestId = this.#generateRequestId();
      const issuedAt = new Date().toISOString();

      logger.info(`钱包 [${this.index}] 生成签名参数:`);
      logger.info(`  - Nonce: ${nonce}`);
      logger.info(`  - Request ID: ${requestId}`);
      logger.info(`  - Issued At: ${issuedAt}`);

      // 3. 构造签名消息
      const message = this.#buildSignMessage(nonce, issuedAt, requestId);
      
      logger.info(`钱包 [${this.index}] 待签名消息:`);
      logger.info(`\n${message}\n`);

      // 4. 执行签名
      const signResult = await this.#performSign(message);
      if (!signResult.success) {
        return signResult;
      }

      // 5. 返回完整的签名数据
      const signData = {
        address: this.address,
        signature: signResult.signature,
        message: message,
        messageData: {
          domain: this.DOMAIN,
          address: this.address,
          statement: this.STATEMENT,
          uri: this.URI,
          version: this.VERSION,
          chainId: this.CHAIN_ID,
          nonce: nonce,
          issuedAt: issuedAt,
          requestId: requestId
        }
      };

      logger.success(`钱包 [${this.index}] ${this.address} StandardWeb3登录签名成功`);

      return {
        success: true,
        msg: `StandardWeb3登录签名成功 ｜ 地址：${this.address} ｜ Nonce：${nonce}`,
        data: signData,
        action: 'sign_in'
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] StandardWeb3登录签名流程执行时发生错误: ${error.message}`);
      return {
        success: false,
        msg: `StandardWeb3登录签名流程执行时发生错误: ${error.message}`,
        error: error.message
      };
    }
  }

  // 验证签名（可选功能）
  async verifySignature(signData) {
    try {
      const { address, signature, message } = signData;

      // 使用ethers.js验证签名
      const recoveredAddress = ethers.verifyMessage(message, signature);

      const isValid = recoveredAddress.toLowerCase() === address.toLowerCase();

      logger.info(`钱包 [${this.index}] 签名验证结果: ${isValid ? '有效' : '无效'}`);
      logger.info(`  - 原始地址: ${address}`);
      logger.info(`  - 恢复地址: ${recoveredAddress}`);

      return {
        success: true,
        isValid: isValid,
        originalAddress: address,
        recoveredAddress: recoveredAddress,
        msg: isValid ? '签名验证成功' : '签名验证失败'
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 签名验证失败: ${error.message}`);
      return {
        success: false,
        msg: `签名验证失败: ${error.message}`,
        error: error.message
      };
    }
  }


}

export default StandardWeb3;
