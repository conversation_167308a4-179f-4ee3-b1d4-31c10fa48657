import { ethers } from "ethers";
import axios from "axios";
import crypto from "crypto";
import { HttpsProxyAgent } from "https-proxy-agent";
import logger from "../../base/tools/logger.js";

class StandardWeb3 {
  constructor(index, wallet, proxy) {
    this.index = index;
    this.wallet = wallet;
    this.proxy = proxy;
    this.address = this.wallet.getAddress();

    // StandardWeb3 固定配置
    this.DOMAIN = "testnet.standardweb3.com";
    this.URI = "https://testnet.standardweb3.com/somnia-testnet/trade";
    this.VERSION = "1";
    this.CHAIN_ID = "50312";
    this.STATEMENT =
      "Welcome to Digital Native Standard ltd.. Signing is the only way we can truly know that you are the owner of the wallet you are connecting. Signing is a safe, gas-less transaction that does not in any way give Digital Native Standard ltd. permission to perform any transactions with your wallet.";

    // Dynamic Auth API 配置
    this.DYNAMIC_API_BASE = "https://app.dynamicauth.com/api/v0/sdk";
    this.SDK_REQUEST_ID = this.#generateRequestId(); // 用于API请求的Request ID

    // 生成动态请求头参数
    this.DYNAMIC_HEADERS = this.#generateDynamicHeaders();

    // 创建axios实例
    this.axiosInstance = this.#createAxiosInstance();
  }

  // 生成设备指纹
  #generateDeviceFingerprint() {
    try {
      const components = {
        timestamp: Date.now(),
        random: Math.random(),
        userAgent:
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        platform: "macOS",
        language: "zh-CN",
        colorDepth: "24",
        resolution: "2560x1440",
        availableResolution: "2560x1400",
        timezone: "Asia/Shanghai",
        timezoneOffset: -480,
        sessionStorage: true,
        localStorage: true,
        indexedDb: true,
        cpuCores: 16,
        deviceMemory: 32,
        address: this.address, // 使用钱包地址作为唯一标识
        uniqueId: crypto.randomBytes(16).toString("hex"),
      };

      const componentsString = Object.entries(components)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}:${value}`)
        .join("|");

      return crypto.createHash("md5").update(componentsString).digest("hex");
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成设备指纹失败: ${error.message}`);
      // 降级方案：使用钱包地址生成固定指纹
      return crypto
        .createHash("md5")
        .update(this.address + Date.now())
        .digest("hex");
    }
  }

  // 生成会话公钥（椭圆曲线公钥格式）
  #generateSessionPublicKey() {
    try {
      // 生成32字节随机数作为私钥
      const privateKey = crypto.randomBytes(32);

      // 使用secp256k1曲线生成公钥
      const keyPair = crypto.createECDH("secp256k1");
      keyPair.setPrivateKey(privateKey);
      return keyPair.getPublicKey("hex", "compressed");
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成会话公钥失败: ${error.message}`);
      // 降级方案：生成固定格式的公钥
      const randomBytes = crypto.randomBytes(32);
      return "03" + randomBytes.toString("hex");
    }
  }

  // 生成动态请求ID（base64编码格式）
  #generateDynamicRequestId() {
    try {
      // 生成32字节随机数据
      const randomData = crypto.randomBytes(32);
      // 转换为base64并移除填充字符
      return randomData
        .toString("base64")
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成动态请求ID失败: ${error.message}`);
      // 降级方案
      return crypto.randomBytes(32).toString("hex");
    }
  }

  // 生成所有动态请求头
  #generateDynamicHeaders() {
    const deviceFingerprint = this.#generateDeviceFingerprint();
    const sessionPublicKey = this.#generateSessionPublicKey();
    const dynamicRequestId = this.#generateDynamicRequestId();

    logger.info(`钱包 [${this.index}] 生成动态请求头:`);
    logger.info(`  - Device Fingerprint: ${deviceFingerprint}`);
    logger.info(`  - Session Public Key: ${sessionPublicKey}`);
    logger.info(`  - Dynamic Request ID: ${dynamicRequestId}`);

    return {
      "x-dyn-api-version": "API/0.0.728",
      "x-dyn-device-fingerprint": deviceFingerprint,
      "x-dyn-is-global-wallet-popup": "false",
      "x-dyn-request-id": dynamicRequestId,
      "x-dyn-session-public-key": sessionPublicKey,
      "x-dyn-version": "WalletKit/4.25.5",
    };
  }

  // 创建axios实例
  #createAxiosInstance() {
    const baseConfig = {
      timeout: 15000,
      headers: {
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://testnet.standardweb3.com',
        'referer': 'https://testnet.standardweb3.com/',
        'sec-ch-ua': '"Google Chrome";v="138", "Chromium";v="138", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ...this.DYNAMIC_HEADERS
      }
    };

    // 如果设置了代理，添加代理配置
    if (this.proxy && this.proxy.trim() !== '') {
      baseConfig.httpsAgent = new HttpsProxyAgent(this.proxy);
      baseConfig.proxy = false;
      logger.info(`钱包 [${this.index}] axios实例使用代理: ${this.proxy.substring(0, 20)}...`);
    }

    return axios.create(baseConfig);
  }

  // 从Dynamic Auth API获取nonce
  async #getNonce() {
    try {
      logger.info(`钱包 [${this.index}] 开始从API获取nonce`);

      const url = `${this.DYNAMIC_API_BASE}/${this.SDK_REQUEST_ID}/nonce`;

      const response = await this.axiosInstance.get(url);

      if (response.status === 200 && response.data && response.data.nonce) {
        const nonce = response.data.nonce;
        logger.success(`钱包 [${this.index}] 成功获取nonce: ${nonce}`);
        return {
          success: true,
          nonce: nonce,
        };
      } else {
        logger.error(`钱包 [${this.index}] API返回数据格式错误:`, response.data);
        return {
          success: false,
          msg: "API返回数据格式错误",
          error: "Invalid response format",
        };
      }
    } catch (error) {
      logger.error(`钱包 [${this.index}] 获取nonce失败: ${error.message}`);

      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.response) {
        errorMsg = `HTTP ${error.response.status}: ${error.response.statusText}`;
        logger.error(`钱包 [${this.index}] API响应错误:`, error.response.data);
      } else if (error.code === "ECONNABORTED") {
        errorMsg = "请求超时";
      } else if (error.code === "ENOTFOUND") {
        errorMsg = "DNS解析失败";
      }

      return {
        success: false,
        msg: `获取nonce失败: ${errorMsg}`,
        error: error.message,
      };
    }
  }

  // 生成随机请求ID (UUID格式) - 用于签名消息
  #generateRequestId() {
    try {
      // Node.js环境
      return crypto.randomUUID();
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成请求ID失败: ${error.message}`);
      // 降级方案
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    }
  }

  // 构造SIWE格式的签名消息
  #buildSignMessage(nonce, issuedAt, requestId) {
    return `${this.DOMAIN} wants you to sign in with your Ethereum account:
${this.address}

${this.STATEMENT}

URI: ${this.URI}
Version: ${this.VERSION}
Chain ID: ${this.CHAIN_ID}
Nonce: ${nonce}
Issued At: ${issuedAt}
Request ID: ${requestId}`;
  }

  // 验证钱包连接状态
  async #validateWallet() {
    try {
      if (!this.wallet) {
        return {
          success: false,
          msg: "钱包实例未初始化",
          error: "Wallet not initialized",
        };
      }

      if (!this.address) {
        return {
          success: false,
          msg: "无法获取钱包地址",
          error: "Cannot get wallet address",
        };
      }

      // 检查钱包是否连接到正确的网络
      const network = await this.wallet.provider.getNetwork();
      if (network.chainId.toString() !== this.CHAIN_ID) {
        logger.warn(
          `钱包 [${this.index}] 当前网络链ID: ${network.chainId}, 期望链ID: ${this.CHAIN_ID}`,
        );
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        msg: `钱包验证失败: ${error.message}`,
        error: error.message,
      };
    }
  }

  // 执行签名操作
  async #performSign(message) {
    try {
      logger.info(`钱包 [${this.index}] 开始签名消息`);

      const signature = await this.wallet.signMessage(message);

      logger.info(
        `钱包 [${this.index}] 签名成功: ${signature.substring(0, 10)}...${signature.substring(
          signature.length - 10,
        )}`,
      );

      return {
        success: true,
        signature: signature,
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 签名失败: ${error.message}`);

      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.message.includes("user rejected")) {
        errorMsg = "用户拒绝签名";
      } else if (error.message.includes("invalid private key")) {
        errorMsg = "私钥无效";
      } else if (error.message.includes("network")) {
        errorMsg = "网络连接错误";
      }

      return {
        success: false,
        msg: `签名失败: ${errorMsg}`,
        error: error.message,
      };
    }
  }

  // 执行完整的登录签名流程
  async sign() {
    try {
      logger.info(`钱包 [${this.index}] ${this.address} 开始StandardWeb3登录签名流程`);

      // 1. 验证钱包状态
      const walletValidation = await this.#validateWallet();
      if (!walletValidation.success) {
        return walletValidation;
      }

      // 2. 从API获取nonce
      const nonceResult = await this.#getNonce();

      if (!nonceResult.success) {
        logger.error(`钱包 [${this.index}] API获取nonce失败: ${nonceResult.msg}`);
        return {
          success: false,
          msg: `获取nonce失败: ${nonceResult.msg}`,
          error: nonceResult.error
        };
      }

      const nonce = nonceResult.nonce;
      logger.info(`钱包 [${this.index}] 使用API获取的nonce: ${nonce}`);

      // 3. 生成其他签名参数
      const requestId = this.#generateRequestId(); // 生成新的UUID格式Request ID
      const issuedAt = new Date().toISOString();

      logger.info(`钱包 [${this.index}] 签名参数:`);
      logger.info(`  - Nonce: ${nonce}`);
      logger.info(`  - Request ID: ${requestId}`);
      logger.info(`  - Issued At: ${issuedAt}`);

      // 4. 构造签名消息
      const message = this.#buildSignMessage(nonce, issuedAt, requestId);

      logger.info(`钱包 [${this.index}] 待签名消息:`);
      logger.info(`\n${message}\n`);

      // 5. 执行签名
      const signResult = await this.#performSign(message);
      if (!signResult.success) {
        return signResult;
      }

      // 6. 返回完整的签名数据
      const signData = {
        address: this.address,
        signature: signResult.signature,
        message: message,
        messageData: {
          domain: this.DOMAIN,
          address: this.address,
          statement: this.STATEMENT,
          uri: this.URI,
          version: this.VERSION,
          chainId: this.CHAIN_ID,
          nonce: nonce,
          issuedAt: issuedAt,
          requestId: requestId,
        },
        nonceSource: "api",
      };

      logger.success(`钱包 [${this.index}] ${this.address} StandardWeb3登录签名成功`);

      return {
        success: true,
        msg: `StandardWeb3登录签名成功 ｜ 地址：${this.address} ｜ Nonce：${nonce} ｜ 来源：API`,
        data: signData,
        action: "sign_in",
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] StandardWeb3登录签名流程执行时发生错误: ${error.message}`);
      return {
        success: false,
        msg: `StandardWeb3登录签名流程执行时发生错误: ${error.message}`,
        error: error.message,
      };
    }
  }

  // 向Dynamic Auth API提交签名进行验证
  async verify(signData) {
    try {
      logger.info(`钱包 [${this.index}] 开始向Dynamic Auth API提交签名验证`);

      const url = `${this.DYNAMIC_API_BASE}/${this.SDK_REQUEST_ID}/verify`;

      // 构造请求载荷
      const payload = {
        signedMessage: signData.signature,
        messageToSign: signData.message,
        publicWalletAddress: signData.address,
        chain: "EVM",
        walletName: "okxwallet",
        walletProvider: "browserExtension",
        network: this.CHAIN_ID,
        additionalWalletAddresses: [],
        sessionPublicKey: this.DYNAMIC_HEADERS['x-dyn-session-public-key']
      };

      logger.info(`钱包 [${this.index}] 发送验证请求到: ${url}`);
      logger.info(`钱包 [${this.index}] 请求载荷:`, {
        signedMessage: payload.signedMessage.substring(0, 20) + '...',
        publicWalletAddress: payload.publicWalletAddress,
        chain: payload.chain,
        network: payload.network
      });

      const response = await this.axiosInstance.post(url, payload);

      if (response.status === 200 && response.data) {
        logger.success(`钱包 [${this.index}] Dynamic Auth验证成功`);

        const { jwt, minifiedJwt, expiresAt, user } = response.data;

        logger.info(`钱包 [${this.index}] 验证结果:`);
        logger.info(`  - 用户ID: ${user.id}`);
        logger.info(`  - 会话ID: ${user.sessionId}`);
        logger.info(`  - JWT过期时间: ${new Date(expiresAt * 1000).toISOString()}`);
        logger.info(`  - 新用户: ${user.newUser}`);

        return {
          success: true,
          msg: 'Dynamic Auth验证成功',
          data: {
            jwt: jwt,
            minifiedJwt: minifiedJwt,
            expiresAt: expiresAt,
            user: user,
            verifiedCredentials: user.verifiedCredentials
          },
          statusCode: response.status
        };
      } else {
        logger.error(`钱包 [${this.index}] Dynamic Auth验证失败: 响应格式错误`);
        return {
          success: false,
          msg: 'Dynamic Auth验证失败: 响应格式错误',
          error: 'Invalid response format',
          statusCode: response.status
        };
      }

    } catch (error) {
      logger.error(`钱包 [${this.index}] Dynamic Auth验证请求失败: ${error.message}`);

      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.response) {
        errorMsg = `HTTP ${error.response.status}: ${error.response.statusText}`;
        logger.error(`钱包 [${this.index}] API响应错误:`, error.response.data);
      } else if (error.code === 'ECONNABORTED') {
        errorMsg = '请求超时';
      } else if (error.code === 'ENOTFOUND') {
        errorMsg = 'DNS解析失败';
      }

      return {
        success: false,
        msg: `Dynamic Auth验证失败: ${errorMsg}`,
        error: error.message,
        statusCode: error.response?.status
      };
    }
  }

  // 完整的登录和验证流程
  async loginAndVerify() {
    try {
      logger.info(`钱包 [${this.index}] 开始完整的登录和验证流程`);

      // 1. 执行签名
      const signResult = await this.sign();
      if (!signResult.success) {
        return signResult;
      }

      // 2. 向Dynamic Auth API提交验证
      const verifyResult = await this.verify(signResult.data);
      if (!verifyResult.success) {
        return {
          success: false,
          msg: `验证失败: ${verifyResult.msg}`,
          signData: signResult.data,
          verifyError: verifyResult.error
        };
      }

      logger.success(`钱包 [${this.index}] ${this.address} StandardWeb3完整流程成功`);

      return {
        success: true,
        msg: `StandardWeb3登录验证成功 ｜ 地址：${this.address} ｜ 用户ID：${verifyResult.data.user.id}`,
        signData: signResult.data,
        verifyData: verifyResult.data,
        action: 'login_and_verify_complete'
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] 完整流程执行时发生错误: ${error.message}`);
      return {
        success: false,
        msg: `完整流程执行时发生错误: ${error.message}`,
        error: error.message
      };
    }
  }
}

export default StandardWeb3;
