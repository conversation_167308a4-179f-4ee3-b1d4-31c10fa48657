import { ethers } from "ethers";
import axios from "axios";
import crypto from "crypto";
import { HttpsProxyAgent } from "https-proxy-agent";
import logger from "../../base/tools/logger.js";
import { randomSleep, getRandomValue } from "../../base/tools/common.js";

class StandardWeb3 {
  constructor(index, wallet, proxy) {
    this.index = index;
    this.wallet = wallet;
    this.proxy = proxy;
    this.address = this.wallet.getAddress();

    // StandardWeb3 固定配置
    this.DOMAIN = "testnet.standardweb3.com";
    this.URI = "https://testnet.standardweb3.com/somnia-testnet/trade";
    this.VERSION = "1";
    this.CHAIN_ID = "50312";
    this.STATEMENT =
      "Welcome to Digital Native Standard ltd.. Signing is the only way we can truly know that you are the owner of the wallet you are connecting. Signing is a safe, gas-less transaction that does not in any way give Digital Native Standard ltd. permission to perform any transactions with your wallet.";

    // Dynamic Auth API 配置
    this.DYNAMIC_API_BASE = "https://app.dynamicauth.com/api/v0/sdk";
    this.SDK_REQUEST_ID = this.#generateRequestId(); // 用于API请求的Request ID

    // 生成动态请求头参数
    this.DYNAMIC_HEADERS = this.#generateDynamicHeaders();

    // 创建axios实例
    this.axiosInstance = this.#createAxiosInstance();

    // 交易相关配置
    this.TRADING_CONTRACT = '******************************************';
    this.TOKENS = {
      STT: '******************************************',
      USDC: '******************************************',
      SOL: '******************************************',
      BTC: '******************************************'
    };
    this.TRADE_PARAMS = {
      isMaker: true,
      n: 20,
      slippageLimit: 10000000
    };

    // 合约ABI
    this.TRADING_ABI = [
      "function marketSellETH(address quote, bool isMaker, uint32 n, address recipient, uint32 slippageLimit) external payable",
      "function marketBuy(address base, address quote, uint256 quoteAmount, bool isMaker, uint32 n, address recipient, uint32 slippageLimit) external",
      "function marketSell(address base, address quote, uint256 baseAmount, bool isMaker, uint32 n, address recipient, uint32 slippageLimit) external"
    ];
    this.ERC20_ABI = [
      "function balanceOf(address owner) view returns (uint256)",
      "function approve(address spender, uint256 amount) returns (bool)",
      "function allowance(address owner, address spender) view returns (uint256)",
      "function decimals() view returns (uint8)"
    ];

    // 创建交易合约实例
    this.tradingContract = new ethers.Contract(this.TRADING_CONTRACT, this.TRADING_ABI, this.wallet);

    // Gas配置
    this.gasConfig = {
      gasLimitMultiplier: 1.2, // gas limit 乘数
      gasPriceMultiplier: 1.1   // gas price 乘数
    };
  }

  // 生成设备指纹
  #generateDeviceFingerprint() {
    try {
      const components = {
        timestamp: Date.now(),
        random: Math.random(),
        userAgent:
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        platform: "macOS",
        language: "zh-CN",
        colorDepth: "24",
        resolution: "2560x1440",
        availableResolution: "2560x1400",
        timezone: "Asia/Shanghai",
        timezoneOffset: -480,
        sessionStorage: true,
        localStorage: true,
        indexedDb: true,
        cpuCores: 16,
        deviceMemory: 32,
        address: this.address, // 使用钱包地址作为唯一标识
        uniqueId: crypto.randomBytes(16).toString("hex"),
      };

      const componentsString = Object.entries(components)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}:${value}`)
        .join("|");

      return crypto.createHash("md5").update(componentsString).digest("hex");
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成设备指纹失败: ${error.message}`);
      // 降级方案：使用钱包地址生成固定指纹
      return crypto
        .createHash("md5")
        .update(this.address + Date.now())
        .digest("hex");
    }
  }

  // 生成会话公钥（椭圆曲线公钥格式）
  #generateSessionPublicKey() {
    try {
      // 生成32字节随机数作为私钥
      const privateKey = crypto.randomBytes(32);

      // 使用secp256k1曲线生成公钥
      const keyPair = crypto.createECDH("secp256k1");
      keyPair.setPrivateKey(privateKey);
      return keyPair.getPublicKey("hex", "compressed");
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成会话公钥失败: ${error.message}`);
      // 降级方案：生成固定格式的公钥
      const randomBytes = crypto.randomBytes(32);
      return "03" + randomBytes.toString("hex");
    }
  }

  // 生成动态请求ID（base64编码格式）
  #generateDynamicRequestId() {
    try {
      // 生成32字节随机数据
      const randomData = crypto.randomBytes(32);
      // 转换为base64并移除填充字符
      return randomData
        .toString("base64")
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成动态请求ID失败: ${error.message}`);
      // 降级方案
      return crypto.randomBytes(32).toString("hex");
    }
  }

  // 生成所有动态请求头
  #generateDynamicHeaders() {
    const deviceFingerprint = this.#generateDeviceFingerprint();
    const sessionPublicKey = this.#generateSessionPublicKey();
    const dynamicRequestId = this.#generateDynamicRequestId();

    logger.info(`钱包 [${this.index}] 生成动态请求头:`);
    logger.info(`  - Device Fingerprint: ${deviceFingerprint}`);
    logger.info(`  - Session Public Key: ${sessionPublicKey}`);
    logger.info(`  - Dynamic Request ID: ${dynamicRequestId}`);

    return {
      "x-dyn-api-version": "API/0.0.728",
      "x-dyn-device-fingerprint": deviceFingerprint,
      "x-dyn-is-global-wallet-popup": "false",
      "x-dyn-request-id": dynamicRequestId,
      "x-dyn-session-public-key": sessionPublicKey,
      "x-dyn-version": "WalletKit/4.25.5",
    };
  }

  // 创建axios实例
  #createAxiosInstance() {
    const baseConfig = {
      timeout: 15000,
      headers: {
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://testnet.standardweb3.com',
        'referer': 'https://testnet.standardweb3.com/',
        'sec-ch-ua': '"Google Chrome";v="138", "Chromium";v="138", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ...this.DYNAMIC_HEADERS
      }
    };

    // 如果设置了代理，添加代理配置
    if (this.proxy && this.proxy.trim() !== '') {
      baseConfig.httpsAgent = new HttpsProxyAgent(this.proxy);
      baseConfig.proxy = false;
      logger.info(`钱包 [${this.index}] axios实例使用代理: ${this.proxy.substring(0, 20)}...`);
    }

    return axios.create(baseConfig);
  }

  // 从Dynamic Auth API获取nonce
  async #getNonce() {
    try {
      logger.info(`钱包 [${this.index}] 开始从API获取nonce`);

      const url = `${this.DYNAMIC_API_BASE}/${this.SDK_REQUEST_ID}/nonce`;

      const response = await this.axiosInstance.get(url);

      if (response.status === 200 && response.data && response.data.nonce) {
        const nonce = response.data.nonce;
        logger.success(`钱包 [${this.index}] 成功获取nonce: ${nonce}`);
        return {
          success: true,
          nonce: nonce,
        };
      } else {
        logger.error(`钱包 [${this.index}] API返回数据格式错误:`, response.data);
        return {
          success: false,
          msg: "API返回数据格式错误",
          error: "Invalid response format",
        };
      }
    } catch (error) {
      logger.error(`钱包 [${this.index}] 获取nonce失败: ${error.message}`);

      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.response) {
        errorMsg = `HTTP ${error.response.status}: ${error.response.statusText}`;
        logger.error(`钱包 [${this.index}] API响应错误:`, error.response.data);
      } else if (error.code === "ECONNABORTED") {
        errorMsg = "请求超时";
      } else if (error.code === "ENOTFOUND") {
        errorMsg = "DNS解析失败";
      }

      return {
        success: false,
        msg: `获取nonce失败: ${errorMsg}`,
        error: error.message,
      };
    }
  }

  // 生成随机请求ID (UUID格式) - 用于签名消息
  #generateRequestId() {
    try {
      // Node.js环境
      return crypto.randomUUID();
    } catch (error) {
      logger.error(`钱包 [${this.index}] 生成请求ID失败: ${error.message}`);
      // 降级方案
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    }
  }

  // 构造SIWE格式的签名消息
  #buildSignMessage(nonce, issuedAt, requestId) {
    return `${this.DOMAIN} wants you to sign in with your Ethereum account:
${this.address}

${this.STATEMENT}

URI: ${this.URI}
Version: ${this.VERSION}
Chain ID: ${this.CHAIN_ID}
Nonce: ${nonce}
Issued At: ${issuedAt}
Request ID: ${requestId}`;
  }

  // 验证钱包连接状态
  async #validateWallet() {
    try {
      if (!this.wallet) {
        return {
          success: false,
          msg: "钱包实例未初始化",
          error: "Wallet not initialized",
        };
      }

      if (!this.address) {
        return {
          success: false,
          msg: "无法获取钱包地址",
          error: "Cannot get wallet address",
        };
      }

      // 检查钱包是否连接到正确的网络
      const network = await this.wallet.provider.getNetwork();
      if (network.chainId.toString() !== this.CHAIN_ID) {
        logger.warn(
          `钱包 [${this.index}] 当前网络链ID: ${network.chainId}, 期望链ID: ${this.CHAIN_ID}`,
        );
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        msg: `钱包验证失败: ${error.message}`,
        error: error.message,
      };
    }
  }

  // 执行签名操作
  async #performSign(message) {
    try {
      logger.info(`钱包 [${this.index}] 开始签名消息`);

      const signature = await this.wallet.signMessage(message);

      logger.info(
        `钱包 [${this.index}] 签名成功: ${signature.substring(0, 10)}...${signature.substring(
          signature.length - 10,
        )}`,
      );

      return {
        success: true,
        signature: signature,
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 签名失败: ${error.message}`);

      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.message.includes("user rejected")) {
        errorMsg = "用户拒绝签名";
      } else if (error.message.includes("invalid private key")) {
        errorMsg = "私钥无效";
      } else if (error.message.includes("network")) {
        errorMsg = "网络连接错误";
      }

      return {
        success: false,
        msg: `签名失败: ${errorMsg}`,
        error: error.message,
      };
    }
  }

  // 执行完整的登录签名流程
  async sign() {
    try {
      logger.info(`钱包 [${this.index}] ${this.address} 开始StandardWeb3登录签名流程`);

      // 1. 验证钱包状态
      const walletValidation = await this.#validateWallet();
      if (!walletValidation.success) {
        return walletValidation;
      }

      // 2. 从API获取nonce
      const nonceResult = await this.#getNonce();

      if (!nonceResult.success) {
        logger.error(`钱包 [${this.index}] API获取nonce失败: ${nonceResult.msg}`);
        return {
          success: false,
          msg: `获取nonce失败: ${nonceResult.msg}`,
          error: nonceResult.error
        };
      }

      const nonce = nonceResult.nonce;
      logger.info(`钱包 [${this.index}] 使用API获取的nonce: ${nonce}`);

      // 3. 生成其他签名参数
      const requestId = this.#generateRequestId(); // 生成新的UUID格式Request ID
      const issuedAt = new Date().toISOString();

      logger.info(`钱包 [${this.index}] 签名参数:`);
      logger.info(`  - Nonce: ${nonce}`);
      logger.info(`  - Request ID: ${requestId}`);
      logger.info(`  - Issued At: ${issuedAt}`);

      // 4. 构造签名消息
      const message = this.#buildSignMessage(nonce, issuedAt, requestId);

      logger.info(`钱包 [${this.index}] 待签名消息:`);
      logger.info(`\n${message}\n`);

      // 5. 执行签名
      const signResult = await this.#performSign(message);
      if (!signResult.success) {
        return signResult;
      }

      // 6. 返回完整的签名数据
      const signData = {
        address: this.address,
        signature: signResult.signature,
        message: message,
        messageData: {
          domain: this.DOMAIN,
          address: this.address,
          statement: this.STATEMENT,
          uri: this.URI,
          version: this.VERSION,
          chainId: this.CHAIN_ID,
          nonce: nonce,
          issuedAt: issuedAt,
          requestId: requestId,
        },
        nonceSource: "api",
      };

      logger.success(`钱包 [${this.index}] ${this.address} StandardWeb3登录签名成功`);

      return {
        success: true,
        msg: `StandardWeb3登录签名成功 ｜ 地址：${this.address} ｜ Nonce：${nonce} ｜ 来源：API`,
        data: signData,
        action: "sign_in",
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] StandardWeb3登录签名流程执行时发生错误: ${error.message}`);
      return {
        success: false,
        msg: `StandardWeb3登录签名流程执行时发生错误: ${error.message}`,
        error: error.message,
      };
    }
  }

  // 向Dynamic Auth API提交签名进行验证
  async verify(signData) {
    try {
      logger.info(`钱包 [${this.index}] 开始向Dynamic Auth API提交签名验证`);

      const url = `${this.DYNAMIC_API_BASE}/${this.SDK_REQUEST_ID}/verify`;

      // 构造请求载荷
      const payload = {
        signedMessage: signData.signature,
        messageToSign: signData.message,
        publicWalletAddress: signData.address,
        chain: "EVM",
        walletName: "okxwallet",
        walletProvider: "browserExtension",
        network: this.CHAIN_ID,
        additionalWalletAddresses: [],
        sessionPublicKey: this.DYNAMIC_HEADERS['x-dyn-session-public-key']
      };

      logger.info(`钱包 [${this.index}] 发送验证请求到: ${url}`);
      logger.info(`钱包 [${this.index}] 请求载荷:`, {
        signedMessage: payload.signedMessage.substring(0, 20) + '...',
        publicWalletAddress: payload.publicWalletAddress,
        chain: payload.chain,
        network: payload.network
      });

      const response = await this.axiosInstance.post(url, payload);

      if (response.status === 200 && response.data) {
        logger.success(`钱包 [${this.index}] Dynamic Auth验证成功`);

        const { jwt, minifiedJwt, expiresAt, user } = response.data;

        logger.info(`钱包 [${this.index}] 验证结果:`);
        logger.info(`  - 用户ID: ${user.id}`);
        logger.info(`  - 会话ID: ${user.sessionId}`);
        logger.info(`  - JWT过期时间: ${new Date(expiresAt * 1000).toISOString()}`);
        logger.info(`  - 新用户: ${user.newUser}`);

        return {
          success: true,
          msg: 'Dynamic Auth验证成功',
          data: {
            jwt: jwt,
            minifiedJwt: minifiedJwt,
            expiresAt: expiresAt,
            user: user,
            verifiedCredentials: user.verifiedCredentials
          },
          statusCode: response.status
        };
      } else {
        logger.error(`钱包 [${this.index}] Dynamic Auth验证失败: 响应格式错误`);
        return {
          success: false,
          msg: 'Dynamic Auth验证失败: 响应格式错误',
          error: 'Invalid response format',
          statusCode: response.status
        };
      }

    } catch (error) {
      logger.error(`钱包 [${this.index}] Dynamic Auth验证请求失败: ${error.message}`);

      // 检查常见错误类型
      let errorMsg = error.message;
      if (error.response) {
        errorMsg = `HTTP ${error.response.status}: ${error.response.statusText}`;
        logger.error(`钱包 [${this.index}] API响应错误:`, error.response.data);
      } else if (error.code === 'ECONNABORTED') {
        errorMsg = '请求超时';
      } else if (error.code === 'ENOTFOUND') {
        errorMsg = 'DNS解析失败';
      }

      return {
        success: false,
        msg: `Dynamic Auth验证失败: ${errorMsg}`,
        error: error.message,
        statusCode: error.response?.status
      };
    }
  }

  // 完整的登录和验证流程
  async loginAndVerify() {
    try {
      logger.info(`钱包 [${this.index}] 开始完整的登录和验证流程`);

      // 1. 执行签名
      const signResult = await this.sign();
      if (!signResult.success) {
        return signResult;
      }

      // 2. 向Dynamic Auth API提交验证
      const verifyResult = await this.verify(signResult.data);
      if (!verifyResult.success) {
        return {
          success: false,
          msg: `验证失败: ${verifyResult.msg}`,
          signData: signResult.data,
          verifyError: verifyResult.error
        };
      }

      logger.success(`钱包 [${this.index}] ${this.address} StandardWeb3完整流程成功`);

      return {
        success: true,
        msg: `StandardWeb3登录验证成功 ｜ 地址：${this.address} ｜ 用户ID：${verifyResult.data.user.id}`,
        signData: signResult.data,
        verifyData: verifyResult.data,
        action: 'login_and_verify_complete'
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] 完整流程执行时发生错误: ${error.message}`);
      return {
        success: false,
        msg: `完整流程执行时发生错误: ${error.message}`,
        error: error.message
      };
    }
  }

  // ==================== 交易相关方法 ====================

  // 获取动态gas配置
  async #getGasConfig(txData = null) {
    try {
      // 获取当前网络的gas price
      const feeData = await this.wallet.provider.getFeeData();

      let gasPrice = feeData.gasPrice;
      let maxFeePerGas = feeData.maxFeePerGas;
      let maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;

      // 如果是EIP-1559网络，使用maxFeePerGas和maxPriorityFeePerGas
      if (maxFeePerGas && maxPriorityFeePerGas) {
        maxFeePerGas = maxFeePerGas * BigInt(Math.floor(this.gasConfig.gasPriceMultiplier * 100)) / 100n;
        maxPriorityFeePerGas = maxPriorityFeePerGas * BigInt(Math.floor(this.gasConfig.gasPriceMultiplier * 100)) / 100n;

        logger.info(`钱包 [${this.index}] EIP-1559 Gas配置: maxFeePerGas=${ethers.formatUnits(maxFeePerGas, 'gwei')} gwei, maxPriorityFeePerGas=${ethers.formatUnits(maxPriorityFeePerGas, 'gwei')} gwei`);

        return {
          maxFeePerGas: maxFeePerGas,
          maxPriorityFeePerGas: maxPriorityFeePerGas
        };
      } else if (gasPrice) {
        // 传统gas price
        gasPrice = gasPrice * BigInt(Math.floor(this.gasConfig.gasPriceMultiplier * 100)) / 100n;

        logger.info(`钱包 [${this.index}] Legacy Gas配置: gasPrice=${ethers.formatUnits(gasPrice, 'gwei')} gwei`);

        return {
          gasPrice: gasPrice
        };
      }

      // 降级方案
      logger.warn(`钱包 [${this.index}] 无法获取gas信息，使用默认配置`);
      return {
        gasPrice: ethers.parseUnits('20', 'gwei') // 默认20 gwei
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] 获取gas配置失败: ${error.message}`);
      return {
        gasPrice: ethers.parseUnits('20', 'gwei') // 默认20 gwei
      };
    }
  }

  // 估算gas limit
  async #estimateGasLimit(contract, method, params, overrides = {}) {
    try {
      const gasLimit = await contract[method].estimateGas(...params, overrides);
      const adjustedGasLimit = gasLimit * BigInt(Math.floor(this.gasConfig.gasLimitMultiplier * 100)) / 100n;

      logger.info(`钱包 [${this.index}] Gas估算: ${method} - 估算值=${gasLimit.toString()}, 调整后=${adjustedGasLimit.toString()}`);

      return adjustedGasLimit;
    } catch (error) {
      logger.error(`钱包 [${this.index}] Gas估算失败: ${error.message}`);
      // 返回默认gas limit
      return BigInt(500000);
    }
  }

  // 获取代币合约实例
  #getTokenContract(tokenSymbol) {
    const tokenAddress = this.TOKENS[tokenSymbol];
    return new ethers.Contract(tokenAddress, this.ERC20_ABI, this.wallet);
  }

  // 获取代币余额
  async #getTokenBalance(tokenSymbol) {
    try {
      const tokenContract = this.#getTokenContract(tokenSymbol);
      const balance = await tokenContract.balanceOf(this.address);
      const decimals = await tokenContract.decimals();
      const formattedBalance = ethers.formatUnits(balance, decimals);

      logger.info(`钱包 [${this.index}] ${tokenSymbol} 余额: ${formattedBalance}`);
      return {
        raw: balance,
        formatted: parseFloat(formattedBalance),
        decimals: decimals
      };
    } catch (error) {
      logger.error(`钱包 [${this.index}] 获取 ${tokenSymbol} 余额失败: ${error.message}`);
      return { raw: 0n, formatted: 0, decimals: 18 };
    }
  }

  // 检查并approve代币
  async #ensureApproval(tokenSymbol, amount) {
    try {
      const tokenContract = this.#getTokenContract(tokenSymbol);
      const currentAllowance = await tokenContract.allowance(this.address, this.TRADING_CONTRACT);

      if (currentAllowance < amount) {
        logger.info(`钱包 [${this.index}] 需要approve ${tokenSymbol}, 数量: ${ethers.formatUnits(amount, 18)}`);

        // 获取gas配置
        const gasConfig = await this.#getGasConfig();
        const gasLimit = await this.#estimateGasLimit(tokenContract, 'approve', [this.TRADING_CONTRACT, amount]);

        const txOptions = {
          gasLimit: gasLimit,
          ...gasConfig
        };

        const approveTx = await tokenContract.approve(this.TRADING_CONTRACT, amount, txOptions);
        await approveTx.wait();

        logger.success(`钱包 [${this.index}] ${tokenSymbol} approve成功，交易哈希: ${approveTx.hash}`);
        return true;
      }

      return true;
    } catch (error) {
      logger.error(`钱包 [${this.index}] ${tokenSymbol} approve失败: ${error.message}`);
      return false;
    }
  }

  // STT/USDC交易对方法
  async #tradeSTTUSDC() {
    try {
      logger.info(`钱包 [${this.index}] 开始STT/USDC交易对操作`);

      // 检查USDC余额
      const usdcBalance = await this.#getTokenBalance('USDC');

      if (usdcBalance.formatted < 1) {
        // 没有USDC，先卖出STT获得USDC
        const sellAmount = getRandomValue(0.01, 0.06);
        const sellAmountFormatted = parseFloat(sellAmount.toFixed(4));

        logger.info(`钱包 [${this.index}] USDC不足，卖出 ${sellAmountFormatted} STT 获得 USDC`);

        const sttBalance = await this.#getTokenBalance('STT');
        if (sttBalance.formatted < sellAmountFormatted) {
          logger.error(`钱包 [${this.index}] STT余额不足: ${sttBalance.formatted} < ${sellAmountFormatted}`);
          return false;
        }

        // 获取gas配置
        const gasConfig = await this.#getGasConfig();
        const gasLimit = await this.#estimateGasLimit(
          this.tradingContract,
          'marketSellETH',
          [
            this.TOKENS.USDC,
            this.TRADE_PARAMS.isMaker,
            this.TRADE_PARAMS.n,
            this.address,
            this.TRADE_PARAMS.slippageLimit
          ],
          { value: ethers.parseEther(sellAmountFormatted.toString()) }
        );

        const txOptions = {
          value: ethers.parseEther(sellAmountFormatted.toString()),
          gasLimit: gasLimit,
          ...gasConfig
        };

        // 使用marketSellETH卖出STT
        const tx = await this.tradingContract.marketSellETH(
          this.TOKENS.USDC,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit,
          txOptions
        );

        const receipt = await tx.wait();
        logger.success(`钱包 [${this.index}] STT/USDC交易完成，交易哈希: ${receipt.hash}`);
        return true;
      } else {
        logger.info(`钱包 [${this.index}] 已有USDC余额: ${usdcBalance.formatted}，STT/USDC交易对已满足`);
        return true;
      }

    } catch (error) {
      logger.error(`钱包 [${this.index}] STT/USDC交易失败: ${error.message}`);
      return false;
    }
  }

  // SOL/USDC交易对方法
  async #tradeSOLUSDC() {
    try {
      logger.info(`钱包 [${this.index}] 开始SOL/USDC交易对操作`);

      // 1. 检查USDC余额，不足时先卖出STT获得USDC
      let usdcBalance = await this.#getTokenBalance('USDC');
      if (usdcBalance.formatted < 0.1) {
        logger.info(`钱包 [${this.index}] USDC余额不足 (${usdcBalance.formatted})，先卖出STT获得USDC`);

        // 卖出STT获得USDC
        const sellAmount = getRandomValue(0.01, 0.06);
        const sellAmountFormatted = parseFloat(sellAmount.toFixed(4));

        const sttBalance = await this.#getTokenBalance('STT');
        if (sttBalance.formatted < sellAmountFormatted) {
          logger.error(`钱包 [${this.index}] STT余额不足: ${sttBalance.formatted} < ${sellAmountFormatted}`);
          return false;
        }

        // 获取gas配置
        const gasConfig = await this.#getGasConfig();
        const gasLimit = await this.#estimateGasLimit(
          this.tradingContract,
          'marketSellETH',
          [
            this.TOKENS.USDC,
            this.TRADE_PARAMS.isMaker,
            this.TRADE_PARAMS.n,
            this.address,
            this.TRADE_PARAMS.slippageLimit
          ],
          { value: ethers.parseEther(sellAmountFormatted.toString()) }
        );

        const txOptions = {
          value: ethers.parseEther(sellAmountFormatted.toString()),
          gasLimit: gasLimit,
          ...gasConfig
        };

        // 执行STT卖出
        const sellTx = await this.tradingContract.marketSellETH(
          this.TOKENS.USDC,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit,
          txOptions
        );
        await sellTx.wait();

        logger.success(`钱包 [${this.index}] 卖出 ${sellAmountFormatted} STT 获得 USDC 成功`);

        // 等待余额更新
        await randomSleep(2, 3);

        // 重新获取USDC余额
        usdcBalance = await this.#getTokenBalance('USDC');
        if (usdcBalance.formatted < 0.1) {
          logger.warn(`钱包 [${this.index}] 卖出STT后USDC余额仍不足，跳过SOL/USDC交易`);
          return false;
        }
      }

      // 2. 用全部USDC买入SOL

      const buyAmountWei = ethers.parseUnits(usdcBalance.formatted.toString(), 6);

      // approve USDC
      const approveSuccess = await this.#ensureApproval('USDC', buyAmountWei);
      if (!approveSuccess) {
        return false;
      }

      // 获取gas配置
      const buyGasConfig = await this.#getGasConfig();
      const buyGasLimit = await this.#estimateGasLimit(
        this.tradingContract,
        'marketBuy',
        [
          this.TOKENS.SOL,
          this.TOKENS.USDC,
          buyAmountWei,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit
        ]
      );

      const buyTxOptions = {
        gasLimit: buyGasLimit,
        ...buyGasConfig
      };

      // 买入SOL
      logger.info(`钱包 [${this.index}] 用 ${usdcBalance.formatted} USDC 买入 SOL`);
      const buyTx = await this.tradingContract.marketBuy(
        this.TOKENS.SOL,
        this.TOKENS.USDC,
        buyAmountWei,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit,
        buyTxOptions
      );
      await buyTx.wait();

      // 等待一下让余额更新
      await randomSleep(2, 3);

      // 2. 卖出全部SOL
      const solBalance = await this.#getTokenBalance('SOL');
      if (solBalance.formatted <= 0) {
        logger.warn(`钱包 [${this.index}] SOL余额为0，无法卖出`);
        return false;
      }

      // approve SOL
      const solApproveSuccess = await this.#ensureApproval('SOL', solBalance.raw);
      if (!solApproveSuccess) {
        return false;
      }

      // 获取卖出gas配置
      const sellGasConfig = await this.#getGasConfig();
      const sellGasLimit = await this.#estimateGasLimit(
        this.tradingContract,
        'marketSell',
        [
          this.TOKENS.SOL,
          this.TOKENS.USDC,
          solBalance.raw,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit
        ]
      );

      const sellTxOptions = {
        gasLimit: sellGasLimit,
        ...sellGasConfig
      };

      // 卖出SOL
      logger.info(`钱包 [${this.index}] 卖出全部 ${solBalance.formatted} SOL`);
      const sellTx = await this.tradingContract.marketSell(
        this.TOKENS.SOL,
        this.TOKENS.USDC,
        solBalance.raw,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit,
        sellTxOptions
      );

      const receipt = await sellTx.wait();
      logger.success(`钱包 [${this.index}] SOL/USDC交易对完成，交易哈希: ${receipt.hash}`);
      return true;

    } catch (error) {
      logger.error(`钱包 [${this.index}] SOL/USDC交易失败: ${error.message}`);
      return false;
    }
  }

  // BTC/USDC交易对方法
  async #tradeBTCUSDC() {
    try {
      logger.info(`钱包 [${this.index}] 开始BTC/USDC交易对操作`);

      // 1. 检查USDC余额，不足时先卖出STT获得USDC
      let usdcBalance = await this.#getTokenBalance('USDC');
      if (usdcBalance.formatted < 0.1) {
        logger.info(`钱包 [${this.index}] USDC余额不足 (${usdcBalance.formatted})，先卖出STT获得USDC`);

        // 卖出STT获得USDC
        const sellAmount = getRandomValue(0.01, 0.06);
        const sellAmountFormatted = parseFloat(sellAmount.toFixed(4));

        const sttBalance = await this.#getTokenBalance('STT');
        if (sttBalance.formatted < sellAmountFormatted) {
          logger.error(`钱包 [${this.index}] STT余额不足: ${sttBalance.formatted} < ${sellAmountFormatted}`);
          return false;
        }

        // 获取gas配置
        const gasConfig = await this.#getGasConfig();
        const gasLimit = await this.#estimateGasLimit(
          this.tradingContract,
          'marketSellETH',
          [
            this.TOKENS.USDC,
            this.TRADE_PARAMS.isMaker,
            this.TRADE_PARAMS.n,
            this.address,
            this.TRADE_PARAMS.slippageLimit
          ],
          { value: ethers.parseEther(sellAmountFormatted.toString()) }
        );

        const txOptions = {
          value: ethers.parseEther(sellAmountFormatted.toString()),
          gasLimit: gasLimit,
          ...gasConfig
        };

        // 执行STT卖出
        const sellTx = await this.tradingContract.marketSellETH(
          this.TOKENS.USDC,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit,
          txOptions
        );
        await sellTx.wait();

        logger.success(`钱包 [${this.index}] 卖出 ${sellAmountFormatted} STT 获得 USDC 成功`);

        // 等待余额更新
        await randomSleep(2, 3);

        // 重新获取USDC余额
        usdcBalance = await this.#getTokenBalance('USDC');
        if (usdcBalance.formatted < 0.1) {
          logger.warn(`钱包 [${this.index}] 卖出STT后USDC余额仍不足，跳过BTC/USDC交易`);
          return false;
        }
      }

      // 2. 用全部USDC买入BTC

      const buyAmountWei = ethers.parseUnits(usdcBalance.formatted.toString(), 6);

      // approve USDC
      const approveSuccess = await this.#ensureApproval('USDC', buyAmountWei);
      if (!approveSuccess) {
        return false;
      }

      // 获取买入gas配置
      const buyGasConfig = await this.#getGasConfig();
      const buyGasLimit = await this.#estimateGasLimit(
        this.tradingContract,
        'marketBuy',
        [
          this.TOKENS.BTC,
          this.TOKENS.USDC,
          buyAmountWei,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit
        ]
      );

      const buyTxOptions = {
        gasLimit: buyGasLimit,
        ...buyGasConfig
      };

      // 买入BTC
      logger.info(`钱包 [${this.index}] 用 ${usdcBalance.formatted} USDC 买入 BTC`);
      const buyTx = await this.tradingContract.marketBuy(
        this.TOKENS.BTC,
        this.TOKENS.USDC,
        buyAmountWei,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit,
        buyTxOptions
      );
      await buyTx.wait();

      // 等待一下让余额更新
      await randomSleep(2, 3);

      // 2. 卖出全部BTC
      const btcBalance = await this.#getTokenBalance('BTC');
      if (btcBalance.formatted <= 0) {
        logger.warn(`钱包 [${this.index}] BTC余额为0，无法卖出`);
        return false;
      }

      // approve BTC
      const btcApproveSuccess = await this.#ensureApproval('BTC', btcBalance.raw);
      if (!btcApproveSuccess) {
        return false;
      }

      // 获取卖出gas配置
      const sellGasConfig = await this.#getGasConfig();
      const sellGasLimit = await this.#estimateGasLimit(
        this.tradingContract,
        'marketSell',
        [
          this.TOKENS.BTC,
          this.TOKENS.USDC,
          btcBalance.raw,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit
        ]
      );

      const sellTxOptions = {
        gasLimit: sellGasLimit,
        ...sellGasConfig
      };

      // 卖出BTC
      logger.info(`钱包 [${this.index}] 卖出全部 ${btcBalance.formatted} BTC`);
      const sellTx = await this.tradingContract.marketSell(
        this.TOKENS.BTC,
        this.TOKENS.USDC,
        btcBalance.raw,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit,
        sellTxOptions
      );

      const receipt = await sellTx.wait();
      logger.success(`钱包 [${this.index}] BTC/USDC交易对完成，交易哈希: ${receipt.hash}`);
      return true;

    } catch (error) {
      logger.error(`钱包 [${this.index}] BTC/USDC交易失败: ${error.message}`);
      return false;
    }
  }

  // 将全部USDC买入STT
  async #buyAllSTTWithUSDC() {
    try {
      logger.info(`钱包 [${this.index}] 将全部USDC买入STT`);

      const usdcBalance = await this.#getTokenBalance('USDC');
      if (usdcBalance.formatted < 0.1) {
        logger.warn(`钱包 [${this.index}] USDC余额不足，无法买入STT`);
        return false;
      }

      const buyAmountWei = ethers.parseUnits(usdcBalance.formatted.toString(), 6);

      // approve USDC
      const approveSuccess = await this.#ensureApproval('USDC', buyAmountWei);
      if (!approveSuccess) {
        return false;
      }

      // 获取gas配置
      const gasConfig = await this.#getGasConfig();
      const gasLimit = await this.#estimateGasLimit(
        this.tradingContract,
        'marketBuy',
        [
          this.TOKENS.STT,
          this.TOKENS.USDC,
          buyAmountWei,
          this.TRADE_PARAMS.isMaker,
          this.TRADE_PARAMS.n,
          this.address,
          this.TRADE_PARAMS.slippageLimit
        ]
      );

      const txOptions = {
        gasLimit: gasLimit,
        ...gasConfig
      };

      // 买入STT
      const tx = await this.tradingContract.marketBuy(
        this.TOKENS.STT,
        this.TOKENS.USDC,
        buyAmountWei,
        this.TRADE_PARAMS.isMaker,
        this.TRADE_PARAMS.n,
        this.address,
        this.TRADE_PARAMS.slippageLimit,
        txOptions
      );

      const receipt = await tx.wait();
      logger.success(`钱包 [${this.index}] 用全部USDC买入STT成功，交易哈希: ${receipt.hash}`);
      return true;

    } catch (error) {
      logger.error(`钱包 [${this.index}] 用USDC买入STT失败: ${error.message}`);
      return false;
    }
  }

  // 执行随机交易
  async trade() {
    try {
      logger.info(`钱包 [${this.index}] ${this.address} 开始执行随机交易`);

      const tradingMethods = [
        { name: 'STT/USDC', method: this.#tradeSTTUSDC.bind(this) },
        { name: 'SOL/USDC', method: this.#tradeSOLUSDC.bind(this) },
        { name: 'BTC/USDC', method: this.#tradeBTCUSDC.bind(this) }
      ];

      let tradeCount = 0;
      const usedPairs = new Set();
      const maxRetries = 20;
      let retries = 0;

      // 重复随机调用交易方法，直到满足条件
      while (tradeCount < 10 && retries < maxRetries) {
        const randomMethod = tradingMethods[Math.floor(Math.random() * tradingMethods.length)];

        logger.info(`钱包 [${this.index}] 执行第 ${tradeCount + 1} 次交易: ${randomMethod.name}`);

        const success = await randomMethod.method();
        if (success) {
          tradeCount++;
          usedPairs.add(randomMethod.name);
          logger.success(`钱包 [${this.index}] 第 ${tradeCount} 次交易完成: ${randomMethod.name}`);
        } else {
          logger.warn(`钱包 [${this.index}] 交易失败，继续下一次: ${randomMethod.name}`);
        }

        retries++;

        // 交易间延迟
        if (tradeCount < 10) {
          const delay = getRandomValue(1, 3);
          logger.info(`钱包 [${this.index}] 等待 ${delay.toFixed(1)} 秒后执行下一次交易...`);
          await randomSleep(delay, delay + 0.5);
        }
      }

      // 检查是否满足条件
      if (tradeCount < 10) {
        logger.warn(`钱包 [${this.index}] 未完成10次交易，实际完成: ${tradeCount}`);
      }

      if (usedPairs.size < 2) {
        logger.warn(`钱包 [${this.index}] 未满足2个不同交易对要求，实际: ${usedPairs.size}`);
      }

      // 最后将全部USDC买入STT
      logger.info(`钱包 [${this.index}] 开始最终操作：将全部USDC买入STT`);
      await randomSleep(2, 3);
      const finalBuySuccess = await this.#buyAllSTTWithUSDC();

      logger.success(`钱包 [${this.index}] 随机交易完成`);
      logger.info(`钱包 [${this.index}] 交易统计: 完成 ${tradeCount}/10 次，涉及交易对: ${Array.from(usedPairs).join(', ')}`);

      return {
        success: true,
        msg: `随机交易完成 | 交易次数: ${tradeCount}/10 | 交易对: ${usedPairs.size} | 最终买入STT: ${finalBuySuccess}`,
        data: {
          tradeCount: tradeCount,
          usedPairs: Array.from(usedPairs),
          finalBuySuccess: finalBuySuccess
        }
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] 随机交易执行失败: ${error.message}`);
      return {
        success: false,
        msg: `随机交易执行失败: ${error.message}`,
        error: error.message
      };
    }
  }

  // 完整执行流程：登录验证 + 交易
  async execute() {
    try {
      logger.info(`钱包 [${this.index}] ${this.address} 开始完整执行流程`);

      // 1. 先执行登录和验证
      const loginResult = await this.loginAndVerify();
      if (!loginResult.success) {
        return {
          success: false,
          msg: `登录验证失败: ${loginResult.msg}`,
          loginError: loginResult.error
        };
      }

      logger.success(`钱包 [${this.index}] 登录验证成功，开始交易`);

      // 2. 执行交易
      const tradeResult = await this.trade();
      if (!tradeResult.success) {
        return {
          success: false,
          msg: `交易失败: ${tradeResult.msg}`,
          loginData: loginResult.verifyData,
          tradeError: tradeResult.error
        };
      }

      logger.success(`钱包 [${this.index}] ${this.address} 完整流程执行成功`);

      return {
        success: true,
        msg: `完整流程成功 ｜ 地址：${this.address} ｜ ${tradeResult.msg}`,
        loginData: loginResult.verifyData,
        tradeData: tradeResult.data,
        action: 'execute_complete'
      };

    } catch (error) {
      logger.error(`钱包 [${this.index}] 完整流程执行失败: ${error.message}`);
      return {
        success: false,
        msg: `完整流程执行失败: ${error.message}`,
        error: error.message
      };
    }
  }
}

export default StandardWeb3;
