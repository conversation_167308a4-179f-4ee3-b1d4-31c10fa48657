import axios from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import fakeUa from 'fake-useragent';
import { createTask, getTaskResult } from '../../base/tools/captcha.js';
import logger from '../../base/tools/logger.js';
import config from '../../config/config.json' assert { type: "json" };

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 配置参数
const MAX_RETRIES = 3;
const RETRY_DELAY = 3000;
const websiteUrl = "https://faucet.story.foundation/";
const websiteKey = "0x4AAAAAAAgnLZFPXbTlsiiE";
const taskType = "TurnstileTaskProxyless";
const pageAction = "undefined";
const service = 'YESCAPTCHA'; // 指定使用 YESCAPTCHA
// 获取验证码
async function getCaptcha(websiteUrl, websiteKey, taskType, pageAction) {
    try {
        logger.info(`开始获取验证码...`);

        // 创建验证码任务
        const { taskId, service } = await createTask(
            websiteUrl,
            websiteKey,
            taskType,
            pageAction,
            service
        );
        const result = await getTaskResult(taskId, service);

        if (result && result.solution && result.solution.token) {
            return result.solution.token;
        }
    } catch (error) {
        logger.error(`验证码获取错误: ${error.message}`);
        throw error;
    }
}

async function claimIP(address, retryCount = 0) {
    try {
        const userAgent = fakeUa();

        // 获取验证码
        logger.info(`开始为地址 ${address} 获取验证码`);
        const token = await getCaptcha(websiteUrl, websiteKey, taskType, pageAction);
        // 构造请求数据
        const response = await axios.post('https://faucet.story.foundation/',
            [{
                id: "",
                address: address,
                score: "$undefined",
                token: token
            }],
            {
                headers: {
                    'accept': 'text/x-component',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'content-type': 'text/plain;charset=UTF-8',
                    'origin': 'https://faucet.story.foundation',
                    'referer': 'https://faucet.story.foundation/',
                    'user-agent': userAgent
                },
                httpsAgent: new HttpsProxyAgent(config.proxy),
                timeout: 30000
            }
        );

        if (response.status === 200) {
            logger.success(`领取成功 | 地址: ${address}`);
            return true;
        }

        throw new Error(`请求失败，状态码: ${response.status}`);

    } catch (error) {
        if (retryCount < MAX_RETRIES) {
            logger.warn(`重试 ${address}: ${error.message}`);
            await sleep(RETRY_DELAY);
            return await claimIP(address, retryCount + 1);
        }
        logger.error(`领取失败 | 地址: ${address} | 错误: ${error.message}`);
        return false;
    }
}

// 主函数
async function main() {
    try {
        // 使用 path 模块处理文件路径
        const filePath = path.join(path.dirname(fileURLToPath(import.meta.url)), 'address.txt');

        // 检查文件是否存在
        try {
            await fs.access(filePath);
        } catch (error) {
            throw new Error(`地址文件不存在: ${filePath}`);
        }

        // 读取地址列表
        const addresses = (await fs.readFile(filePath, 'utf8'))
            .split('\n')
            .map(addr => addr.trim())
            .filter(addr => addr);

        if (addresses.length === 0) {
            throw new Error('地址文件为空');
        }

        logger.info(`总共找到 ${addresses.length} 个地址需要处理`);

        for (const address of addresses) {
            await claimIP(address);
            // 添加随机延迟
            await sleep(RETRY_DELAY + Math.random() * 2000);
        }

    } catch (error) {
        logger.error(`程序执行失败: ${error.message}`);
        process.exit(1);
    }
}

main();
