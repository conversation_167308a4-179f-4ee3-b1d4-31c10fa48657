import logger from "../../../base/tools/logger.js";
import CSV from "../../../base/tools/csv.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { sleep } from "../../../base/tools/common.js";
import {
  parseTOMLConfig,
  filterWalletsByIndex,
  getRandomValue,
} from "../../../base/tools/common.js";

import { DomainMinter } from "./domain.js";
import { MinerLegacy } from "./legacy.js";

const configPath = resolveFromModule(import.meta.url, "../config.toml");
const tomlConfig = parseTOMLConfig(configPath);
const config = tomlConfig["0g"];
const rpc = config.rpc;

const min_delay_seconds = config.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = config.MAX_DELAY_SECONDS || 30;
const task_interval_min_seconds = config.TASK_INTERVAL_MIN_SECONDS || 10;
const task_interval_max_seconds = config.TASK_INTERVAL_MAX_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "../wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  const indexArg = args[0]; // 获取钱包索引参数
  // const indexArg = "1"; // 获取钱包索引参数

  const walletList = await CSV.read(getCSVFilePath());
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);
  logger.info(`找到 ${filteredWallets.length} 个钱包`);
  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  // 定义可用的任务列表
  const tasks = [
    {
      name: "Mint Domain",
      action: async (wallet) => {
        const domain = new DomainMinter(rpc, wallet.private_key, wallet.index);
        await domain.mintDomain();
      },
    },
    {
      name: "Mint NFT",
      action: async (wallet) => {
        const legacy = new MinerLegacy(rpc);
        await legacy.mint(wallet.private_key, wallet.index);
      },
    },
  ];

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];
    const private_key = wallet.private_key;
    const index = wallet.index;
    const address = wallet.address;

    if (!private_key || private_key.trim() === "") {
      logger.warn(`钱包序号 ${index} 的私钥为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    logger.info(`开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: ${index} ${address}`);

    for (const task of tasks) {
      try {
        logger.info(`执行任务: ${task.name}`);
        await task.action(wallet);
      } catch (error) {
        logger.error(`执行任务 ${task.name} 失败:`, error);
      }
      const delay = getRandomValue(task_interval_min_seconds, task_interval_max_seconds);
      await sleep(delay);
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    logger.info(`任务完成: ${index} ${address}, 耗时 ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }
}

// 执行任务
// node example/monad/task.js 1-10
main();
