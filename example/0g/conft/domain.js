import { ethers } from "ethers";
import logger from "../../../base/tools/logger.js";
import { getRandomDomain } from "../../../base/tools/domain.js";

const contractAddress = "******************************************";
const providerUrl = "https://evmrpc-testnet.0g.ai"; // 替换为你的 RPC URL

export class DomainMinter {
  constructor(rpc, privateKey, index) {
    this.rpc = rpc || providerUrl;
    this.provider = new ethers.JsonRpcProvider(this.rpc);
    this.wallet = new ethers.Wallet(privateKey, this.provider);
    this.contract = new ethers.Contract(contractAddress, this.getAbi(), this.wallet);
    this.index = index;
  }

  // 定义合约 ABI
  getAbi() {
    return [
      {
        name: "isVacant",
        type: "function",
        inputs: [
          {
            name: "domainName",
            type: "string",
            internalType: "string",
          },
        ],
        outputs: [
          {
            name: "",
            type: "bool",
            internalType: "bool",
          },
        ],
        stateMutability: "view",
      },
      {
        name: "lease",
        type: "function",
        inputs: [
          {
            name: "domainName",
            type: "string",
            internalType: "string",
          },
          {
            name: "yearAmount",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "isPrimary",
            type: "bool",
            internalType: "bool",
          },
        ],
        outputs: [
          {
            name: "",
            type: "uint256",
            internalType: "uint256",
          },
        ],
        stateMutability: "payable",
      },
      {
        name: "balanceOf",
        type: "function",
        inputs: [
          {
            name: "owner",
            type: "address",
            internalType: "address",
          },
        ],
        outputs: [
          {
            name: "",
            type: "uint256",
            internalType: "uint256",
          },
        ],
        stateMutability: "view",
      },
    ];
  }

  // 检查钱包是否已经拥有域名
  async hasMintedDomain() {
    try {
      const balance = await this.contract.balanceOf(this.wallet.address);
      if (balance > 0) {
        logger.info(`钱包 ${this.index} 已拥有 ${balance} 个域名`);
        return true;
      }
      return false;
    } catch (error) {
      logger.error(`查询钱包 ${this.index} 域名数量时出错: ${error.message}`);
      return false;
    }
  }

  async getAvailableDomain() {
    // 检查域名是否可用
    let domainName = getRandomDomain();
    const isVacant = await this.isVacant(domainName);
    if (!isVacant) {
      logger.warn(`域名 ${domainName} 已被注册，无法 mint。`);
      for (let i = 0; i < 10; i++) {
        // 保留原始名称，添加随机数
        domainName = `${domainName}${i}`;
        isVacant = await this.isVacant(domainName);

        if (isVacant) {
          logger.success(`域名可用|${domainName}.0g`);
          return { name: domainName, isAvailable: true };
        }

        logger.info(`域名已经被注册|${domainName}.0g`);
      }
    }

    return domainName;
  }

  // 检查域名是否可用
  async isVacant(domainName) {
    const isVacant = await this.contract.isVacant(domainName);
    return isVacant;
  }

  // 检查域名是否已注册并进行 mint
  async mintDomain(domainName, yearAmount = 1, isPrimary = true) {
    try {
      const hasMintedDomain = await this.hasMintedDomain();
      if (hasMintedDomain) {
        logger.warn(`钱包 ${this.index} 已拥有域名，跳过 mint。`);
        return {
          success: true,
          code: "SUCCESS",
          message: `钱包 ${this.index} 已拥有域名，跳过 mint。`,
        };
      }

      // 检查域名是否可用
      if (!domainName) {
        domainName = await this.getAvailableDomain();
      }

      logger.debug(`${this.index} 开始 mint 域名 ${domainName}.0g`);

      const isVacant = await this.isVacant(domainName);

      if (!isVacant) {
        logger.warn(`域名 ${domainName} 已被注册，无法 mint。`);
        return {
          success: false,
          code: "DOMAIN_ALREADY_REGISTERED",
          message: `域名 ${domainName} 已被注册，无法 mint。`,
          data: {
            domainName,
          },
        };
      }

      // 检查钱包余额
      const balance = await this.provider.getBalance(this.wallet.address);
      if (balance <= 0) {
        logger.warn("钱包余额不足，无法 mint 域名。");
        return {
          success: false,
          code: "INSUFFICIENT_FUNDS",
          message: `钱包 ${this.index} 余额不足，无法 mint 域名。`,
          data: {
            balance: balance.toString(),
          },
        };
      }

      // 如果域名可用，进行 mint 操作
      const tx = await this.contract.lease(domainName, yearAmount, isPrimary, {
        value: 0n,
      });

      logger.debug(`交易发送中... ${tx.hash}`);

      // 等待交易确认
      try {
        const receipt = await tx.wait();
        if (receipt.status === 0) {
          throw new Error("交易失败");
        }
        logger.success(`域名 ${domainName} mint 成功。tx: ${tx.hash}`);
        return {
          success: true,
          code: "SUCCESS",
          message: `域名 ${domainName} mint 成功。`,
          data: {
            txHash: tx.hash,
          },
        };
      } catch (error) {
        logger.error(`交易失败: ${error}`);
        return {
          success: false,
          code: "ERROR",
          message: `交易失败: ${error}`,
        };
      }

    } catch (error) {
      logger.error(`操作失败: ${error}`);
      return {
        success: false,
        code: "ERROR",
        message: `操作失败: ${error}`,
      };
    }
  }
}

// const registerDomain = async (rpc, index, privateKey) => {
//   const domainMinter = new DomainMinter(rpc, privateKey, index);
//   return await domainMinter.mintDomain();
// };

// // 注册域名
// registerDomain(
//   "https://evmrpc-testnet.0g.ai",
//   "index",
//   "",
// );
