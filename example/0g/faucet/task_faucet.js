import logger from "../../../base/tools/logger.js";
import CSV from "../../../base/tools/csv.js";
import { resolveFromModule } from "../../../base/tools/path.js";
import { sleep } from "../../../base/tools/common.js";
import { parseTOMLConfig, filterWalletsByIndex, getRandomValue } from "../../../base/tools/common.js";
import Faucet from "./faucet.js";
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const tomlConfig = parseTOMLConfig(configPath);
const config = tomlConfig["0g"];
const rpc = config.rpc;

const min_delay_seconds = tomlConfig.MIN_DELAY_SECONDS || 10;
const max_delay_seconds = tomlConfig.MAX_DELAY_SECONDS || 30;

function getCSVFilePath() {
  return resolveFromModule(import.meta.url, "./wallet.csv");
}

async function main() {
  const args = process.argv.slice(2);
  // const indexArg = args[0]; // 获取钱包索引参数
  const indexArg = "1-10"; // 获取钱包索引参数

  const walletList = await CSV.read(getCSVFilePath());

  logger.info(`读取钱包列表: ${walletList.length} 个`);
  const filteredWallets = filterWalletsByIndex(indexArg, walletList);

  // 随机打乱钱包顺序
  const shuffledWallets = [...filteredWallets].sort(() => Math.random() - 0.5);

  for (let j = 0; j < shuffledWallets.length; j++) {
    const wallet = shuffledWallets[j];

    const index = wallet.index;
    const address = wallet.address;

    if (!address || address.trim() === "") {
      logger.warn(`钱包序号 ${index} 的地址为空，跳过处理`);
      continue;
    }

    const startTime = Date.now();
    logger.info(`开始执行第 ${j + 1}/${shuffledWallets.length} 个任务: ${index} ${address}`);

    const faucet = new Faucet();
    await faucet.requestFaucet(0, address);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    logger.info(`任务完成: ${index} ${address}, 耗时 ${duration} 秒`);

    // 如果是最后一个任务，则不延迟
    if (j === shuffledWallets.length - 1) {
      break;
    }

    // 任务间随机延迟 min_delay_seconds-max_delay_seconds 秒
    const delay = getRandomValue(min_delay_seconds, max_delay_seconds);
    await sleep(delay);
  }
}

// 执行任务
// node example/monad/task.js 1-10
main();
