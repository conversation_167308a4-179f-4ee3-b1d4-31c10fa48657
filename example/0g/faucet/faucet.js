import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";
import { ethers, parseEther, formatEther } from "ethers";
import logger from "../../../base/tools/logger.js";
import { createTask, getTaskResult } from "../../../base/tools/captcha.js";
import { getHeaders } from "../../../base/tools/fake_useragent.js";
import { parseTOMLConfig } from "../../../base/tools/common.js";
import { resolveFromModule } from "../../../base/tools/path.js";

const configPath = resolveFromModule(import.meta.url, "./config.toml");
const tomlConfig = parseTOMLConfig(configPath);
const config = tomlConfig["0g"];

// 常量配置
const FAUCET_API_URL = "https://992dkn4ph6.execute-api.us-west-1.amazonaws.com/";
const MAX_RETRIES = tomlConfig.MAX_RETRIES || 3; // 最大重试次数
const RETRY_DELAY = tomlConfig.RETRY_DELAY || 2000; // 基础重试延迟时间(毫秒)
const MIN_BALANCE = parseEther("0.1"); // 最小余额阈值
const RPC_URL = config.rpc;
const YESCAPTCHA_KEY = tomlConfig.YESCAPTCHA_KEY;
const PROXY_URL = tomlConfig.PROXY_URL;

export default class Faucet {
  constructor(proxyUrl) {
    this.provider = new ethers.JsonRpcProvider(RPC_URL);
    this.captchaKey = YESCAPTCHA_KEY;
    this.websiteKey = "1230eb62-f50c-4da4-a736-da5c3c342e8e";
    this.taskType = "HCaptchaTaskProxyless";
    this.websiteUrl = "https://hub.0g.ai/faucet";
    this.proxyUrl = proxyUrl || PROXY_URL;
  }

  /**
   * 获取HCaptcha验证码token
   * @returns {Promise<string>} 验证码token
   */
  async getHCaptchaToken() {
    try {
      // 验证 yescaptchaKEY 配置
      if (!this.captchaKey) {
        throw new Error("未配置 yescaptchaKEY");
      }

      const { taskId, service } = await createTask(
        this.websiteUrl,
        this.websiteKey,
        this.taskType,
        "undefined",
        "YESCAPTCHA",
      );

      if (!taskId) {
        throw new Error("创建验证码任务失败");
      }

      const result = await getTaskResult(taskId, service);
      if (!result || !result.solution || !result.solution.gRecaptchaResponse) {
        throw new Error("获取验证码结果失败");
      }

      return result.solution.gRecaptchaResponse;
    } catch (error) {
      throw new Error(`获取验证码失败: ${error.message}`);
    }
  }

  async getBalance(address) {
    const balance = await this.provider.getBalance(address);
    return balance;
  }

  /**
   * 发送水龙头请求
   */
  async requestFaucet(index, address) {
    let retryCount = 0;
    const walletInfo = { index: index, wallet: address };

    const balance = await this.getBalance(address);
    if (balance >= MIN_BALANCE) {
      logger.success(address, `余额充足 (${formatEther(balance)} AOGI)，跳过领取`);
      return;
    }

    while (retryCount < MAX_RETRIES) {
      try {
        logger.info(walletInfo, "开始获取验证码...");
        const hcaptchaToken = await this.getHCaptchaToken();

        logger.debug(walletInfo, `获取到验证码token: ${hcaptchaToken.substring(0, 30)}...`);

        const headers = {
          accept: "application/json, text/plain, */*",
          "accept-language": "zh-CN,zh;q=0.9",
          "content-type": "application/json",
          origin: "https://hub.0g.ai",
          referer: "https://hub.0g.ai/",
          "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        };

        const options = {
          headers,
          timeout: 60000,
          validateStatus: function (status) {
            return (status >= 200 && status < 300) || status === 400;
          },
        };

        if (this.proxyUrl) {
          options.proxy = false;
          options.httpsAgent = new HttpsProxyAgent(this.proxyUrl);
          logger.debug(walletInfo, `使用代理: ${this.proxyUrl}`);
        }

        const requestData = {
          address: address,
          hcaptchaToken: hcaptchaToken,
        };

        logger.debug(
          walletInfo,
          `发送请求到: ${FAUCET_API_URL}, 数据: ${JSON.stringify(requestData)}`,
        );

        const response = await axios.post(FAUCET_API_URL, requestData, options);
        logger.debug(
          walletInfo,
          `收到响应: status=${response.status}, data=${JSON.stringify(response.data)}`,
        );

        if (response.status === 400) {
          if (response.data.message?.includes("24 hours")) {
            logger.warn(walletInfo, "需要等待24小时后再次请求");
            // 返回特殊结果而不是抛出异常
            return {
              message: "24小时内重复请求，需要等待",
              needWait: true,
            };
          }
          throw new Error(`请求失败: ${response.data.message || "未知错误"}`);
        }

        if (!response.data || !response.data.message) {
          throw new Error("响应数据格式错误");
        }
        return response.data;
      } catch (error) {
        retryCount++;

        if (retryCount >= MAX_RETRIES) {
          throw new Error(`达到最大重试次数: ${error.message}`);
        }

        const delay = RETRY_DELAY * retryCount;
        logger.warn(walletInfo, `等待${delay}ms后重试...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }
}
