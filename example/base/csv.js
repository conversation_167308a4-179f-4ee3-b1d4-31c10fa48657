import CSV from '../../base/tools/csv.js';

async function example() {
  try {
    // 1. 写入数据
    const data = [
      { id: '1', name: '张三', age: 20, city: '北京' },
      { id: '2', name: '李四', age: 25, city: '上海' },
      { id: '3', name: '王五', age: 30, city: '广州' }
    ];
    await CSV.write('users.csv', data);

    // 2. 读取数据
    const readData = await CSV.read('users.csv');
    console.log('原始数据:', readData);

    // 3. 单条更新
    const updateResult = await CSV.updateByField(
      'users.csv',
      'id',
      '1',
      { age: 21, city: '深圳' }
    );
    console.log('单条更新结果:', updateResult);

    // 4. 批量更新
    const batchResult = await CSV.batchUpdate(
      'users.csv',
      'id',
      [
        { keyValue: '2', newData: { age: 26, city: '杭州' } },
        { keyValue: '3', newData: { age: 31, city: '成都' } }
      ]
    );
    console.log('批量更新结果:', batchResult);

    // 5. 追加数据
    const newData = [
      { id: '4', name: '赵六', age: 35, city: '武汉' }
    ];
    await CSV.append('users.csv', newData);

    // 6. 查看最终数据
    const finalData = await CSV.read('users.csv');
    console.log('最终数据:', finalData);

  } catch (error) {
    console.error('发生错误:', error);
  }
}

example();
