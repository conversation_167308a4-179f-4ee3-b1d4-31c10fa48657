import fs from 'fs';
import { ethers } from 'ethers';
import axios from 'axios';
import TOML from '@iarna/toml';
import fakeUa from "fake-useragent";
import { HttpsProxyAgent } from "https-proxy-agent";

import logger from '../../base/tools/logger.js';
import { readExcelData } from '../../base/tools/excel.js';
import { resolveFromModule } from '../../base/tools/path.js';
import ProxyHelper from '../../base/tools/proxy.js';



const configPath = resolveFromModule(import.meta.url, './config.toml');
const config = TOML.parse(fs.readFileSync(configPath, 'utf-8'))

const MAX_CONCURRENT = config.MAX_CONCURRENT;  // 最大并发数
const RETRY_DELAY = config.RETRY_DELAY;  // 重试延迟（毫秒）
const RANDOM_DELAY = config.RANDOM_DELAY; // 随机延迟最大值（毫秒）
const MIN_AMOUNT = config.MIN_AMOUNT; // 最小金额
const MAX_AMOUNT = config.MAX_AMOUNT; // 最大金额
const ALL_AMOUNT = config.ALL_AMOUNT; // 是否全部跨链，如果是，则不随机金额


// 生成随机金额
function getRandomAmount() {
    return (Math.random() * (MAX_AMOUNT - MIN_AMOUNT) + MIN_AMOUNT).toFixed(4);
}

// 线程池类
class ThreadPool {
    constructor(maxThreads) {
        this.maxThreads = maxThreads;
        this.running = new Set();
        this.queue = [];
    }

    async add(task) {
        if (this.running.size >= this.maxThreads) {
            await Promise.race([...this.running]);
        }

        const promise = (async () => {
            try {
                const result = await task();
                this.running.delete(promise);
                this.processQueue();
                return result;
            } catch (error) {
                this.running.delete(promise);
                this.processQueue();
                throw error;
            }
        })();

        this.running.add(promise);
        return promise;
    }

    processQueue() {
        if (this.queue.length > 0 && this.running.size < this.maxThreads) {
            const next = this.queue.shift();
            this.add(next);
        }
    }
}

function getTimeString() {
    return new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
    });
}


/**
 * 获取跨链交易数据
 * @param {*} userAddress 用户地址
 * @param {*} solAddress 目标地址
 * @param {*} proxyUrl 代理地址
 * @param {*} amount 金额
 * @param {*} chainName 链名称
 * @returns
 */
async function getTransactionData(userAddress, solAddress, proxyUrl, amount, chainName) {
    const chainConfig = config[chainName];
    if (!chainConfig) {
        throw new Error(`不支持的链名称: ${chainName}`);
    }

    const amountInWei = ethers.parseUnits(amount, 'ether').toString();

    const headers = {
        'User-Agent': fakeUa(),
        'Content-Type': 'application/json'
    };

    let httpsAgent = null;
    if (proxyUrl) {
        const proxyHelper = new ProxyHelper(proxyUrl);
        httpsAgent = await proxyHelper.getAvailableProxy();
        if (httpsAgent) {
            logger.success(`[${getTimeString()}] 代理设置成功 ${httpsAgent.host}`);
        } else {
            logger.warn(`[${getTimeString()}] 代理设置失败`);
        }
    }

    const response = await axios.post('https://api.relay.link/quote', {
        user: userAddress,
        originChainId: chainConfig.chainId,
        destinationChainId: 9286185,
        amount: amountInWei,
        destinationCurrency: "********************************",
        originCurrency: "******************************************",
        recipient: solAddress,
        referrer: "relay.link/swap",
        tradeType: "EXACT_INPUT",
        useExternalLiquidity: false,
        useDepositAddress: false
    }, {
        headers: headers,
        httpsAgent: httpsAgent,
        timeout: 30000,
    });
    return {
        data: response.data.steps[0].items[0].data.data,
        amount: amount
    };
}


/**
 * 获取跨链金额
 * @param {*} userAddress 用户地址
 * @param {*} chainName 链名称
 * @param {*} index 索引
 * @returns
 */
async function getBridgeAmount(userAddress, chainName, index) {

    const chainConfig = config[chainName];
    if (!chainConfig) {
        throw new Error(`不支持的链名称: ${chainName}`);
    }

    const provider = new ethers.JsonRpcProvider(chainConfig.rpc);
    const balance = await provider.getBalance(userAddress);

    // 估算 gas 费用
    const gasLimit = 500000n;
    const maxFeePerGas = ethers.parseUnits('0.1', 'gwei');
    const estimatedGasCost = gasLimit * maxFeePerGas;

    // 计算可用余额（总余额减去预留的 gas 费用）
    const availableBalance = balance - estimatedGasCost;

    // 如果可用余额小于0，返回false
    if (availableBalance <= 0n) {
        logger.error(`[${getTimeString()}] 账户: ${index} 余额不足支付 gas | 地址: ${userAddress} | 余额: ${ethers.formatEther(balance)} ETH`);
        return false;
    }

    if (!ALL_AMOUNT) {
        const randomAmount = getRandomAmount();
        const randomAmountWei = ethers.parseUnits(randomAmount, 'ether');
        if (randomAmountWei > availableBalance) {
            throw new Error(`账户: ${index} ${chainName} 可用余额不足${randomAmount} ETH, 可用余额: ${ethers.formatEther(availableBalance)} ETH`);
        }
        return randomAmount;
    }

    // 使用可用余额的99%作为转账金额，留出一些余量
    const amount = ethers.formatEther(availableBalance * 99n / 100n);
    logger.info(`[${getTimeString()}] 账户: ${index} ${chainName} 开始获取交易数据 | 地址: ${userAddress} | 转账金额: ${amount} ETH`);
    return amount;

}


/**
 * 获取当前网络Gas价格信息
 * @param {ethers.Provider} provider
 * @returns {Promise<{baseFee: string, maxFeePerGas: string, maxPriorityFeePerGas: string}>}
 */
const getGasPrice = async (provider, multiplier = 1.1) => {
    try {
        // 获取最新区块
        const block = await provider.getBlock('latest');
        // 获取基础费用
        const baseFee = block.baseFeePerGas;

        // 小费
        const maxPriorityFeePerGas = ethers.parseUnits('0.1', 'gwei');
        // 建议的maxFeePerGas（基础费用 * multiplier + 小费）
        const multiplierBigInt = BigInt(Math.floor(multiplier * 1000)) / 1000n;
        const maxFeePerGas = (baseFee * multiplierBigInt) + maxPriorityFeePerGas;

        return {
            baseFee: ethers.formatUnits(baseFee, 'gwei'),
            maxPriorityFeePerGas: ethers.formatUnits(maxPriorityFeePerGas, 'gwei'),
            maxFeePerGas: ethers.formatUnits(maxFeePerGas, 'gwei')
        };
    } catch (error) {
        console.error('获取Gas价格失败:', error.message);
        throw error;
    }
};


/**
 * 诊断交易失败原因
 * @param {string} txHash - 交易哈希
 * @param {ethers.Provider} provider - Provider实例
 */
const diagnoseTxFailure = async (txHash, provider) => {
    try {
        console.log('\n=== 交易失败诊断 ===');

        // 1. 检查交易状态
        const tx = await provider.getTransaction(txHash);
        if (!tx) {
            console.log('交易状态: 未找到交易，可能未被节点接收');
            return;
        }

        // 2. 获取当前网络状态和费用数据
        const [currentBlock, feeData] = await Promise.all([
            provider.getBlock('latest'),
            provider.getFeeData()
        ]);

        // 3. 详细的交易信息
        console.log('\n=== 交易详情 ===');
        console.log(`交易哈希: ${txHash}`);
        console.log(`发送地址: ${tx.from}`);
        console.log(`接收地址: ${tx.to}`);
        console.log(`交易金额: ${ethers.formatEther(tx.value)} ETH`);
        console.log(`Gas限制: ${tx.gasLimit.toString()}`);
        console.log(`Nonce: ${tx.nonce}`);

        // 4. Gas费用对比
        console.log('\n=== Gas费用对比 ===');
        console.log('当前网络费用:');
        console.log(`- 基础费用: ${ethers.formatUnits(feeData.gasPrice || '0', 'gwei')} Gwei`);
        console.log(`- 建议最大费用: ${ethers.formatUnits(feeData.maxFeePerGas || '0', 'gwei')} Gwei`);
        console.log(`- 建议优先费用: ${ethers.formatUnits(feeData.maxPriorityFeePerGas || '0', 'gwei')} Gwei`);

        console.log('\n交易设置费用:');
        console.log(`- 最大费用: ${ethers.formatUnits(tx.maxFeePerGas || tx.gasPrice, 'gwei')} Gwei`);
        if (tx.maxPriorityFeePerGas) {
            console.log(`- 优先费用: ${ethers.formatUnits(tx.maxPriorityFeePerGas, 'gwei')} Gwei`);
        }

        // 5. 余额检查
        const balance = await provider.getBalance(tx.from);
        const estimatedGasCost = tx.gasLimit * (tx.maxFeePerGas || tx.gasPrice);
        const totalCost = tx.value + estimatedGasCost;

        console.log('\n=== 余额信息 ===');
        console.log(`当前余额: ${ethers.formatEther(balance)} ETH`);
        console.log(`预估Gas成本: ${ethers.formatEther(estimatedGasCost)} ETH`);
        console.log(`总需花费: ${ethers.formatEther(totalCost)} ETH`);

        // 6. 问题分析
        console.log('\n=== 可能原因分析 ===');
        if (tx.maxFeePerGas && tx.maxFeePerGas < feeData.maxFeePerGas) {
            console.log('- Gas价格过低：交易设置的Gas价格低于当前网络建议价格');
            console.log(`  建议将maxFeePerGas提高到至少 ${ethers.formatUnits(feeData.maxFeePerGas, 'gwei')} Gwei`);
        }

        if (balance < totalCost) {
            console.log('- 余额不足：账户余额不足以支付交易总成本');
            console.log(`  还需要额外 ${ethers.formatEther(totalCost - balance)} ETH`);
        }

        // 7. 检查Nonce
        const currentNonce = await provider.getTransactionCount(tx.from);
        if (tx.nonce < currentNonce) {
            console.log('- Nonce过低：交易的Nonce低于当前账户Nonce');
            console.log(`  交易Nonce: ${tx.nonce}, 当前账户Nonce: ${currentNonce}`);
        }

        // 8. 网络状态
        console.log('\n=== 网络状态 ===');
        console.log(`当前区块: ${currentBlock.number}`);
        console.log(`区块时间: ${new Date(currentBlock.timestamp * 1000).toLocaleString()}`);

    } catch (error) {
        console.error('诊断过程出错:', error);
    }
};


/**
 * 跨链到 eclipse
 * 目前是把 from 链的 所有 ETH 跨链到 eclipse
 * @param {*} address 地址
 * @param {*} privateKey 私钥
 * @param {*} index 索引
 * @param {*} proxyUrl 代理地址
 * @param {*} chainName 链名称
 * @returns
 */
async function bridgeToEclipse(address, privateKey, index, proxyUrl, chainName) {
    try {
        const chainConfig = config[chainName]
        if (!chainConfig) {
            throw new Error(`不支持的链名称: ${chainName}`);
        }

        const provider = new ethers.JsonRpcProvider(chainConfig.rpc);
        const gasPrice = await getGasPrice(provider);

        const wallet = new ethers.Wallet(privateKey, provider);
        const userAddress = wallet.address;

        const amount = await getBridgeAmount(userAddress, chainName, index);
        if (!amount) {
            throw new Error(`余额不足支付 gas | 地址: ${userAddress} | 余额: ${ethers.formatEther(balance)} ETH`);
        }

        logger.info(`[${getTimeString()}] 账户: ${index} 开始获取交易数据 | 地址: ${userAddress} | 转账金额: ${amount} ETH`);
        const { data: transactionData } = await getTransactionData(userAddress, address, proxyUrl, amount, chainName);

        console.log(transactionData);

        const maxFeePerGas = ethers.parseUnits(gasPrice.maxFeePerGas, 'gwei');
        const maxPriorityFeePerGas = ethers.parseUnits(gasPrice.maxPriorityFeePerGas, 'gwei');

        const estimatedGas = await provider.estimateGas({
            to: chainConfig.relayAddress,
            value: ethers.parseUnits(amount, 'ether'),
            data: transactionData
        });
        const gasLimit = estimatedGas * 150n / 100n;

        console.log(`预计gas: ${estimatedGas} | 建议gas: ${gasLimit}`);

        const tx = await wallet.sendTransaction({
            to: chainConfig.relayAddress,
            value: ethers.parseUnits(amount, 'ether'),
            data: transactionData,
            gasLimit: gasLimit,
            maxFeePerGas: maxFeePerGas,
            maxPriorityFeePerGas: maxPriorityFeePerGas
        });

        try {
            console.log('开始等待交易确认');
            const receipt = await tx.wait(1, 120000);
            if (receipt.status === 1) {
                logger.success(`[${getTimeString()}] 账户: ${index} 跨链成功 | 金额: ${amount} ETH | 交易哈希: ${tx.hash}`);
                return true;
            }
        } catch (error) {
            if (error.message.includes('timeout')) {
                await diagnoseTxFailure(tx.hash, provider);
            }
            throw error;
        }

        throw new Error('交易失败');
    } catch (error) {
        logger.error(`[${getTimeString()}] 账户: ${index} 跨链失败: ${error.message}`);
        throw error;
    }
}

async function getAddressList() {
    const excelFile = resolveFromModule(import.meta.url, './wallet.xlsx');
    return readExcelData(excelFile, {
        index: 'index',
        address: 'evm_address',
        privateKey: 'evm_private_key',
        solAddress: 'sol_address',
        proxyUrl: 'proxy_url'
    }, ['solAddress', 'privateKey']);
}

async function main() {
    try {
        // 添加命令行参数来指定链
        const chainName = process.argv[2] || 'scroll';

        const chainConfig = config[chainName]
        if (!chainConfig) {
            console.log(`找不到 ${chainName} 的配置信息`)
            process.exit(1)
        }

        logger.info(`[${getTimeString()}] 开始执行 从 ${chainName} 跨链 到 eclipse`);

        const addressList = await getAddressList();
        logger.info(`总共找到 ${addressList.length} 个地址需要处理`);

        const pool = new ThreadPool(MAX_CONCURRENT);
        let successCount = 0;
        let failCount = 0;

        const tasks = addressList.map(item => async () => {
            try {
                await bridgeToEclipse(item.solAddress, item.privateKey, item.index, item.proxyUrl, chainName);
                successCount++;
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY + Math.random() * RANDOM_DELAY));
            } catch (error) {
                failCount++;
                logger.error(`处理地址 ${item.address} 失败: ${error.message}`);
            }
        });

        await Promise.all(tasks.map(task => pool.add(task)));

        logger.info('\n=== 统计信息 ===');
        logger.info(`总地址数: ${addressList.length}`);
        logger.success(`成功: ${successCount}`);
        logger.error(`失败: ${failCount}`);
    } catch (error) {
        logger.error(`程序执行失败: ${error.message}`);
        process.exit(1);
    }
}

main();

