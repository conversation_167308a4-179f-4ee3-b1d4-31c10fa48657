# 最大并发数
MAX_CONCURRENT = 1

# 重试延迟时间（毫秒）
RETRY_DELAY = 3000

# 随机延迟范围（毫秒）
RANDOM_DELAY = 2000

# 最小数量
MIN_AMOUNT = 0.003

# 最大数量
MAX_AMOUNT = 0.008

# 是否使用全部数量, 如果是，则不随机金额
ALL_AMOUNT = false



# 跨链配置
[arbitrum]
chainId = 42161
rpc = "https://rpc.ankr.com/arbitrum"

[scroll]
chainId = 534352
rpc = "https://rpc.ankr.com/scroll"

[base]
chainId = 8453
rpc = "https://rpc.ankr.com/base"

[optimism]
chainId = 10
rpc = "https://rpc.ankr.com/optimism"

[linea]
chainId = 59144
rpc = "https://rpc.ankr.com/linea"

