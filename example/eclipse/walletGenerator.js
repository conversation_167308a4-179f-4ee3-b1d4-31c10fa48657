
import SolanaWallet from '../../base/sol/wallet.js';
import { resolveFromModule } from '../../base/tools/path.js';
import readline from "readline";

const main = async () => {
    try {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
          });

        const question = (prompt) => {
            return new Promise((resolve) => {
              rl.question(prompt, resolve);
            });
          };

        const number = await question("请输入要创建的钱包数量 (默认100): ");
        const walletCount = number.trim() === "" ? 100 : parseInt(number);
        if (isNaN(walletCount) || walletCount <= 0) {
            console.log("错误：请输入有效的数字！");
            return;
        }

        const solanaWallet = new SolanaWallet();
        const path = resolveFromModule(import.meta.url, './wallet.csv');
        await solanaWallet.createWallets(walletCount, path);

        console.log('钱包已成功写入文件:', path);
        rl.close();

    } catch (error) {
        console.error('错误:', error.message);
    }
};

main();
