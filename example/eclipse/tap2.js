// 配置项
const minStopMinutes = 20; // 单次执行最小停止时间，单位：分钟
const maxStopMinutes = 60; // 单次执行最大停止时间，单位：分钟
const minExecutions = 5; // 最小执行次数
const maxExecutions = 20; // 最大执行次数
const minInterval = 10; // 执行间隔最小时间，单位：分钟
const maxInterval = 30; // 执行间隔最大时间，单位：分钟
const maxClicks = 10000; // 最大点击次数
const minDelay = 100; // 最小点击延迟（毫秒）
const maxDelay = 1000; // 最大点击延迟（毫秒）

// 全局变量
let currentExecution = 0;
const totalExecutions = Math.floor(minExecutions + Math.random() * (maxExecutions - minExecutions + 1));
let stopTime; // 停止时间戳
let clickCount = 0; // 记录当前点击次数
let isRunning = true; // 全局开关：通过修改该变量可以停止程序
let nextExecutionTimer; // 下一轮执行的计时器
let clickTimer; // 点击的计时器

// 缓存窗口尺寸
let cachedWidth, cachedHeight;

// 格式化时间为 YYYY-MM-DD HH:mm:ss
function getCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 定义固定的点击区域（屏幕中间偏右的100x100区域）
function getClickArea() {
    // 只在需要时更新缓存
    if (!cachedWidth || !cachedHeight) {
        cachedWidth = window.innerWidth || document.documentElement.clientWidth;
        cachedHeight = window.innerHeight || document.documentElement.clientHeight;
    }

    const centerX = cachedWidth * (0.4 + Math.random() * 0.1);
    const centerY = cachedHeight * (0.55 + Math.random() * 0.1);

    return {
        minX: centerX - 200,
        maxX: centerX + 200,
        minY: centerY - 100,
        maxY: centerY + 100,
    };
}

// 记录上一次点击的X坐标
let lastX = null;
let movingRight = true;

function randomCenterClick() {
    try {
        if (!isRunning) {
            console.log("程序已手动停止。");
            return;
        }

        const currentTime = Date.now();
        if (currentTime >= stopTime || clickCount >= maxClicks) {
            console.log(`第 ${currentExecution} 轮执行完成。结束时间：${getCurrentTime()}`);
            isRunning = false;
            return;
        }

        // 计算下一次点击的时间
        const baseDelay = minDelay + Math.random() * (maxDelay - minDelay);
        const shouldPause = Math.random() < 0.05; // 5%的概率增加额外暂停
        const extraDelay = shouldPause ? (2000 + Math.random() * 3000) : 0;
        const nextClickTime = currentTime + baseDelay + extraDelay;

        const area = getClickArea();
        let x, y;

        // 计算点击位置
        if (lastX === null) {
            x = Math.random() * (area.maxX - area.minX) + area.minX;
        } else {
            const maxMove = 50;
            const moveX = (Math.random() - 0.5) * maxMove * 2;
            x = Math.max(area.minX, Math.min(area.maxX, lastX + moveX));
        }
        lastX = x;
        y = Math.random() * (area.maxY - area.minY) + area.minY;

        // 执行点击
        const clickEvent = new MouseEvent("click", {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y
        });
        document.elementFromPoint(x, y)?.dispatchEvent(clickEvent);
        clickCount++;

        if (clickCount % 500 === 0) {
            console.log(`点击次数: ${clickCount}, 当前时间: ${getCurrentTime()}`);
        }

        // 使用 requestAnimationFrame 进行下一次点击调度
        const scheduleNextClick = (timestamp) => {
            if (!isRunning) return;

            if (Date.now() >= nextClickTime) {
                randomCenterClick();
            } else {
                requestAnimationFrame(scheduleNextClick);
            }
        };

        requestAnimationFrame(scheduleNextClick);

    } catch (error) {
        console.error('点击执行出错：', error);
        isRunning = false;
    }
}

// 开始新一轮执行
function startNewExecution() {
    // 清理之前的定时器
    if (nextExecutionTimer) {
        clearTimeout(nextExecutionTimer);
        nextExecutionTimer = null;
    }
    if (clickTimer) {
        clearTimeout(clickTimer);
        clickTimer = null;
    }

    if (currentExecution >= totalExecutions) {
        console.log(`已完成所有${totalExecutions}轮执行，程序结束。结束时间：${getCurrentTime()}`);
        return;
    }

    currentExecution++;
    console.log(`开始第 ${currentExecution}/${totalExecutions} 轮执行，开始时间：${getCurrentTime()}`);

    // 重置点击次数
    clickCount = 0;
    lastX = null;

    // 随机生成本轮执行时间
    const stopMinutes = minStopMinutes + Math.random() * (maxStopMinutes - minStopMinutes);
    stopTime = Date.now() + stopMinutes * 60 * 1000;

    // 重置运行状态
    isRunning = true;

    // 开始点击
    randomCenterClick();

    // 安排下一轮执行
    if (currentExecution < totalExecutions) {
        const intervalMinutes = minInterval + Math.random() * (maxInterval - minInterval);
        const intervalMs = intervalMinutes * 60 * 1000;
        console.log(`本轮将执行 ${stopMinutes.toFixed(2)} 分钟，${intervalMinutes.toFixed(2)} 分钟后开始下一轮`);
        nextExecutionTimer = setTimeout(startNewExecution, intervalMs);
    }
}

// 停止程序的方法
function stopClicking() {
    isRunning = false;
    if (nextExecutionTimer) {
        clearTimeout(nextExecutionTimer);
        nextExecutionTimer = null;
    }
    if (clickTimer) {
        clearTimeout(clickTimer);
        clickTimer = null;
    }
    // 清理其他可能的引用
    lastX = null;
    console.log(`程序已在第 ${currentExecution} 轮手动停止。停止时间：${getCurrentTime()}`);
}

// 添加窗口大小变化监听
window.addEventListener('resize', () => {
    cachedWidth = null;
    cachedHeight = null;
});

// 启动程序
startNewExecution();
