import { ethers } from "ethers";
import { resolveFromModule } from "../../base/tools/path.js";
import { parseTOMLConfig } from "../../base/tools/common.js";
import logger from "../../base/tools/logger.js";
import { Wallet } from "../../base/evm/wallet.js";


// 加载配置
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

const rpc = "https://sepolia.infura.io/v3/********************************"

class Shield {

  /**
 * 创建 Shield 实例
 * @param {number} index - 账户索引
 * @param {string} privateKey - 钱包私钥
 * @param {string|null} proxy - 代理配置
 * @param {string} rpcUrl - RPC 节点地址
 */
  constructor(index = 0, privateKey = "", proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.index = index;
    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.logger = logger;
    this.config = config;
    this.privateKey = privateKey;
    this.address = this.wallet.getAddress();

    this.USDZ_CONTRACT = '******************************************'; // USDZ contract
    this.cUSDZ_CONTRACT = '******************************************'; // cUSDZ contract
    this.cUSDZ_ABI = [
      "function wrap(address,uint256)",
      "function unwrap(address,uint256)",
      "function balanceOf(address) view returns (uint256)"
    ];
  }

  async #getTokenBalance(tokenAddress) {
    try {
      const erc20Abi = [
        "function balanceOf(address account) view returns (uint256)"
      ];

      const contract = new ethers.Contract(tokenAddress, erc20Abi, this.wallet.provider);
      const balance = await contract.balanceOf(this.address);

      return balance;
    } catch (error) {
      console.error(`Error getting ${tokenAddress} balance:`, error);
      return BigInt(0);
    }
  }

  async #getTokenAllowance(tokenAddress, spender) {
    try {
      const erc20Abi = [
        "function allowance(address owner, address spender) view returns (uint256)"
      ];

      const contract = new ethers.Contract(tokenAddress, erc20Abi, this.wallet.provider);
      const allowance = await contract.allowance(this.address, spender);

      return allowance;
    } catch (error) {
      console.error(`Error getting ${tokenAddress} allowance:`, error);
      return BigInt(0);
    }
  }

  async approveUSDZ() {
    try {
      const erc20Abi = [
        "function approve(address spender, uint256 amount) returns (bool)"
      ];

      const contract = new ethers.Contract(this.USDZ_CONTRACT, erc20Abi, this.wallet);
      const tx = await contract.approve(this.cUSDZ_CONTRACT, ethers.MaxUint256);
      const receipt = await tx.wait();
      if (receipt.status) {
        this.logger.info(`[${this.index}] 授权成功, tx=${receipt.hash}`);
        return true;
      } else {
        this.logger.error(`[${this.index}] 授权失败, tx=${receipt.hash}`);
        return false;
      }
    } catch (error) {
      console.error(`Error approve ${this.USDZ_CONTRACT}:`, error);
      return false;
    }
  }


  async getUSDZBalance() {
    try {
      const balance = await this.#getTokenBalance(this.USDZ_CONTRACT);
      return balance;
    } catch (error) {
      console.error('Error getting USDZ balance:', error);
      return BigInt(0);
    }
  }

  async getcUSDZBalance() {
    try {
      const balance = await this.#getTokenBalance(this.cUSDZ_CONTRACT);
      return balance;
    } catch (error) {
      console.error('Error getting cUSDZ balance:', error);
      return BigInt(0);
    }
  }

  async wrapUSDZ() {
    try {
      const usdzBalance = await this.getUSDZBalance();
      logger.info(`[${this.index}] USDZ余额: ${ethers.formatUnits(usdzBalance, 6)}`);
      if (usdzBalance === 0n) {
        return {
          success: false,
          msg: `USDZ余额为0`,
          error: 'USDZ balance is 0'
        };
      }

      // 余额先转换为整数，如果USDZ余额为小于1，则全部wrap, 否则随机取1到不超过余额的整数wrap
      const usdzBalanceInt = Number(usdzBalance) / 10 ** 6;
      let amountBigInt;
      if (usdzBalanceInt < 1) {
        amountBigInt = usdzBalance;
      } else {
        const amount = Math.floor(Math.random() * usdzBalanceInt) + 1;
        amountBigInt = ethers.parseUnits(amount.toString(), 6);
      }

      logger.info(`[${this.index}] 要wrap的USDZ数量: ${amountBigInt}`);

      // 检查USDZ授权额度
      const usdzAllowance = await this.#getTokenAllowance(this.USDZ_CONTRACT, this.cUSDZ_CONTRACT);
      if (usdzAllowance < amountBigInt) {
        // 授权额度不足，需要先授权
        if (!await this.approveUSDZ()) {
          return {
            success: false,
            msg: `USDZ授权失败`,
            error: `USDZ approve error`
          };
        }
      }
      // 执行wrap
      const cusdz_contract = new ethers.Contract(this.cUSDZ_CONTRACT, this.cUSDZ_ABI, this.wallet)
      const tx = await cusdz_contract.wrap(this.address, amountBigInt)
      const receipt = await tx.wait()
      if (receipt.status) {
        this.logger.success(`[${this.index}] shield 成功, tx=${receipt.hash}`);
        return {
          success: true,
          message: 'shield success',
          txHash: tx.hash
        };
      }
      else {
        this.logger.error(`[${this.index}] shield 失败, tx=${receipt.hash}`)
        return {
          success: false,
          msg: `shield失败, tx: ${receipt.hash}`,
          error: `shield失败, tx: ${receipt.hash}`
        };
      }

    } catch (error) {
      logger.error('Error wrapping USDZ:', error);
      return {
        success: false,
        msg: `shield交易失败, 错误信息: ${error.message}`,
        error: error.message
      };
    }
  }

}

export default Shield;
