import { ethers, formatEther, formatUnits } from 'ethers';
import logger from "../../base/tools/logger.js";
import { resolveFromModule } from "../../base/tools/path.js";
import { parseTOMLConfig } from "../../base/tools/common.js";
import { Wallet } from "../../base/evm/wallet.js";

// 加载配置
const configPath = resolveFromModule(import.meta.url, "./config.toml");
const config = parseTOMLConfig(configPath);

// 合约地址
const USDZ_CONTRACT_ADDRESS = "******************************************";

// USDZ合约ABI
const USDZ_ABI = [
  "function mint(address)",
  "function balanceOf(address) view returns (uint256)"
];

const rpc = "https://sepolia.infura.io/v3/********************************"

/**
 * FaucetUSDZ
 */
class FaucetUSDZ {
  /**
   * 创建 FaucetUSDZ 实例
   * @param {number} index - 账户索引
   * @param {string} privateKey - 钱包私钥
   * @param {string|null} proxy - 代理配置
   * @param {string} rpcUrl - RPC 节点地址
   */
  constructor(index = 0, privateKey = "", proxy = "", rpcUrl = rpc) {
    // 参数验证
    if (!privateKey || typeof privateKey !== "string") {
      throw new Error("必须提供有效的私钥");
    }

    this.index = index;
    this.proxy = proxy || config.PROXY_URL;
    this.wallet = new Wallet(privateKey, rpcUrl);
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.logger = logger;
    this.config = config;
    this.privateKey = privateKey;
    this.address = this.wallet.getAddress();
  }

  // 执行mint
  async executeMint() {
    try {
      logger.info(`[${this.index}] 开始执行mint`);

      const ethBalance = await this.balanceOf(this.address);
      if (ethBalance === 0n) {
        this.logger.warn(`[${this.index}] ETH余额为0，跳过mint`);
        return { success: false, message: "ETH余额为0" };
      }

      this.logger.info(`[${this.index}] ETH余额: ${formatEther(ethBalance)}`);

      // 执行mint
      const usdz_contract = new ethers.Contract(USDZ_CONTRACT_ADDRESS, USDZ_ABI, this.wallet)
      const tx = await usdz_contract.mint(this.address)
      const receipt = await tx.wait()
      if (receipt.status)
        this.logger.info(`[${this.index}] mint成功, tx=%s`, receipt.hash)
      else {
        this.logger.error(`[${this.index}] mint失败, tx=%s`, receipt.hash)
        return false
      }

      const usdzBalanceAfter = await this.balanceOfUSDZ(this.address);
      const usdzBalanceAfterFormatted = formatUnits(usdzBalanceAfter, 6);

      this.logger.success(`[${this.index}] mint成功，区块号: ${receipt.blockNumber}, USDZ余额: ${usdzBalanceAfterFormatted}`);

      return {
        success: true,
        message: 'mint success',
        txHash: tx.hash,
        balance: usdzBalanceAfterFormatted
      };
    } catch (error) {
      this.logger.error(
        `[${this.index}] 交换失败: ${error.message}`);

      if (error.transaction) {
        this.logger.error(
          `[${this.index}] 交易详情: ${error.transaction.hash}`);
      }

      return { success: false, message: error.message };
    }
  }


  async balanceOf(address) {
    return await this.provider.getBalance(address);
  }

  async balanceOfUSDZ(address) {
    const usdzContract = new ethers.Contract(USDZ_CONTRACT_ADDRESS, USDZ_ABI, this.provider);
    return await usdzContract.balanceOf(address);
  }

}

export default FaucetUSDZ;
